import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';

void main() {
  group('Sotuvchi Localization Tests', () {
    testWidgets('Test sotuvchi profile page translations', (WidgetTester tester) async {
      await tester.pumpWidget(
        EasyLocalization(
          supportedLocales: const [Locale('uz'), Locale('ru')],
          path: 'assets/translations',
          fallbackLocale: const Locale('uz'),
          child: MaterialApp(
            localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
            supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
            locale: const Locale('uz'),
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Text(LocaleKeys.profile_personal_info.tr()),
                      Text(LocaleKeys.profile_seller_personal_info_subtitle.tr()),
                      Text(LocaleKeys.profile_language_selection.tr()),
                      Text(LocaleKeys.profile_logout.tr()),
                      Text(LocaleKeys.profile_select_image.tr()),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Uzbek translations for sotuvchi profile
      expect(find.text('Shaxsiy ma\'lumotlar'), findsOneWidget);
      expect(find.text('Profil ma\'lumotlar, biriktirilgan joylar'), findsOneWidget);
      expect(find.text('Tilni tanlang'), findsOneWidget);
      expect(find.text('Chiqish'), findsOneWidget);
      expect(find.text('Rasm tanlash'), findsOneWidget);
    });

    testWidgets('Test sotuvchi empty places translations', (WidgetTester tester) async {
      await tester.pumpWidget(
        EasyLocalization(
          supportedLocales: const [Locale('uz'), Locale('ru')],
          path: 'assets/translations',
          fallbackLocale: const Locale('uz'),
          child: MaterialApp(
            localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
            supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
            locale: const Locale('uz'),
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Text(LocaleKeys.navigation_empty_places.tr()),
                      Text(LocaleKeys.places_no_empty_places.tr()),
                      Text(LocaleKeys.places_no_empty_places_subtitle.tr()),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Uzbek translations for empty places
      expect(find.text('Bo\'sh joylar'), findsOneWidget);
      expect(find.text('Bo\'sh joylar topilmadi'), findsOneWidget);
      expect(find.text('Hozircha ijara uchun bo\'sh joylar mavjud emas'), findsOneWidget);
    });

    testWidgets('Test Russian translations for sotuvchi', (WidgetTester tester) async {
      await tester.pumpWidget(
        EasyLocalization(
          supportedLocales: const [Locale('uz'), Locale('ru')],
          path: 'assets/translations',
          fallbackLocale: const Locale('uz'),
          child: MaterialApp(
            localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
            supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
            locale: const Locale('ru'),
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Text(LocaleKeys.profile_personal_info.tr()),
                      Text(LocaleKeys.navigation_payment_history.tr()),
                      Text(LocaleKeys.navigation_empty_places.tr()),
                      Text(LocaleKeys.places_no_empty_places.tr()),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Russian translations
      expect(find.text('Личная информация'), findsOneWidget);
      expect(find.text('История платежей'), findsOneWidget);
      expect(find.text('Свободные места'), findsOneWidget);
      expect(find.text('Свободные места не найдены'), findsOneWidget);
    });
  });
}
