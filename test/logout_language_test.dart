import 'package:flutter_test/flutter_test.dart';
import 'package:click_bazaar/core/services/language_service.dart';

void main() {
  group('Language Service Tests', () {

    test('LanguageService getLanguageDisplayName should return correct names', () {
      // Test Uzbek
      expect(LanguageService.getLanguageDisplayName('uz'), equals('O\'zbek'));

      // Test Russian
      expect(LanguageService.getLanguageDisplayName('ru'), equals('Русский'));

      // Test default fallback
      expect(LanguageService.getLanguageDisplayName('unknown'), equals('O\'zbek'));
    });

    test('LanguageService getSupportedLanguages should return correct list', () {
      final languages = LanguageService.getSupportedLanguages();

      expect(languages, hasLength(2));
      expect(languages[0]['code'], equals('uz'));
      expect(languages[0]['name'], equals('O\'zbek'));
      expect(languages[1]['code'], equals('ru'));
      expect(languages[1]['name'], equals('Русский'));
    });
  });
}
