import 'package:flutter_test/flutter_test.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/plan_statistics_model.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/place_statistics_model.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/payment_statistics_model.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/presentation/bloc/naz_statistics_bloc/naz_statistics_bloc.dart';
void main() {
  group('Naz Statistics Models', () {
    group('PlanStatisticsModel', () {
      test('should create from JSON correctly', () {
        final json = {
          'totalPlan': 100,
          'tushum': 80,
          'click': 30,
          'naqt': 25,
          'terminal': 25,
          'qarzdorlik': 20,
        };

        final model = PlanStatisticsModel.fromJson(json);

        expect(model.rejda, 100);
        expect(model.tushgan, 80);
        expect(model.clickO<PERSON><PERSON><PERSON>, 30);
        expect(model.naqdPul, 25);
        expect(model.terminal<PERSON><PERSON><PERSON><PERSON>, 25);
        expect(model.qar<PERSON><PERSON>, 20);
      });

      test('should handle missing fields with default values', () {
        final json = <String, dynamic>{};
        final model = PlanStatisticsModel.fromJson(json);

        expect(model.rejda, 0);
        expect(model.tushgan, 0);
        expect(model.clickOrqali, 0);
        expect(model.naqdPul, 0);
        expect(model.terminalOrqali, 0);
        expect(model.qarzdalik, 0);
      });

      test('should convert to JSON correctly', () {
        const model = PlanStatisticsModel(
          rejda: 100,
          tushgan: 80,
          clickOrqali: 30,
          naqdPul: 25,
          terminalOrqali: 25,
          qarzdalik: 20,
        );

        final json = model.toJson();

        expect(json['totalPlan'], 100);
        expect(json['tushum'], 80);
        expect(json['click'], 30);
        expect(json['naqt'], 25);
        expect(json['terminal'], 25);
        expect(json['qarzdorlik'], 20);
      });

      test('should create empty instance', () {
        final model = PlanStatisticsModel.empty();

        expect(model.rejda, 0);
        expect(model.tushgan, 0);
        expect(model.clickOrqali, 0);
        expect(model.naqdPul, 0);
        expect(model.terminalOrqali, 0);
        expect(model.qarzdalik, 0);
      });
    });

    group('PlaceStatisticsModel', () {
      test('should create from JSON correctly and calculate total', () {
        final json = {
          'belgilanmagan': 5,
          'free': 8,
          'qarzdor': 3,
          'tolangan': 10,
        };

        final model = PlaceStatisticsModel.fromJson(json);

        expect(model.belgilanmaganCount, 5);
        expect(model.bosh, 8);
        expect(model.tolanmagan, 3);
        expect(model.tolangan, 10);
        expect(model.totalCount, 26); // 5 + 8 + 3 + 10
      });

      test('should handle missing fields with default values', () {
        final json = <String, dynamic>{};
        final model = PlaceStatisticsModel.fromJson(json);

        expect(model.belgilanmaganCount, 0);
        expect(model.bosh, 0);
        expect(model.tolanmagan, 0);
        expect(model.tolangan, 0);
        expect(model.totalCount, 0);
      });

      test('should convert to JSON correctly', () {
        const model = PlaceStatisticsModel(
          belgilanmaganCount: 5,
          bosh: 8,
          tolanmagan: 3,
          tolangan: 10,
          totalCount: 26,
        );

        final json = model.toJson();

        expect(json['belgilanmagan'], 5);
        expect(json['free'], 8);
        expect(json['qarzdor'], 3);
        expect(json['tolangan'], 10);
      });
    });

    group('PaymentStatisticsModel', () {
      test('should create from JSON correctly', () {
        final json = {
          'topshirilgan': 50,
          'topshirilmagan': 30,
        };

        final model = PaymentStatisticsModel.fromJson(json);

        expect(model.topshirilgan, 50);
        expect(model.topshirilmagan, 30);
        expect(model.total, 80); // 50 + 30
      });

      test('should handle missing fields with default values', () {
        final json = <String, dynamic>{};
        final model = PaymentStatisticsModel.fromJson(json);

        expect(model.topshirilgan, 0);
        expect(model.topshirilmagan, 0);
        expect(model.total, 0);
      });

      test('should convert to JSON correctly', () {
        const model = PaymentStatisticsModel(
          topshirilgan: 50,
          topshirilmagan: 30,
        );

        final json = model.toJson();

        expect(json['topshirilgan'], 50);
        expect(json['topshirilmagan'], 30);
      });

      test('should create empty instance', () {
        final model = PaymentStatisticsModel.empty();

        expect(model.topshirilgan, 0);
        expect(model.topshirilmagan, 0);
        expect(model.total, 0);
      });
    });

    group('NazStatisticsState', () {
      test('should have correct initial values', () {
        const state = NazStatisticsState();

        expect(state.status, NazStatisticsStatus.initial);
        expect(state.message, '');
        expect(state.planStatistics, null);
        expect(state.placeStatistics, null);
        expect(state.paymentStatistics, null);
        expect(state.currentDate, null);
      });

      test('hasAllStatistics returns true when all statistics are present', () {
        final state = NazStatisticsState(
          planStatistics: PlanStatisticsModel.empty(),
          placeStatistics: PlaceStatisticsModel.empty(),
          paymentStatistics: PaymentStatisticsModel.empty(),
        );
        expect(state.hasAllStatistics, true);
      });

      test('hasAllStatistics returns false when any statistics are missing', () {
        final state = NazStatisticsState(
          planStatistics: PlanStatisticsModel.empty(),
          placeStatistics: PlaceStatisticsModel.empty(),
        );
        expect(state.hasAllStatistics, false);
      });

      test('hasAnyStatistics returns true when at least one statistic is present', () {
        final state = NazStatisticsState(
          planStatistics: PlanStatisticsModel.empty(),
        );
        expect(state.hasAnyStatistics, true);
      });

      test('isLoading returns true when status is loading', () {
        const state = NazStatisticsState(status: NazStatisticsStatus.loading);
        expect(state.isLoading, true);
      });

      test('isSuccess returns true when status is success', () {
        const state = NazStatisticsState(status: NazStatisticsStatus.success);
        expect(state.isSuccess, true);
      });

      test('isFailure returns true when status is failure', () {
        const state = NazStatisticsState(status: NazStatisticsStatus.failure);
        expect(state.isFailure, true);
      });

      test('isInitial returns true when status is initial', () {
        const state = NazStatisticsState(status: NazStatisticsStatus.initial);
        expect(state.isInitial, true);
      });

      test('copyWith should update only specified fields', () {
        const originalState = NazStatisticsState(
          status: NazStatisticsStatus.initial,
          message: 'original',
        );

        final newState = originalState.copyWith(
          status: NazStatisticsStatus.loading,
        );

        expect(newState.status, NazStatisticsStatus.loading);
        expect(newState.message, 'original'); // Should remain unchanged
      });
    });
  });
}
