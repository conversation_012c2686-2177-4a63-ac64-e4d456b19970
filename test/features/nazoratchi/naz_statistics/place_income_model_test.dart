import 'package:flutter_test/flutter_test.dart';
import 'package:click_bazaar/features/nazoratchi/naz_statistics/data/models/place_income_model.dart';

void main() {
  group('PlaceIncomeModel', () {
    test('should create from JSON correctly', () {
      final json = {
        'placeNumber': '35',
        'amount': 16000,
      };

      final model = PlaceIncomeModel.fromJson(json);

      expect(model.placeNumber, '35');
      expect(model.amount, 16000);
    });

    test('should handle null values in JSON', () {
      final json = <String, dynamic>{};

      final model = PlaceIncomeModel.fromJson(json);

      expect(model.placeNumber, '');
      expect(model.amount, 0);
    });

    test('should convert to JSON correctly', () {
      const model = PlaceIncomeModel(
        placeNumber: '24',
        amount: 24000,
      );

      final json = model.toJson();

      expect(json['placeNumber'], '24');
      expect(json['amount'], 24000);
    });

    test('should create empty instance', () {
      final model = PlaceIncomeModel.empty();

      expect(model.placeNumber, '');
      expect(model.amount, 0);
    });

    test('should copy with new values', () {
      const original = PlaceIncomeModel(
        placeNumber: '35',
        amount: 16000,
      );

      final copied = original.copyWith(amount: 20000);

      expect(copied.placeNumber, '35');
      expect(copied.amount, 20000);
    });
  });

  group('PlaceIncomeResponse', () {
    test('should create from JSON correctly', () {
      final json = {
        'places': [
          {'placeNumber': '35', 'amount': 16000},
          {'placeNumber': '24', 'amount': 24000},
        ],
        'totalAmount': 40000,
      };

      final response = PlaceIncomeResponse.fromJson(json);

      expect(response.places.length, 2);
      expect(response.places[0].placeNumber, '35');
      expect(response.places[0].amount, 16000);
      expect(response.places[1].placeNumber, '24');
      expect(response.places[1].amount, 24000);
      expect(response.totalAmount, 40000);
    });

    test('should handle empty places list', () {
      final json = {
        'places': <Map<String, dynamic>>[],
        'totalAmount': 0,
      };

      final response = PlaceIncomeResponse.fromJson(json);

      expect(response.places.isEmpty, true);
      expect(response.totalAmount, 0);
    });

    test('should create empty instance', () {
      final response = PlaceIncomeResponse.empty();

      expect(response.places.isEmpty, true);
      expect(response.totalAmount, 0);
    });

    test('should convert to JSON correctly', () {
      const response = PlaceIncomeResponse(
        places: [
          PlaceIncomeModel(placeNumber: '35', amount: 16000),
          PlaceIncomeModel(placeNumber: '24', amount: 24000),
        ],
        totalAmount: 40000,
      );

      final json = response.toJson();

      expect(json['places'], isA<List>());
      expect((json['places'] as List).length, 2);
      expect(json['totalAmount'], 40000);
    });
  });
}
