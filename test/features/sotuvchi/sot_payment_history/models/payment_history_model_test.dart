import 'package:flutter_test/flutter_test.dart';
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/models/payment_history_model.dart';

void main() {
  group('PaymentHistory Model Tests', () {
    test('should create PaymentHistory from JSON correctly', () {
      // Arrange
      final json = {
        '_id': 'payment_123',
        'price': 120000,
        'date': '2025-01-15 14:30',
        'paymentType': 1,
        'places': [
          {'_id': 'place_1', 'title': 45},
          {'_id': 'place_2', 'title': 46},
        ]
      };

      // Act
      final paymentHistory = PaymentHistory.fromJson(json);

      // Assert
      expect(paymentHistory.id, 'payment_123');
      expect(paymentHistory.price, 120000.0);
      expect(paymentHistory.date, '2025-01-15 14:30');
      expect(paymentHistory.paymentType, 1);
      expect(paymentHistory.places.length, 2);
      expect(paymentHistory.places[0].title, 45);
      expect(paymentHistory.places[1].title, 46);
    });

    test('should format price with comma separators correctly', () {
      // Arrange
      final paymentHistory = PaymentHistory(
        id: 'test',
        price: 1234567.0,
        date: '2025-01-15',
        paymentType: 1,
        places: [],
      );

      // Act & Assert
      expect(paymentHistory.displayPrice, '1,234,567');
    });

    test('should format date correctly', () {
      // Arrange
      final paymentHistory = PaymentHistory(
        id: 'test',
        price: 0,
        date: '2025-01-15 14:30',
        paymentType: 1,
        places: [],
      );

      // Act & Assert
      expect(paymentHistory.displayDate, '15.01.2025');
    });

    test('should display place numbers as comma-separated string with # prefix', () {
      // Arrange
      final places = [
        PaymentPlace(id: 'place_1', title: 45),
        PaymentPlace(id: 'place_2', title: 46),
        PaymentPlace(id: 'place_3', title: 47),
      ];

      final paymentHistory = PaymentHistory(
        id: 'test',
        price: 0,
        date: '2025-01-15',
        paymentType: 1,
        places: places,
      );

      // Act & Assert
      expect(paymentHistory.displayPlaceNumbers, '#45, #46, #47');
    });

    test('should handle empty places array', () {
      // Arrange
      final paymentHistory = PaymentHistory(
        id: 'test',
        price: 0,
        date: '2025-01-15',
        paymentType: 1,
        places: [],
      );

      // Act & Assert
      expect(paymentHistory.displayPlaceNumbers, '-');
    });

    test('should handle zero price correctly', () {
      // Arrange
      final paymentHistory = PaymentHistory(
        id: 'test',
        price: 0,
        date: '2025-01-15',
        paymentType: 1,
        places: [],
      );

      // Act & Assert
      expect(paymentHistory.displayPrice, '0');
    });
  });

  group('PaymentHistoryResponse Model Tests', () {
    test('should create PaymentHistoryResponse from JSON correctly', () {
      // Arrange
      final json = {
        'docs': [
          {
            '_id': 'payment_123',
            'price': 120000,
            'date': '2025-01-15 14:30',
            'paymentType': 1,
            'places': [
              {'_id': 'place_1', 'title': 45}
            ]
          }
        ],
        'totalDocs': 100,
        'limit': 20,
        'totalPages': 5,
        'page': 1,
        'pagingCounter': 1,
        'hasPrevPage': false,
        'hasNextPage': true,
        'prevPage': null,
        'nextPage': 2
      };

      // Act
      final response = PaymentHistoryResponse.fromJson(json);

      // Assert
      expect(response.docs.length, 1);
      expect(response.totalDocs, 100);
      expect(response.limit, 20);
      expect(response.totalPages, 5);
      expect(response.page, 1);
      expect(response.hasNextPage, true);
      expect(response.hasPrevPage, false);
      expect(response.nextPage, 2);
      expect(response.prevPage, null);
    });
  });
}
