import 'package:flutter_test/flutter_test.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/models/free_place_model.dart';

void main() {
  group('Free Place Model Tests', () {
    test('FreePlaceRequest should create correct form data', () {
      // Arrange
      const request = FreePlaceRequest(
        file: '/path/to/image.jpg',
        place: '686e2f7a07dd9c17ea4bfc97',
        desc: 'Test description',
      );

      // Act
      final formData = request.toFormData();

      // Assert
      expect(formData['file'], '/path/to/image.jpg');
      expect(formData['place'], '686e2f7a07dd9c17ea4bfc97');
      expect(formData['desc'], 'Test description');
    });

    test('FreePlaceResponse should create success response correctly', () {
      // Act
      final response = FreePlaceResponse.success(
        message: 'Success message',
        data: {'id': '123'},
      );

      // Assert
      expect(response.success, true);
      expect(response.message, 'Success message');
      expect(response.data, {'id': '123'});
    });

    test('FreePlaceResponse should create error response correctly', () {
      // Act
      final response = FreePlaceResponse.error('Error message');

      // Assert
      expect(response.success, false);
      expect(response.message, 'Error message');
      expect(response.data, null);
    });

    test(
        'FreePlaceResponse should handle API response format {message: "success"}',
        () {
      // Arrange - simulate the actual API response format
      final apiResponse = {'message': 'success'};

      // Act - this should not throw an error now
      final response = FreePlaceResponse.success(
        message: 'Bo\'sh rasta muvaffaqiyatli belgilandi',
        data: apiResponse,
      );

      // Assert
      expect(response.success, true);
      expect(response.message, 'Bo\'sh rasta muvaffaqiyatli belgilandi');
      expect(response.data, apiResponse);
    });
  });
}
