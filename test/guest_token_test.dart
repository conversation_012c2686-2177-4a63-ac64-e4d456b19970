import 'package:flutter_test/flutter_test.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/utils/api_path.dart';

void main() {
  group('Guest Token Debug Tests', () {
    test('Check all API endpoints configuration', () {
      print('=== API ENDPOINTS DEBUG ===');
      print('GUEST_TOKEN: "$GUEST_TOKEN"');
      print('API_BASE_URL: "$API_BASE_URL"');
      print('RASTALAR_ENDPOINT: "$RASTALAR_HARD_ENDPOINT"');
      print('Full Rastalar URL: "$API_BASE_URL$RASTALAR_HARD_ENDPOINT"');
      print('');
      print('Other API Paths:');
      print('Pavilion Path: "${ApiPath.pavilionPath}"');
      print('Full Pavilion URL: "${ApiPath.baseUrl}${ApiPath.pavilionPath}"');
      print('Block Path: "mobile/place/block"');
      print('Full Block URL: "${ApiPath.baseUrl}mobile/place/block"');
      print('');
      print('Token length: ${GUEST_TOKEN.length}');
      print('============================');

      // Basic validations
      expect(GUEST_TOKEN, isNotEmpty);
      expect(API_BASE_URL, isNotEmpty);
      expect(RASTALAR_HARD_ENDPOINT, isNotEmpty);
      expect(ApiPath.pavilionPath, isNotEmpty);
      expect(ApiPath.baseUrl, isNotEmpty);
    });
  });
}
