import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:easy_localization/easy_localization.dart';
import '../lib/translations/locale_keys.g.dart';

void main() {
  group('Face Control Localization Tests', () {
    testWidgets('Test face control Uzbek translations', (WidgetTester tester) async {
      await tester.pumpWidget(
        EasyLocalization(
          supportedLocales: const [Locale('uz'), Locale('ru')],
          path: 'assets/translations',
          fallbackLocale: const Locale('uz'),
          child: MaterialApp(
            localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
            supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
            locale: const Locale('uz'),
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Text(LocaleKeys.face_control_face_control_title.tr()),
                      Text(LocaleKeys.face_control_uploaded.tr()),
                      Text(LocaleKeys.face_control_recognised.tr()),
                      Text(LocaleKeys.face_control_face_match.tr()),
                      Text(LocaleKeys.face_control_liveness.tr()),
                      Text(LocaleKeys.face_control_analyzing.tr()),
                      Text(LocaleKeys.face_control_error_uploading.tr()),
                      Text(LocaleKeys.face_control_leader_not_confirmed.tr()),
                      Text(LocaleKeys.face_control_picture_uploaded.tr()),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Uzbek translations
      expect(find.text('Yuz nazorati'), findsOneWidget);
      expect(find.text('Yuklandi'), findsOneWidget);
      expect(find.text('Tanildi'), findsOneWidget);
      expect(find.text('Yuz mos kelishi'), findsOneWidget);
      expect(find.text('Jonlilik'), findsOneWidget);
      expect(find.text('Tahlil qilinmoqda...'), findsOneWidget);
      expect(find.text('Yuklashda xatolik'), findsOneWidget);
      expect(find.text('Admin tasdiqlashi kerak'), findsOneWidget);
      expect(find.text('Rasm yuklandi'), findsOneWidget);
    });

    testWidgets('Test face control Russian translations', (WidgetTester tester) async {
      await tester.pumpWidget(
        EasyLocalization(
          supportedLocales: const [Locale('uz'), Locale('ru')],
          path: 'assets/translations',
          fallbackLocale: const Locale('uz'),
          child: MaterialApp(
            localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
            supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
            locale: const Locale('ru'),
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Text(LocaleKeys.face_control_face_control_title.tr()),
                      Text(LocaleKeys.face_control_uploaded.tr()),
                      Text(LocaleKeys.face_control_recognised.tr()),
                      Text(LocaleKeys.face_control_face_match.tr()),
                      Text(LocaleKeys.face_control_liveness.tr()),
                      Text(LocaleKeys.face_control_analyzing.tr()),
                      Text(LocaleKeys.face_control_error_uploading.tr()),
                      Text(LocaleKeys.face_control_leader_not_confirmed.tr()),
                      Text(LocaleKeys.face_control_picture_uploaded.tr()),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Russian translations
      expect(find.text('Контроль лица'), findsOneWidget);
      expect(find.text('Загружено'), findsOneWidget);
      expect(find.text('Распознано'), findsOneWidget);
      expect(find.text('Совпадение лица'), findsOneWidget);
      expect(find.text('Живость'), findsOneWidget);
      expect(find.text('Анализируется...'), findsOneWidget);
      expect(find.text('Ошибка при загрузке'), findsOneWidget);
      expect(find.text('Требуется подтверждение администратора'), findsOneWidget);
      expect(find.text('Изображение загружено'), findsOneWidget);
    });
  });
}
