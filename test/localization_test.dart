import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:click_bazaar/core/services/dio_error_handler.dart';
import 'package:click_bazaar/core/services/image_error_handler.dart';
import 'package:dio/dio.dart';

void main() {
  group('Localization Tests', () {
    testWidgets('Test Uzbek translations', (WidgetTester tester) async {
      await tester.pumpWidget(
        EasyLocalization(
          supportedLocales: const [Locale('uz'), Locale('ru')],
          path: 'assets/translations',
          fallbackLocale: const Locale('uz'),
          child: MaterialApp(
            localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
            supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
            locale: const Locale('uz'),
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Text(LocaleKeys.auth_login_title.tr()),
                      Text(LocaleKeys.navigation_home.tr()),
                      Text(LocaleKeys.profile_personal_info.tr()),
                      Text(LocaleKeys.errors_connection_timeout.tr()),
                      Text(LocaleKeys.image_errors_loading_error.tr()),
                      Text(LocaleKeys.dialogs_empty_square_status_empty.tr()),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Uzbek translations
      expect(find.text('Ilovaga kirish'), findsOneWidget);
      expect(find.text('Asosiy'), findsOneWidget);
      expect(find.text('Shaxsiy ma\'lumotlar'), findsOneWidget);
      expect(find.text('Ulanish vaqti tugadi. Iltimos, qayta urinib ko\'ring.'), findsOneWidget);
      expect(find.text('Rasm yuklashda xatolik'), findsOneWidget);
      expect(find.text('Bo\'sh'), findsOneWidget);
    });

    testWidgets('Test Russian translations', (WidgetTester tester) async {
      await tester.pumpWidget(
        EasyLocalization(
          supportedLocales: const [Locale('uz'), Locale('ru')],
          path: 'assets/translations',
          fallbackLocale: const Locale('uz'),
          child: MaterialApp(
            localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
            supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
            locale: const Locale('ru'),
            home: Builder(
              builder: (context) {
                return Scaffold(
                  body: Column(
                    children: [
                      Text(LocaleKeys.auth_login_title.tr()),
                      Text(LocaleKeys.navigation_home.tr()),
                      Text(LocaleKeys.profile_personal_info.tr()),
                      Text(LocaleKeys.errors_connection_timeout.tr()),
                      Text(LocaleKeys.image_errors_loading_error.tr()),
                      Text(LocaleKeys.dialogs_empty_square_status_empty.tr()),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Russian translations
      expect(find.text('Вход в приложение'), findsOneWidget);
      expect(find.text('Главная'), findsOneWidget);
      expect(find.text('Личная информация'), findsOneWidget);
      expect(find.text('Время соединения истекло. Пожалуйста, попробуйте снова.'), findsOneWidget);
      expect(find.text('Ошибка загрузки изображения'), findsOneWidget);
      expect(find.text('Пусто'), findsOneWidget);
    });

    group('Error Handler Localization Tests', () {
      testWidgets('Test DioErrorHandler localization', (WidgetTester tester) async {
        await tester.pumpWidget(
          EasyLocalization(
            supportedLocales: const [Locale('uz'), Locale('ru')],
            path: 'assets/translations',
            fallbackLocale: const Locale('uz'),
            child: MaterialApp(
              localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
              supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
              locale: const Locale('uz'),
              home: Builder(
                builder: (context) {
                  // Test various Dio error types
                  final connectionTimeoutError = DioException(
                    requestOptions: RequestOptions(path: '/test'),
                    type: DioExceptionType.connectionTimeout,
                  );

                  final badResponseError = DioException(
                    requestOptions: RequestOptions(path: '/test'),
                    type: DioExceptionType.badResponse,
                    response: Response(
                      requestOptions: RequestOptions(path: '/test'),
                      statusCode: 404,
                    ),
                  );

                  return Scaffold(
                    body: Column(
                      children: [
                        Text(DioErrorHandler.handleDioError(connectionTimeoutError)),
                        Text(DioErrorHandler.handleDioError(badResponseError)),
                        Text(DioErrorHandler.handleGenericError('Test error')),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify Uzbek error messages
        expect(find.text('Ulanish vaqti tugadi. Iltimos, qayta urinib ko\'ring.'), findsOneWidget);
        expect(find.text('Ma\'lumot topilmadi.'), findsOneWidget);
        expect(find.text('Kutilmagan xatolik: Test error'), findsOneWidget);
      });

      testWidgets('Test ImageErrorHandler localization', (WidgetTester tester) async {
        await tester.pumpWidget(
          EasyLocalization(
            supportedLocales: const [Locale('uz'), Locale('ru')],
            path: 'assets/translations',
            fallbackLocale: const Locale('uz'),
            child: MaterialApp(
              localizationsDelegates: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.delegates,
              supportedLocales: EasyLocalization.of(tester.element(find.byType(MaterialApp)))!.supportedLocales,
              locale: const Locale('uz'),
              home: Builder(
                builder: (context) {
                  return Scaffold(
                    body: Column(
                      children: [
                        Text(ImageErrorHandler.getUserFriendlyMessage(Exception('Network error'))),
                        Text(ImageErrorHandler.getUserFriendlyMessage(Exception('404'))),
                        Text(ImageErrorHandler.getUserFriendlyMessage(Exception('corrupted'))),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify image error messages
        expect(find.text('Rasm yuklashda xatolik'), findsOneWidget);
      });
    });
  });
}
