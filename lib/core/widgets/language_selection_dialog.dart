import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../services/language_service.dart';
import '../../translations/locale_keys.g.dart';

/// Universal language selection dialog
class LanguageSelectionDialog extends StatefulWidget {
  final Function(String)? onLanguageChanged;

  const LanguageSelectionDialog({
    super.key,
    this.onLanguageChanged,
  });

  @override
  State<LanguageSelectionDialog> createState() => _LanguageSelectionDialogState();
}

class _LanguageSelectionDialogState extends State<LanguageSelectionDialog> {
  String _tempSelectedLanguage = LanguageService.getCurrentLanguage();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 280.w,
        decoration: BoxDecoration(
          color: AppColors.cCardsColor,
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.fromLTRB(24.w, 20.h, 16.w, 16.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    LocaleKeys.language_dialog_title.tr(),
                    style: AppTextStyles.titleMedium.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 18.sp,
                    ),
                  ),
                ],
              ),
            ),
            
            // Language options
            Container(
              padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
              child: Column(
                children: [
                  ...LanguageService.getSupportedLanguages().map(
                    (language) => _buildLanguageOption(
                      languageCode: language['code']!,
                      languageName: language['name']!,
                      isSelected: _tempSelectedLanguage == language['code'],
                    ),
                  ),
                  
                  SizedBox(height: 24.h),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(vertical: 12.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                              side: BorderSide(color: AppColors.cGrayBorderColor),
                            ),
                          ),
                          child: Text(
                            LocaleKeys.language_cancel.tr(),
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.cTextGrayColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      
                      SizedBox(width: 12.w),
                      
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () async {
                            await LanguageService.setLanguage(context, _tempSelectedLanguage);
                            widget.onLanguageChanged?.call(_tempSelectedLanguage);
                            if (mounted) {
                              Navigator.of(context).pop();
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.cFirstColor,
                            padding: EdgeInsets.symmetric(vertical: 12.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                          ),
                          child: Text(
                            LocaleKeys.language_save.tr(),
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageOption({
    required String languageCode,
    required String languageName,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _tempSelectedLanguage = languageCode;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.cFirstColor.withOpacity(0.1) : Colors.transparent,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected ? AppColors.cFirstColor : AppColors.cGrayBorderColor,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 20.w,
              height: 20.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: isSelected ? AppColors.cFirstColor : AppColors.cGrayBorderColor,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 10.w,
                        height: 10.h,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: AppColors.cFirstColor,
                        ),
                      ),
                    )
                  : null,
            ),
            
            SizedBox(width: 12.w),
            
            Text(
              languageName,
              style: AppTextStyles.bodyMedium.copyWith(
                color: isSelected ? AppColors.cFirstColor : AppColors.white,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
