import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CircleButton extends StatelessWidget {
  final String svgPath;
  final VoidCallback? onPressed;
  final double iconHeight;
  final double iconWidth;

  const CircleButton(
      {super.key,
      required this.svgPath,
      this.onPressed,
      this.iconHeight = 20,
      this.iconWidth = 20});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 10),
      child: ClipOval(
        child: Material(
          color: Colors.transparent,
          child: IconButton(
            iconSize: 40.w,
            padding: EdgeInsets.zero,
            onPressed: onPressed,
            icon: SvgPicture.asset(
              svgPath,
              height: iconHeight,
              width: iconWidth,
              colorFilter: ColorFilter.mode(
                Theme.of(context).colorScheme.onSurface,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
