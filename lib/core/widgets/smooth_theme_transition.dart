import 'package:flutter/material.dart';
import '../theme/theme_constants.dart';

/// A widget that provides smooth theme transitions with custom animations
class SmoothThemeTransition extends StatefulWidget {
  final Widget child;
  final ThemeData theme;
  final Duration? duration;
  final Curve? curve;

  const SmoothThemeTransition({
    Key? key,
    required this.child,
    required this.theme,
    this.duration,
    this.curve,
  }) : super(key: key);

  @override
  State<SmoothThemeTransition> createState() => _SmoothThemeTransitionState();
}

class _SmoothThemeTransitionState extends State<SmoothThemeTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  ThemeData? _previousTheme;
  ThemeData? _currentTheme;

  @override
  void initState() {
    super.initState();
    _currentTheme = widget.theme;
    
    _controller = AnimationController(
      duration: widget.duration ?? ThemeConstants.themeTransitionDuration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve ?? Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.98,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: widget.curve ?? Curves.easeInOut,
    ));

    _controller.forward();
  }

  @override
  void didUpdateWidget(SmoothThemeTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.theme != widget.theme) {
      _previousTheme = _currentTheme;
      _currentTheme = widget.theme;
      
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return AnimatedTheme(
          data: _currentTheme!,
          duration: widget.duration ?? ThemeConstants.themeTransitionDuration,
          curve: widget.curve ?? Curves.easeInOut,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: AnimatedOpacity(
              opacity: _fadeAnimation.value,
              duration: widget.duration ?? ThemeConstants.themeTransitionDuration,
              curve: widget.curve ?? Curves.easeInOut,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

/// A widget that provides a ripple effect during theme transitions
class ThemeRippleTransition extends StatefulWidget {
  final Widget child;
  final ThemeData theme;
  final Offset? center;
  final Duration? duration;

  const ThemeRippleTransition({
    Key? key,
    required this.child,
    required this.theme,
    this.center,
    this.duration,
  }) : super(key: key);

  @override
  State<ThemeRippleTransition> createState() => _ThemeRippleTransitionState();
}

class _ThemeRippleTransitionState extends State<ThemeRippleTransition>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rippleAnimation;
  ThemeData? _previousTheme;
  ThemeData? _currentTheme;
  bool _isTransitioning = false;

  @override
  void initState() {
    super.initState();
    _currentTheme = widget.theme;
    
    _controller = AnimationController(
      duration: widget.duration ?? const Duration(milliseconds: 600),
      vsync: this,
    );

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(ThemeRippleTransition oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.theme != widget.theme) {
      _previousTheme = _currentTheme;
      _currentTheme = widget.theme;
      _isTransitioning = true;
      
      _controller.reset();
      _controller.forward().then((_) {
        setState(() {
          _isTransitioning = false;
        });
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedTheme(
      data: _currentTheme!,
      duration: widget.duration ?? const Duration(milliseconds: 600),
      child: Stack(
        children: [
          widget.child,
          if (_isTransitioning)
            AnimatedBuilder(
              animation: _rippleAnimation,
              builder: (context, child) {
                return CustomPaint(
                  painter: RipplePainter(
                    progress: _rippleAnimation.value,
                    center: widget.center ?? const Offset(0.5, 0.5),
                    color: _currentTheme!.colorScheme.surface,
                  ),
                  size: Size.infinite,
                );
              },
            ),
        ],
      ),
    );
  }
}

/// Custom painter for the ripple effect
class RipplePainter extends CustomPainter {
  final double progress;
  final Offset center;
  final Color color;

  RipplePainter({
    required this.progress,
    required this.center,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final centerPoint = Offset(
      size.width * center.dx,
      size.height * center.dy,
    );

    final maxRadius = (size.width > size.height ? size.width : size.height) * 1.5;
    final radius = maxRadius * progress;

    canvas.drawCircle(centerPoint, radius, paint);
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.center != center ||
           oldDelegate.color != color;
  }
}

/// Enhanced theme transition with multiple animation effects
class EnhancedThemeTransition extends StatefulWidget {
  final Widget child;
  final ThemeData theme;
  final Duration? duration;
  final bool useRippleEffect;
  final bool useScaleEffect;
  final bool useFadeEffect;

  const EnhancedThemeTransition({
    Key? key,
    required this.child,
    required this.theme,
    this.duration,
    this.useRippleEffect = false,
    this.useScaleEffect = true,
    this.useFadeEffect = true,
  }) : super(key: key);

  @override
  State<EnhancedThemeTransition> createState() => _EnhancedThemeTransitionState();
}

class _EnhancedThemeTransitionState extends State<EnhancedThemeTransition> {
  @override
  Widget build(BuildContext context) {
    Widget child = widget.child;

    if (widget.useRippleEffect) {
      child = ThemeRippleTransition(
        theme: widget.theme,
        duration: widget.duration,
        child: child,
      );
    } else if (widget.useScaleEffect || widget.useFadeEffect) {
      child = SmoothThemeTransition(
        theme: widget.theme,
        duration: widget.duration,
        child: child,
      );
    } else {
      child = AnimatedTheme(
        data: widget.theme,
        duration: widget.duration ?? ThemeConstants.themeTransitionDuration,
        child: child,
      );
    }

    return child;
  }
}
