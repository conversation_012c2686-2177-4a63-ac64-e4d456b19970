import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../extensions/context_extensions.dart';

/// Custom app dialog widget that matches the app's design system
class CustomAppDialog extends StatelessWidget {
  final Widget child;
  final bool isCloseVisible;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final double? maxWidth;
  final double? maxHeight;
  final VoidCallback? onClose;

  const CustomAppDialog({
    super.key,
    required this.child,
    this.isCloseVisible = true,
    this.backgroundColor,
    this.padding,
    this.borderRadius,
    this.maxWidth,
    this.maxHeight,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = context.isTablet;
    
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Stack(
        children: [
          Container(
            constraints: BoxConstraints(
              maxWidth: maxWidth ?? (isTablet ? 400.w : screenSize.width * 0.9),
              maxHeight: maxHeight ?? screenSize.height * 0.8,
            ),
            padding: padding ?? EdgeInsets.all(24.w),
            decoration: BoxDecoration(
              color: backgroundColor ?? Theme.of(context).colorScheme.surface,
              borderRadius: borderRadius ?? BorderRadius.circular(16.r),
            ),
            child: child,
          ),
          
          // Close button
          if (isCloseVisible)
            Positioned(
              top: 8.h,
              right: 8.w,
              child: GestureDetector(
                onTap: onClose ?? () => Navigator.of(context).pop(),
                child: Container(
                  width: 32.w,
                  height: 32.h,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 18.w,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Permission instruction dialog that matches app style
class PermissionInstructionDialog extends StatelessWidget {
  final String message;
  final bool isCloseVisible;
  final VoidCallback? onClose;

  const PermissionInstructionDialog({
    super.key,
    required this.message,
    this.isCloseVisible = true,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final isTablet = context.isTablet;
    
    return CustomAppDialog(
      isCloseVisible: isCloseVisible,
      onClose: onClose,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 20.h),
          
          // Permission icon
          Container(
            width: 60.w,
            height: 60.h,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.security,
              color: Theme.of(context).colorScheme.primary,
              size: 30.w,
            ),
          ),
          
          SizedBox(height: 24.h),
          
          // Message text
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 10.w : 0.w,
            ),
            child: Text(
              message,
              style: AppTextStyles.bodyMedium.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: isTablet ? 16.sp : 18.sp,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          
          SizedBox(height: 32.h),
          
          // Action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onClose ?? () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.cFirstColor,
                foregroundColor: AppColors.white,
                padding: EdgeInsets.symmetric(vertical: 16.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              child: Text(
                'Tushundim',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          
          SizedBox(height: 20.h),
        ],
      ),
    );
  }
}

/// Confirmation dialog that matches app style
class ConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmText;
  final String cancelText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final Color? confirmButtonColor;
  final IconData? icon;

  const ConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    this.confirmText = 'Tasdiqlash',
    this.cancelText = 'Bekor qilish',
    this.onConfirm,
    this.onCancel,
    this.confirmButtonColor,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return CustomAppDialog(
      isCloseVisible: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          if (icon != null) ...[
            Container(
              width: 60.w,
              height: 60.h,
              decoration: BoxDecoration(
                color: (confirmButtonColor ?? Theme.of(context).colorScheme.primary).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: confirmButtonColor ?? Theme.of(context).colorScheme.primary,
                size: 30.w,
              ),
            ),
            SizedBox(height: 16.h),
          ],
          
          // Title
          Text(
            title,
            style: AppTextStyles.titleMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: 12.h),
          
          // Message
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: 24.h),
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: onCancel ?? () => Navigator.of(context).pop(false),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.cTextGrayColor,
                    side: BorderSide(color: AppColors.cTextGrayColor),
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(cancelText),
                ),
              ),
              
              SizedBox(width: 12.w),
              
              Expanded(
                child: ElevatedButton(
                  onPressed: onConfirm ?? () => Navigator.of(context).pop(true),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: confirmButtonColor ?? AppColors.cFirstColor,
                    foregroundColor: AppColors.white,
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(confirmText),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
