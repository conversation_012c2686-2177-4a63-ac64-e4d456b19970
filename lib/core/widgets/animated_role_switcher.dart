import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../translations/locale_keys.g.dart' show LocaleKeys;
import '../theme/app_text_styles.dart';
import '../utils/app_constants.dart';
import 'role_switcher.dart';

/// Animated role switcher with smooth transitions and haptic feedback
class AnimatedRoleSwitcher extends StatefulWidget {
  final UserRole currentRole;
  final Function(UserRole) onRoleChanged;

  const AnimatedRoleSwitcher({
    super.key,
    required this.currentRole,
    required this.onRoleChanged,
  });

  @override
  State<AnimatedRoleSwitcher> createState() => _AnimatedRoleSwitcherState();
}

class _AnimatedRoleSwitcherState extends State<AnimatedRoleSwitcher>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Set initial animation state
    if (widget.currentRole == UserRole.sotuvchi) {
      _animationController.value = 1.0;
    }
  }

  @override
  void didUpdateWidget(AnimatedRoleSwitcher oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentRole != widget.currentRole) {
      if (widget.currentRole == UserRole.sotuvchi) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleRoleChange(UserRole newRole) {
    if (newRole != widget.currentRole) {
      // Add haptic feedback
      HapticFeedback.mediumImpact();
      
      // Trigger animation
      if (newRole == UserRole.sotuvchi) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
      
      widget.onRoleChanged(newRole);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 56.h,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // Animated background slider
          AnimatedBuilder(
            animation: _slideAnimation,
            builder: (context, child) {
              return Positioned(
                left: _slideAnimation.value * (MediaQuery.of(context).size.width - 46.w) / 2,
                top: 4.h,
                bottom: 4.h,
                width: (MediaQuery.of(context).size.width - 32.w) / 2,
                child: Container(
                  margin: EdgeInsets.only(left: 4.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(8.r),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          // Role options
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => _handleRoleChange(UserRole.nazoratchi),
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      final isSelected = widget.currentRole == UserRole.nazoratchi;
                      return Transform.scale(
                        scale: isSelected ? _scaleAnimation.value : 1.0,
                        child: Container(
                          height: 56.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Center(
                            child: AnimatedDefaultTextStyle(
                              duration: const Duration(milliseconds: 200),
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.onPrimary
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                                fontSize: 14.sp,
                                fontWeight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.w500,
                              ),
                              child:  Text(LocaleKeys.auth_role_selection_nazoratchi.tr()),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => _handleRoleChange(UserRole.sotuvchi),
                  child: AnimatedBuilder(
                    animation: _scaleAnimation,
                    builder: (context, child) {
                      final isSelected = widget.currentRole == UserRole.sotuvchi;
                      return Transform.scale(
                        scale: isSelected ? _scaleAnimation.value : 1.0,
                        child: Container(
                          height: 56.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Center(
                            child: AnimatedDefaultTextStyle(
                              duration: const Duration(milliseconds: 200),
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.onPrimary
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                                fontSize: 14.sp,
                                fontWeight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.w500,
                              ),
                              child:  Text(LocaleKeys.auth_role_selection_sotuvchi.tr()),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
