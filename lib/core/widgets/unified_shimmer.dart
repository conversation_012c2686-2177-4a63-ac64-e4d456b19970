import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Unified shimmer loading component optimized for both light and dark themes
class UnifiedShimmer extends StatefulWidget {
  final Widget child;
  final bool enabled;
  final Duration duration;
  final Color? baseColor;
  final Color? highlightColor;

  const UnifiedShimmer({
    super.key,
    required this.child,
    this.enabled = true,
    this.duration = const Duration(milliseconds: 1500),
    this.baseColor,
    this.highlightColor,
  });

  /// Create a shimmer container with rounded corners
  static Widget container({
    required BuildContext context,
    required double width,
    required double height,
    double borderRadius = 8,
    bool enabled = true,
  }) {
    return UnifiedShimmer(
      enabled: enabled,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: _getBaseColor(context),
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }

  /// Create a shimmer text placeholder
  static Widget text({
    required BuildContext context,
    required double width,
    double height = 16,
    double borderRadius = 4,
    bool enabled = true,
  }) {
    return UnifiedShimmer(
      enabled: enabled,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: _getBaseColor(context),
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }

  /// Create a shimmer circle (for avatars, icons, etc.)
  static Widget circle({
    required BuildContext context,
    required double diameter,
    bool enabled = true,
  }) {
    return UnifiedShimmer(
      enabled: enabled,
      child: Container(
        width: diameter,
        height: diameter,
        decoration: BoxDecoration(
          color: _getBaseColor(context),
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  /// Get theme-aware base color - gray for better visibility
  static Color _getBaseColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? const Color(0xFF2A2B2C) // Dark gray for dark theme
        : const Color(0xFFF0F0F0); // Light gray for light theme
  }

  /// Get theme-aware highlight color
  static Color _getHighlightColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.3)
        : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.15);
  }

  @override
  State<UnifiedShimmer> createState() => _UnifiedShimmerState();
}

class _UnifiedShimmerState extends State<UnifiedShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutSine,
    ));

    if (widget.enabled) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(UnifiedShimmer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.enabled != oldWidget.enabled) {
      if (widget.enabled) {
        _controller.repeat();
      } else {
        _controller.stop();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    final baseColor = widget.baseColor ?? UnifiedShimmer._getBaseColor(context);
    final highlightColor = widget.highlightColor ?? UnifiedShimmer._getHighlightColor(context);

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                baseColor,
                highlightColor,
             AppColors.white,
              ],
              stops: [
                (_animation.value - 1.0).clamp(0.0, 1.0),
                _animation.value.clamp(0.0, 1.0),
                (_animation.value + 1.0).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Shimmer skeleton for cards
class ShimmerCard extends StatelessWidget {
  final double? width;
  final double? height;
  final double borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final List<Widget>? children;

  const ShimmerCard({
    super.key,
    this.width,
    this.height,
    this.borderRadius = 12,
    this.margin,
    this.padding,
    this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin ?? EdgeInsets.all(8.w),
      padding: padding ?? EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: UnifiedShimmer._getBaseColor(context),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: children != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children!,
            )
          : null,
    );
  }
}

/// Shimmer skeleton for list items
class ShimmerListItem extends StatelessWidget {
  final double? height;
  final EdgeInsetsGeometry? padding;
  final bool hasLeading;
  final bool hasTrailing;
  final int titleLines;
  final int subtitleLines;

  const ShimmerListItem({
    super.key,
    this.height,
    this.padding,
    this.hasLeading = true,
    this.hasTrailing = false,
    this.titleLines = 1,
    this.subtitleLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 72.h,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        children: [
          if (hasLeading) ...[
            UnifiedShimmer.circle(context: context, diameter: 40.w),
            SizedBox(width: 16.w),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ...List.generate(titleLines, (index) => Padding(
                  padding: EdgeInsets.only(bottom: index < titleLines - 1 ? 4.h : 0),
                  child: UnifiedShimmer.text(
                    context: context,
                    width: index == 0 ? double.infinity : 120.w,
                    height: 16.h,
                  ),
                )),
                if (subtitleLines > 0) ...[
                  SizedBox(height: 8.h),
                  ...List.generate(subtitleLines, (index) => Padding(
                    padding: EdgeInsets.only(bottom: index < subtitleLines - 1 ? 4.h : 0),
                    child: UnifiedShimmer.text(
                      context: context,
                      width: index == 0 ? 200.w : 80.w,
                      height: 14.h,
                    ),
                  )),
                ],
              ],
            ),
          ),
          if (hasTrailing) ...[
            SizedBox(width: 16.w),
            UnifiedShimmer.container(
              context: context,
              width: 24.w,
              height: 24.w,
              borderRadius: 4,
            ),
          ],
        ],
      ),
    );
  }
}
