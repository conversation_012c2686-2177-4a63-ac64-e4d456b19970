import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../theme/app_colors.dart';

/// Universal loading components for different UI scenarios
class UniversalLoading {
  /// Shimmer effect for loading states
  static Widget shimmer({
    required Widget child,
    bool isLoading = true,
  }) {
    if (!isLoading) return child;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 1000),
      child: _ShimmerWidget(child: child),
    );
  }

  /// Profile header loading skeleton


  /// Personal info row loading skeleton
  static Widget personalInfoRow({bool isStyled = false}) {
    return Builder(
      builder: (context) {
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return Padding(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Container(
            width: double.infinity,
            height: 60,
            color: isDark
                ? const Color(0xFF2A2B2C) // Dark gray for dark theme
                : const Color(0xFFF0F0F0), // Light gray for light theme
          )
        );
      }
    );
  }

  /// Statistics metric card loading skeleton
  static Widget statisticsMetricCard() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 12.h,
            width: 80.w,
            decoration: BoxDecoration(
              color: AppColors.cTextGrayColor,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
          Gap(6.h),
          Container(
            height: 20.h,
            width: 60.w,
            decoration: BoxDecoration(
              color: AppColors.cTextGrayColor,
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
        ],
      ),
    );
  }

  /// Statistics metrics row loading skeleton
  static Widget statisticsMetricsRow() {
    return Column(
      children: [
        // First row
        Row(
          children: [
            Expanded(child: statisticsMetricCard()),
            Gap(12.w),
            Expanded(child: statisticsMetricCard()),
          ],
        ),
        Gap(12.h),
        // Second row
        Row(
          children: [
            Expanded(child: statisticsMetricCard()),
            Gap(12.w),
            Expanded(child: statisticsMetricCard()),
          ],
        ),
        Gap(12.h),
        // Third row
        Row(
          children: [
            Expanded(child: statisticsMetricCard()),
            Gap(12.w),
            Expanded(child: statisticsMetricCard()),
          ],
        ),
      ],
    );
  }

  /// Statistics circle chart loading skeleton
  static Widget statisticsCircleChart() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.cGrayBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Circle chart skeleton
          Container(
            width: 200.w,
            height: 200.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.cTextGrayColor.withValues(alpha: 0.2),
            ),
            child: Center(
              child: Container(
                width: 135.w,
                height: 135.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
                ),
              ),
            ),
          ),
          Gap(32.h),
          // Legend skeleton
          Column(
            children: [
              _statisticsLegendItem(),
              Gap(12.h),
              _statisticsLegendItem(),
              Gap(12.h),
              _statisticsLegendItem(),
              Gap(12.h),
              _statisticsLegendItem(),
            ],
          ),
        ],
      ),
    );
  }

  /// Statistics legend item skeleton
  static Widget _statisticsLegendItem() {
    return Row(
      children: [
        Container(
          width: 12.w,
          height: 12.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.cTextGrayColor,
          ),
        ),
        Gap(8.w),
        Container(
          height: 14.h,
          width: 80.w,
          decoration: BoxDecoration(
            color: AppColors.cTextGrayColor,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ),
        const Spacer(),
        Container(
          height: 14.h,
          width: 40.w,
          decoration: BoxDecoration(
            color: AppColors.cTextGrayColor,
            borderRadius: BorderRadius.circular(4.r),
          ),
        ),
      ],
    );
  }

  /// Statistics payment cards row skeleton
  static Widget statisticsPaymentCards() {
    return Row(
      children: [
        Expanded(child: statisticsMetricCard()),
        Gap(8.w),
        Expanded(child: statisticsMetricCard()),
      ],
    );
  }

  /// List item loading skeleton

  /// Pulsing loading animation
  static Widget pulse({
    required Widget child,
    bool isLoading = true,
    Duration duration = const Duration(milliseconds: 1200),
  }) {
    if (!isLoading) return child;

    return _PulseWidget(
      duration: duration,
      child: child,
    );
  }

  /// Error widget for displaying error states
  static Widget error({
    required String message,
    VoidCallback? onRetry,
    String? retryButtonText,
    IconData? icon,
    double? iconSize,
    Color? iconColor,
    TextStyle? messageStyle,
    EdgeInsets? padding,
  }) {
    return Builder(
      builder: (context) => Container(
        padding: padding ?? EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon ?? Icons.error_outline,
              size: iconSize ?? 64.w,
              color: iconColor ?? Theme.of(context).colorScheme.error,
            ),
            Gap(16.h),
            Text(
              message,
              style: messageStyle ?? TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              Gap(24.h),
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                child: Text(
                  retryButtonText ?? 'Qayta urinish',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Internal shimmer widget
class _ShimmerWidget extends StatefulWidget {
  final Widget child;

  const _ShimmerWidget({required this.child});

  @override
  State<_ShimmerWidget> createState() => _ShimmerWidgetState();
}

class _ShimmerWidgetState extends State<_ShimmerWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                AppColors.cTextGrayColor.withValues(alpha: 0.1),
                AppColors.cTextGrayColor.withValues(alpha: 0.3),
                AppColors.cTextGrayColor.withValues(alpha: 0.1),
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ],
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// Internal pulse widget
class _PulseWidget extends StatefulWidget {
  final Widget child;
  final Duration duration;

  const _PulseWidget({
    required this.child,
    required this.duration,
  });

  @override
  State<_PulseWidget> createState() => _PulseWidgetState();
}

class _PulseWidgetState extends State<_PulseWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: widget.child,
        );
      },
    );
  }
}
