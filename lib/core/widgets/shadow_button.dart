import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme/app_colors.dart';
import '../theme/theme_constants.dart';

/// Beautiful shadowed button with primary color and multi-layer shadows
class ShadowButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool isLoading;
  final Widget? icon;
  final bool enabled;

  const ShadowButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.padding,
    this.borderRadius,
    this.isLoading = false,
    this.icon,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<ShadowButton> createState() => _ShadowButtonState();
}

class _ShadowButtonState extends State<ShadowButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: ThemeConstants.animationDurationFast,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 0.5,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.enabled && !widget.isLoading) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.enabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.enabled && !widget.isLoading) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = widget.backgroundColor ?? AppColors.cFirstColor;
    final textColor = widget.textColor ?? AppColors.white;
    
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: widget.enabled && !widget.isLoading ? widget.onPressed : null,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.width,
              height: widget.height ?? 48.h,
              padding: widget.padding ?? EdgeInsets.symmetric(
                horizontal: 24.w,
                vertical: 12.h,
              ),
              decoration: BoxDecoration(
                color: widget.enabled 
                  ? backgroundColor 
                  : backgroundColor.withOpacity(0.5),
                borderRadius: widget.borderRadius ?? BorderRadius.circular(
                  ThemeConstants.borderRadiusMedium,
                ),
                boxShadow: widget.enabled && !isDark ? [
                  // Primary shadow (only in light theme)
                  BoxShadow(
                    color: AppColors.primaryShadowLight.withOpacity(_shadowAnimation.value),
                    offset: ThemeConstants.primaryShadowOffset,
                    blurRadius: ThemeConstants.primaryShadowBlurRadius * _shadowAnimation.value,
                    spreadRadius: ThemeConstants.shadowSpreadRadius,
                  ),
                  // Secondary shadow for depth (only in light theme)
                  BoxShadow(
                    color: AppColors.cardShadowLight.withOpacity(_shadowAnimation.value * 0.5),
                    offset: Offset(0, 2),
                    blurRadius: 8 * _shadowAnimation.value,
                    spreadRadius: 0,
                  ),
                ] : null,
              ),
              child: widget.isLoading
                ? Center(
                    child: SizedBox(
                      width: 20.w,
                      height: 20.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(textColor),
                      ),
                    ),
                  )
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (widget.icon != null) ...[
                        widget.icon!,
                        SizedBox(width: 8.w),
                      ],
                      Text(
                        widget.text,
                        style: TextStyle(
                          color: widget.enabled 
                            ? textColor 
                            : textColor.withOpacity(0.5),
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          fontFamily: 'EuclidCircularA',
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
            ),
          );
        },
      ),
    );
  }
}

/// Beautiful shadowed card with primary color accent
class ShadowCard extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;
  final VoidCallback? onTap;
  final bool hasPrimaryAccent;

  const ShadowCard({
    Key? key,
    required this.child,
    this.backgroundColor,
    this.padding,
    this.margin,
    this.borderRadius,
    this.onTap,
    this.hasPrimaryAccent = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final cardColor = backgroundColor ?? Theme.of(context).colorScheme.surface;
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: margin ?? EdgeInsets.all(8.w),
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: borderRadius ?? BorderRadius.circular(
            ThemeConstants.borderRadiusLarge,
          ),
          border: hasPrimaryAccent ? Border.all(
            color: AppColors.cFirstColor.withOpacity(0.3),
            width: 1,
          ) : null,
          boxShadow: !isDark ? [
            // Primary shadow (only in light theme)
            BoxShadow(
              color: AppColors.cardShadowLight,
              offset: ThemeConstants.shadowOffset,
              blurRadius: ThemeConstants.shadowBlurRadius,
              spreadRadius: ThemeConstants.shadowSpreadRadius,
            ),
            // Secondary shadow for depth (only in light theme)
            BoxShadow(
              color: AppColors.shadowLight.withValues(alpha: 0.3),
              offset: Offset(0, 2),
              blurRadius: 6,
              spreadRadius: 0,
            ),
          ] : null,
        ),
        child: Padding(
          padding: padding ?? EdgeInsets.all(16.w),
          child: child,
        ),
      ),
    );
  }
}
