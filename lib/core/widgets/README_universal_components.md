# Universal Components Documentation

This document describes the universal components created for consistent UI across the application.

## UniversalAvatar

A flexible avatar component that handles profile images, fallbacks, and loading states.

### Features
- Supports network images, local files, and fallback states
- Automatic initials generation from names
- Camera icon for editable profiles
- Multiple size presets (small, medium, profile)
- Customizable colors and borders
- Circle borders for placeholder images
- Customizable placeholder border colors and widths

### Usage Examples

#### Profile Avatar with Camera
```dart
UniversalAvatar.profile(
  imageUrl: user.image,
  imageFile: selectedFile,
  size: 120,
  onCameraTap: () => _showImagePicker(),
  isLoading: isUploading,
  fallbackText: user.fullName,
  placeholderBorderColor: AppColors.cFirstColor,
  placeholderBorderWidth: 2,
)
```

#### Small Avatar for Lists
```dart
UniversalAvatar.small(
  imageUrl: user.image,
  size: 40,
  fallbackText: user.fullName,
  backgroundColor: AppColors.cCardsColor,
)
```

#### Medium Avatar
```dart
UniversalAvatar.medium(
  imageUrl: user.image,
  size: 80,
  fallbackText: user.fullName,
)
```

#### Custom Avatar
```dart
UniversalAvatar(
  imageUrl: user.image,
  size: 100,
  borderColor: AppColors.cFirstColor,
  borderWidth: 2,
  fallbackText: user.fullName,
  backgroundColor: AppColors.cCardsColor,
)
```

### Properties
- `imageUrl`: Network image URL
- `imageFile`: Local file for selected images
- `size`: Avatar size (default: 120)
- `showCameraIcon`: Show camera icon for editing
- `onCameraTap`: Callback for camera icon tap
- `fallbackText`: Text for initials generation
- `backgroundColor`: Background color for fallback
- `borderColor`: Border color for main avatar
- `borderWidth`: Border width for main avatar
- `placeholderBorderColor`: Border color for placeholder/fallback
- `placeholderBorderWidth`: Border width for placeholder/fallback

## UniversalLoading

Professional loading components for different UI scenarios.

### Features
- Shimmer effects for skeleton loading
- Profile-specific loading states
- List item loading skeletons
- Card loading placeholders
- Full-screen loading overlays
- Pulsing animations

### Usage Examples

#### Shimmer Loading
```dart
UniversalLoading.shimmer(
  isLoading: isLoading,
  child: YourWidget(),
)
```

#### Profile Header Loading
```dart
UniversalLoading.profileHeader()
```

#### Personal Info Row Loading
```dart
UniversalLoading.personalInfoRow()
```

#### List Item Loading
```dart
UniversalLoading.listItem(
  showAvatar: true,
  showSubtitle: true,
  height: 60,
)
```

#### Card Loading
```dart
UniversalLoading.card(
  height: 100,
  padding: EdgeInsets.all(16),
)
```

#### Full Screen Loading
```dart
UniversalLoading.overlay(
  message: 'Loading...',
  showBackground: true,
)
```

#### Loading Indicator
```dart
UniversalLoading.indicator(
  size: 24,
  color: AppColors.cFirstColor,
  strokeWidth: 2,
)
```

#### Pulse Animation
```dart
UniversalLoading.pulse(
  isLoading: isLoading,
  duration: Duration(milliseconds: 1200),
  child: YourWidget(),
)
```

## Implementation in Pages

### Adding Swipe-to-Refresh

```dart
RefreshIndicator(
  onRefresh: _onRefresh,
  color: AppColors.cFirstColor,
  backgroundColor: AppColors.cCardsColor,
  child: SingleChildScrollView(
    physics: const AlwaysScrollableScrollPhysics(),
    child: YourContent(),
  ),
)
```

### Refresh Handler
```dart
Future<void> _onRefresh() async {
  _loadData(forceRefresh: true);
  
  // Wait for the refresh to complete
  await _bloc.stream
      .firstWhere((state) => state is! LoadingState);
}
```

### BLoC Integration
```dart
BlocBuilder<YourBloc, YourState>(
  builder: (context, state) {
    if (state is LoadingState) {
      return UniversalLoading.shimmer(
        child: UniversalLoading.profileHeader(),
      );
    }
    
    if (state is LoadedState) {
      return YourContent(data: state.data);
    }
    
    return ErrorWidget();
  },
)
```

## Best Practices

1. **Consistent Sizing**: Use predefined size presets (small, medium, profile)
2. **Loading States**: Always provide loading states for better UX
3. **Fallback Handling**: Provide meaningful fallback text for avatars
4. **Shimmer Effects**: Use shimmer for skeleton loading states
5. **Swipe-to-Refresh**: Implement on all data-driven pages
6. **Error Handling**: Show cached data when available during errors

## Color Scheme

The components use the app's color scheme:
- `AppColors.cFirstColor`: Primary accent color
- `AppColors.cCardsColor`: Card background color
- `AppColors.cBackgroundColor`: Main background color
- `AppColors.white`: White text/elements
- `AppColors.cTextGrayColor`: Secondary text color

## Performance Considerations

1. **Image Caching**: Network images are automatically cached
2. **Animation Performance**: Shimmer and pulse animations are optimized
3. **Memory Management**: Proper disposal of animation controllers
4. **Lazy Loading**: Components only render when needed
