import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import '../theme/theme_manager.dart';
import '../theme/theme_constants.dart';
import '../theme/app_colors.dart';
import '../../translations/locale_keys.g.dart';

/// Animated theme switcher widget with smooth sun/moon transition
class AnimatedThemeSwitcher extends StatefulWidget {
  final VoidCallback? onThemeChanged;
  final double size;

  const AnimatedThemeSwitcher({
    Key? key,
    this.onThemeChanged,
    this.size = 28.0,
  }) : super(key: key);

  @override
  State<AnimatedThemeSwitcher> createState() => _AnimatedThemeSwitcherState();
}

class _AnimatedThemeSwitcherState extends State<AnimatedThemeSwitcher>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late ThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _themeManager = ThemeManager();
    
    _animationController = AnimationController(
      duration: ThemeConstants.themeTransitionDuration,
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Interval(0.0, 0.5, curve: Curves.easeOut),
    ));

    // Set initial animation state based on current theme
    if (_themeManager.isEffectiveDarkMode) {
      _animationController.value = 1.0;
    }

    _themeManager.addListener(_onThemeChanged);
  }

  void _onThemeChanged() {
    if (_themeManager.isEffectiveDarkMode) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  void dispose() {
    _themeManager.removeListener(_onThemeChanged);
    _animationController.dispose();
    super.dispose();
  }

  void _toggleTheme() {
    _themeManager.toggleTheme();
    widget.onThemeChanged?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _toggleTheme,
      child: Container(
        width: widget.size.w,
        height: widget.size.w,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.transparent,
        ),
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Transform.rotate(
                angle: _rotationAnimation.value * 3.14159, // 180 degrees
                child: Icon(
                  _rotationAnimation.value > 0.5 
                    ? Icons.dark_mode_rounded 
                    : Icons.light_mode_rounded,
                  size: widget.size.w,
                  color: _themeManager.isEffectiveDarkMode
                    ? AppColors.cYellowishColor
                    : AppColors.cFirstColor,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

/// Theme selection bottom sheet
class ThemeSelectionBottomSheet extends StatelessWidget {
  const ThemeSelectionBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final themeManager = ThemeManager();
    
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2.r),
            ),
          ),
          SizedBox(height: 20.h),
          
          // Title
          Text(
            LocaleKeys.theme_title.tr(),
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 24.h),
          
          // Theme options
          _buildThemeOption(
            context: context,
            icon: Icons.light_mode_rounded,
            title: LocaleKeys.theme_light.tr(),
            subtitle: LocaleKeys.theme_light_subtitle.tr(),
            isSelected: themeManager.isLightMode,
            onTap: () {
              themeManager.setLightMode();
              Navigator.pop(context);
            },
          ),
          SizedBox(height: 16.h),
          
          _buildThemeOption(
            context: context,
            icon: Icons.dark_mode_rounded,
            title: LocaleKeys.theme_dark.tr(),
            subtitle: LocaleKeys.theme_dark_subtitle.tr(),
            isSelected: themeManager.isDarkMode,
            onTap: () {
              themeManager.setDarkMode();
              Navigator.pop(context);
            },
          ),
          SizedBox(height: 16.h),
          
          _buildThemeOption(
            context: context,
            icon: Icons.brightness_auto_rounded,
            title: LocaleKeys.theme_system.tr(),
            subtitle: LocaleKeys.theme_system_subtitle.tr(),
            isSelected: themeManager.isSystemMode,
            onTap: () {
              themeManager.setSystemMode();
              Navigator.pop(context);
            },
          ),
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  Widget _buildThemeOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected 
            ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
            : Colors.transparent,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isSelected 
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface,
              size: 24.w,
            ),
            SizedBox(width: 16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: isSelected 
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle_rounded,
                color: Theme.of(context).colorScheme.primary,
                size: 20.w,
              ),
          ],
        ),
      ),
    );
  }
}
