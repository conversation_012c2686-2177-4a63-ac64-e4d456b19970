import 'package:click_bazaar/core/utils/app_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import '../theme/app_colors.dart';
import '../theme/app_text_styles.dart';
import '../utils/app_constants.dart';
import '../../generated/assets.dart';

enum UserRole { nazoratchi, sotuvchi }

class RoleSwitcher extends StatefulWidget {
  final UserRole currentRole;
  final Function(UserRole) onRoleChanged;

  const RoleSwitcher({
    super.key,
    required this.currentRole,
    required this.onRoleChanged,
  });

  @override
  State<RoleSwitcher> createState() => _RoleSwitcherState();
}

class _RoleSwitcherState extends State<RoleSwitcher>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<Color?> _colorAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    ));

    _colorAnimation = ColorTween(
      begin: AppColors.cFirstColor,
      end: AppColors.cGreenishColor,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.currentRole == UserRole.sotuvchi) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleRole() {
    final newRole = widget.currentRole == UserRole.nazoratchi
        ? UserRole.sotuvchi
        : UserRole.nazoratchi;

    if (newRole == UserRole.sotuvchi) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    // Enhanced haptic feedback for smoother feel
    HapticFeedback.mediumImpact();

    widget.onRoleChanged(newRole);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cCardsColor,
        borderRadius: BorderRadius.circular(cRadius16.r),
        border: Border.all(
          color: AppColors.cGrayBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 38.w,
                height: 40.w,
                decoration: BoxDecoration(
                  color: AppColors.cFirstColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(cRadius8.r),
                ),
                child: Center(
                  child: SvgPicture.asset(
                    Assets.iconsProfile,
                    width: 20.w,
                    height: 20.w,
                    colorFilter: ColorFilter.mode(
                      AppColors.cFirstColor,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ),
              Gap(12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Rol almashish',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.white,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Gap(2.h),
                    Text(
                      'Nazoratchi va sotuvchi o\'rtasida almashtiring',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.cTextGrayColor,
                        fontSize: 12.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Gap(20.h),
          _buildRoleToggle(),
        ],
      ),
    );
  }

  Widget _buildRoleToggle() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final containerWidth = constraints.maxWidth;
        final sliderWidth = (containerWidth - 8.w) / 2; // Account for padding
        final maxSlideDistance =
            containerWidth - sliderWidth - 8.w; // Account for padding

        return Container(
          width: double.infinity,
          height: 58.h,
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.circular(cRadius12.r),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline,
              width: 1,
            ),
          ),
          child: Stack(
            children: [
              // Animated background slider
              AnimatedBuilder(
                animation: _slideAnimation,
                builder: (context, child) {
                  return Positioned(
                    left: 4.w + (_slideAnimation.value * maxSlideDistance),
                    top: 4.h,
                    child: Container(
                      width: sliderWidth,
                      height: 48.h,
                      decoration: BoxDecoration(
                        color: _colorAnimation.value ?? AppColors.cFirstColor,
                        borderRadius: BorderRadius.circular(cRadius8.r),
                        boxShadow: [
                          BoxShadow(
                            color:
                                (_colorAnimation.value ?? AppColors.cFirstColor)
                                    .withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
              // Role options
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        if (widget.currentRole != UserRole.nazoratchi) {
                          _toggleRole();
                        }
                      },
                      child: Container(
                        height: 56.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(cRadius12.r),
                        ),
                        child: Center(
                          child: AnimatedBuilder(
                            animation: _slideAnimation,
                            builder: (context, child) {
                              final isSelected =
                                  widget.currentRole == UserRole.nazoratchi;
                              return Text(
                                'Nazoratchi',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: isSelected
                                      ? Theme.of(context).colorScheme.onPrimary
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontSize: 14.sp,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        if (widget.currentRole != UserRole.sotuvchi) {
                          _toggleRole();
                        }
                      },
                      child: Container(
                        height: 56.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(cRadius12.r),
                        ),
                        child: Center(
                          child: AnimatedBuilder(
                            animation: _slideAnimation,
                            builder: (context, child) {
                              final isSelected =
                                  widget.currentRole == UserRole.sotuvchi;
                              return Text(
                                'Sotuvchi',
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: isSelected
                                      ? Theme.of(context).colorScheme.onPrimary
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontSize: 14.sp,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w500,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
