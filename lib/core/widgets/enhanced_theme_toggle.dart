import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../theme/theme_manager.dart';
import '../theme/theme_constants.dart';
import '../theme/app_colors.dart';

/// Enhanced animated theme toggle switch with beautiful sun/moon transition
class EnhancedThemeToggle extends StatefulWidget {
  final VoidCallback? onThemeChanged;
  final double? width;
  final double? height;
  final bool showLabels;

  const EnhancedThemeToggle({
    Key? key,
    this.onThemeChanged,
    this.width,
    this.height,
    this.showLabels = false,
  }) : super(key: key);

  @override
  State<EnhancedThemeToggle> createState() => _EnhancedThemeToggleState();
}

class _EnhancedThemeToggleState extends State<EnhancedThemeToggle>
    with TickerProviderStateMixin {
  late AnimationController _toggleController;
  late AnimationController _iconController;
  late Animation<double> _toggleAnimation;
  late Animation<double> _iconRotationAnimation;
  late Animation<double> _iconScaleAnimation;
  late Animation<Color?> _trackColorAnimation;
  late Animation<Color?> _thumbColorAnimation;
  late ThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _themeManager = ThemeManager();

    // Toggle animation controller
    _toggleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Icon animation controller
    _iconController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Toggle position animation
    _toggleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _toggleController,
      curve: Curves.easeInOut,
    ));

    // Icon rotation animation
    _iconRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    ));

    // Icon scale animation
    _iconScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _iconController,
      curve: Curves.elasticOut,
    ));

    // Track color animation
    _trackColorAnimation = ColorTween(
      begin: AppColors.cLightBorderColor,
      end: AppColors.border,
    ).animate(_toggleController);

    // Thumb color animation
    _thumbColorAnimation = ColorTween(
      begin: AppColors.cYellowishColor,
      end: AppColors.cFirstColor,
    ).animate(_toggleController);

    // Set initial state
    if (_themeManager.isEffectiveDarkMode) {
      _toggleController.value = 1.0;
      _iconController.value = 1.0;
    }

    _themeManager.addListener(_onThemeChanged);
  }

  void _onThemeChanged() {
    if (_themeManager.isEffectiveDarkMode) {
      _toggleController.forward();
      _iconController.forward();
    } else {
      _toggleController.reverse();
      _iconController.reverse();
    }
  }

  @override
  void dispose() {
    _themeManager.removeListener(_onThemeChanged);
    _toggleController.dispose();
    _iconController.dispose();
    super.dispose();
  }

  void _handleToggle() {
    _themeManager.toggleTheme();
    widget.onThemeChanged?.call();
  }

  @override
  Widget build(BuildContext context) {
    final toggleWidth = widget.width ?? 60.w;
    final toggleHeight = widget.height ?? 32.h;
    final thumbSize = toggleHeight - 4.h;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          onTap: _handleToggle,
          child: AnimatedBuilder(
            animation: Listenable.merge([_toggleController, _iconController]),
            builder: (context, child) {
              return Container(
                width: toggleWidth,
                height: toggleHeight,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(toggleHeight / 2),
                  color: _trackColorAnimation.value,
                  boxShadow: [
                    BoxShadow(
                      color: _themeManager.isEffectiveDarkMode
                          ? AppColors.primaryShadowDark
                          : AppColors.cardShadowLight,
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Animated thumb
                    AnimatedPositioned(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      left: _toggleAnimation.value * (toggleWidth - thumbSize - 4.w) + 2.w,
                      top: 2.h,
                      child: Container(
                        width: thumbSize,
                        height: thumbSize,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _thumbColorAnimation.value,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Transform.scale(
                          scale: _iconScaleAnimation.value,
                          child: Transform.rotate(
                            angle: _iconRotationAnimation.value * 3.14159,
                            child: Icon(
                              _toggleAnimation.value > 0.5
                                  ? Icons.dark_mode_rounded
                                  : Icons.light_mode_rounded,
                              size: thumbSize * 0.6,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        if (widget.showLabels) ...[
          SizedBox(height: 8.h),
          AnimatedBuilder(
            animation: _toggleController,
            builder: (context, child) {
              return Text(
                _toggleAnimation.value > 0.5 ? 'Qorong\'u' : 'Yorug\'',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  fontWeight: FontWeight.w500,
                ),
              );
            },
          ),
        ],
      ],
    );
  }
}

/// Simple theme toggle button for app bars and minimal spaces
class SimpleThemeToggle extends StatefulWidget {
  final double size;
  final VoidCallback? onThemeChanged;

  const SimpleThemeToggle({
    Key? key,
    this.size = 24.0,
    this.onThemeChanged,
  }) : super(key: key);

  @override
  State<SimpleThemeToggle> createState() => _SimpleThemeToggleState();
}

class _SimpleThemeToggleState extends State<SimpleThemeToggle>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late ThemeManager _themeManager;

  @override
  void initState() {
    super.initState();
    _themeManager = ThemeManager();

    _controller = AnimationController(
      duration: ThemeConstants.themeTransitionDuration,
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
    ));

    if (_themeManager.isEffectiveDarkMode) {
      _controller.value = 1.0;
    }

    _themeManager.addListener(_onThemeChanged);
  }

  void _onThemeChanged() {
    if (_themeManager.isEffectiveDarkMode) {
      _controller.forward();
    } else {
      _controller.reverse();
    }
  }

  @override
  void dispose() {
    _themeManager.removeListener(_onThemeChanged);
    _controller.dispose();
    super.dispose();
  }

  void _handleToggle() {
    _themeManager.toggleTheme();
    widget.onThemeChanged?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleToggle,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value * 3.14159,
              child: Icon(
                _rotationAnimation.value > 0.5
                    ? Icons.dark_mode_rounded
                    : Icons.light_mode_rounded,
                size: widget.size,
                color: _themeManager.isEffectiveDarkMode
                    ? AppColors.cYellowishColor
                    : AppColors.cFirstColor,
              ),
            ),
          );
        },
      ),
    );
  }
}
