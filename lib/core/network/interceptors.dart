import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/di/dependency_injection.dart';
import 'package:click_bazaar/env.dart';
import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'dart:async';

import '../utils/app_constants.dart';
import '../services/logout_service.dart';
import '../services/navigation_service.dart';

class AppInterceptor extends Interceptor {
  final GetStorage storage;
  final NetworkInfo networkInfo;
  final Dio dio;
  late final LogoutService _logoutService;
  late final NavigationService _navigationService;

  // For refresh token handling
  bool _isRefreshing = false;
  final List<_RetryRequest> _pendingRequests = [];
  Completer<bool>? _refreshCompleter;

  AppInterceptor({
    required this.storage,
    required this.networkInfo,
    required this.dio,
  }) {
    _logoutService = LogoutService(storage: storage, prefs: di());
    _navigationService = NavigationService();
  }

  @override
  void onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    bool isDemoMode = storage.read(is_demo) ?? false;
    bool hasStoredToken = storage.read(TOKEN) != null;

    // Priority order for authentication:
    // 1. If has stored token (real user) -> use stored token
    // 2. If demo mode without stored token -> use guest token
    // 3. Otherwise -> no auth header

    if (hasStoredToken) {
      // Real authenticated user
      options.headers['Authorization'] = 'Bearer ${storage.read(TOKEN)}';
    } else if (isDemoMode) {
      // Demo/guest mode - use guest token globally
      options.headers['Authorization'] = 'Bearer $GUEST_TOKEN';
    }

    // Always set accept header
    options.headers["Accept"] = "application/json";

    // Remove any manual guest mode headers as we handle it globally now
    options.headers.remove('X-Guest-Mode');

    // Set base URL based on demo mode
    var isDemo = storage.read(is_demo) ?? false;
    if (isDemo) {
      options.baseUrl = ApiPath.demoUrl;
    } else {
      options.baseUrl = ApiPath.baseUrl;
    }

    // If we're refreshing tokens, queue the request
    if (_isRefreshing && !options.path.contains(ApiPath.refreshPath)) {
      print('Token refresh in progress, queueing request: ${options.path}');

      final completer = Completer<Response>();
      _pendingRequests.add(_RetryRequest(
        requestOptions: options,
        completer: completer,
      ));

      // Wait for response from retry and return it
      return handler.resolve(await completer.future);
    }

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    return handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    print('=== AppInterceptor onError ===');
    print('Status Code: ${err.response?.statusCode}');
    print('Request Path: ${err.requestOptions.path}');
    print('Request URL: ${err.requestOptions.uri}');
    print('Response Data: ${err.response?.data}');
    print('Current TOKEN: ${storage.read(TOKEN) != null ? "EXISTS" : "NULL"}');
    print(
        'Current REFRESH_TOKEN: ${storage.read(REFRESH_TOKEN) != null ? "EXISTS" : "NULL"}');

    // Check if error is due to token expiration (401 Unauthorized or specific 403 cases)
    if (_shouldAttemptTokenRefresh(err)) {
      print(
          'Token expired, attempting refresh for: ${err.requestOptions.path}');

      // Store the failed request
      final originalRequest = err.requestOptions;
      final completer = Completer<Response>();

      // Add the failed request to the queue
      _pendingRequests.add(_RetryRequest(
        requestOptions: originalRequest,
        completer: completer,
      ));

      // Start token refresh if not already in progress
      if (!_isRefreshing) {
        _refreshCompleter = Completer<bool>();
        bool success = await _refreshToken();

        // After refresh, process pending requests
        if (success) {
          print(
              'Token refreshed, processing ${_pendingRequests.length} pending requests');
          _processPendingRequests();
        } else {
          // If refresh failed, reject all pending requests
          _rejectPendingRequests('Token refresh failed');
          // Note: Logout is now handled directly in the _refreshToken method
          // when the refresh token request itself fails
        }
      }

      try {
        // Wait for the request to be retried and return its response
        final response = await completer.future;
        return handler.resolve(response);
      } catch (e) {
        return handler.reject(DioException(
          requestOptions: originalRequest,
          error: e,
          message: 'Request failed after token refresh',
        ));
      }
    }

    // For all other errors, pass to the next handler
    print('Passing error to next handler: ${err.response?.statusCode}');
    return handler.next(err);
  }

  /// Determine if we should attempt token refresh for this error
  bool _shouldAttemptTokenRefresh(DioException err) {
    final statusCode = err.response?.statusCode;
    final hasRefreshToken = storage.read(REFRESH_TOKEN) != null;
    final hasAccessToken = storage.read(TOKEN) != null;
    final isRefreshPath = err.requestOptions.path.contains(ApiPath.refreshPath);

    print('=== TOKEN REFRESH DEBUG ===');
    print('Status Code: $statusCode');
    print('Has Refresh Token: $hasRefreshToken');
    print('Has Access Token: $hasAccessToken');
    print('Is Refresh Path: $isRefreshPath');
    print('Request Path: ${err.requestOptions.path}');
    print('Response Data: ${err.response?.data}');

    // Don't attempt refresh if this is already a refresh request
    if (isRefreshPath) {
      print('❌ This is already a refresh request');
      return false;
    }

    // For 401/403 errors, always logout regardless of refresh token availability
    if (statusCode == 401 || statusCode == 403) {
      print('🔄 401/403 error detected - performing automatic logout');
      _performLogout();
      return false;
    }

    // For other errors, check if we can attempt refresh
    if (!hasRefreshToken) {
      print('❌ No refresh token available for other errors');
      return false;
    }

    // For other status codes, don't attempt refresh
    print('❌ Status code $statusCode - not attempting refresh');

    print('❌ Status code $statusCode - not attempting refresh');
    return false;
  }

  /// Perform automatic logout by clearing all stored data
  Future<void> _performLogout() async {
    print('🚪 Performing automatic logout due to 401/403 error...');

    // Clear all stored data
    await _logoutService.performCompleteLogout();

    // Navigate to login page immediately
    await _navigationService.navigateToLoginAndClearStack();

    print('✅ Automatic logout and navigation completed');
  }

  // Token refresh logic
  Future<bool> _refreshToken() async {
    if (_isRefreshing) {
      // Wait for the current refresh to complete
      return await _refreshCompleter!.future;
    }

    _isRefreshing = true;

    try {
      // Create a new Dio instance for the refresh request to avoid interceptors
      final refreshDio = Dio(BaseOptions(
        baseUrl:
            storage.read(is_demo) ?? false ? ApiPath.demoUrl : ApiPath.baseUrl,
        connectTimeout: const Duration(seconds: 20),
        receiveTimeout: const Duration(seconds: 20),
        sendTimeout: const Duration(seconds: 20),
      ));

      final refreshToken = storage.read(REFRESH_TOKEN);

      if (refreshToken == null) {
        print('No refresh token available');
        _isRefreshing = false;
        _refreshCompleter?.complete(false);
        return false;
      }

      print('Attempting to refresh token...');

      try {
        final response = await refreshDio.post(
          ApiPath.refreshPath,
          data: {
            'refreshToken': refreshToken,
          },
          options: Options(
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          ),
        );

        if (response.statusCode == 200 && response.data != null) {
          // Save new tokens to GetStorage
          final accessToken = response.data['token'];
          final newRefreshToken = response.data['refreshToken'];

          if (accessToken == null || newRefreshToken == null) {
            print('Token refresh failed: Missing tokens in response');
            _isRefreshing = false;
            _refreshCompleter?.complete(false);
            return false;
          }

          await storage.write(TOKEN, accessToken);
          await storage.write(REFRESH_TOKEN, newRefreshToken);

          print('Token refreshed successfully');
          _isRefreshing = false;
          _refreshCompleter?.complete(true);
          return true;
        } else {
          print(
              'Token refresh failed: Invalid response - Status ${response.statusCode}');
          _isRefreshing = false;
          _refreshCompleter?.complete(false);
          // This is where an error happens with the refresh token request itself
          // - the response came back but was not a success
          // showDanger("Session Expired");
          // clearAndLogout(Get.context!);
          return false;
        }
      } catch (refreshError) {
        // This is a network or server error with the refresh token request itself
        print('Refresh token request failed: $refreshError');
        _isRefreshing = false;
        _refreshCompleter?.complete(false);
        // showDanger("Session Expired");
        // clearAndLogout(Get.context!);
        return false;
      }
    } catch (e) {
      print('Failed to initialize refresh token process: $e');
      _isRefreshing = false;
      _refreshCompleter?.complete(false);
      return false;
    }
  }

  // Process pending requests after successful token refresh
  void _processPendingRequests() {
    print('Processing ${_pendingRequests.length} pending requests');

    final pendingRequests = List<_RetryRequest>.from(_pendingRequests);
    _pendingRequests.clear();

    for (final request in pendingRequests) {
      print('Retrying request: ${request.requestOptions.path}');

      // Update the token in the request
      final token = storage.read(TOKEN);
      request.requestOptions.headers['Authorization'] = 'Bearer $token';

      // Retry the request
      dio.fetch(request.requestOptions).then((response) {
        request.completer.complete(response);
      }).catchError((error) {
        request.completer.completeError(error);
      });
    }
  }

  // Reject all pending requests when token refresh fails
  void _rejectPendingRequests(String reason) {
    print('Rejecting ${_pendingRequests.length} pending requests: $reason');

    final pendingRequests = List<_RetryRequest>.from(_pendingRequests);
    _pendingRequests.clear();

    for (final request in pendingRequests) {
      request.completer.completeError(DioException(
        requestOptions: request.requestOptions,
        type: DioExceptionType.unknown,
        message: reason,
      ));
    }
  }
}

// Helper class to store retry request information
class _RetryRequest {
  final RequestOptions requestOptions;
  final Completer<Response> completer;

  _RetryRequest({
    required this.requestOptions,
    required this.completer,
  });
}
