import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:dio/dio.dart';
import '../mixins/error_handler_mixin.dart';
import '../services/simple_error_handler.dart';

// Example of how to use the error handling mixin in your BLoC

// Events
abstract class ExcusesEvent {}

class LoadExcusesEvent extends ExcusesEvent {
  final int pageKey;
  LoadExcusesEvent(this.pageKey);
}

// States
enum ExcusesStatus { initial, loading, success, failure }

class ExcusesState {
  final ExcusesStatus status;
  final dynamic excuses; // Replace with your actual Excuses model
  final String? message;

  const ExcusesState({
    this.status = ExcusesStatus.initial,
    this.excuses,
    this.message,
  });

  ExcusesState copyWith({
    ExcusesStatus? status,
    dynamic excuses,
    String? message,
  }) {
    return ExcusesState(
      status: status ?? this.status,
      excuses: excuses ?? this.excuses,
      message: message ?? this.message,
    );
  }
}

// BLoC with error handling mixin
class ExcusesBloc extends Bloc<ExcusesEvent, ExcusesState> 
    with ErrorHandlerMixin<ExcusesEvent, ExcusesState> {
  
  final dynamic excusesRemoteDataSource; // Replace with your actual data source

  ExcusesBloc({required this.excusesRemoteDataSource}) : super(const ExcusesState()) {
    on<LoadExcusesEvent>(_onLoadExcuses);
  }

  Future<void> _onLoadExcuses(LoadExcusesEvent event, Emitter<ExcusesState> emit) async {
    // Using the error handling mixin - much cleaner!
    await executeApiCall<dynamic>(
      apiCall: () => excusesRemoteDataSource.getExcuses(page: event.pageKey),
      onLoading: () => emit(state.copyWith(status: ExcusesStatus.loading)),
      onSuccess: (excuses) => emit(state.copyWith(
        status: ExcusesStatus.success,
        excuses: excuses,
      )),
      onFailure: (message) => emit(state.copyWith(
        status: ExcusesStatus.failure,
        message: message,
      )),
    );
  }

  // Alternative: Using the retry mechanism
  Future<void> _onLoadExcusesWithRetry(LoadExcusesEvent event, Emitter<ExcusesState> emit) async {
    await executeWithRetry<dynamic>(
      apiCall: () => excusesRemoteDataSource.getExcuses(page: event.pageKey),
      onLoading: () => emit(state.copyWith(status: ExcusesStatus.loading)),
      onSuccess: (excuses) => emit(state.copyWith(
        status: ExcusesStatus.success,
        excuses: excuses,
      )),
      onFailure: (message) => emit(state.copyWith(
        status: ExcusesStatus.failure,
        message: message,
      )),
      maxRetries: 3,
      retryDelay: const Duration(seconds: 2),
    );
  }

  // Example of handling multiple API calls
  Future<void> _onLoadMultipleData(dynamic event, Emitter<ExcusesState> emit) async {
    await executeParallelApiCalls(
      apiCalls: [
        () => excusesRemoteDataSource.getExcuses(page: 1),
        () => excusesRemoteDataSource.getCategories(),
        () => excusesRemoteDataSource.getUserProfile(),
      ],
      onLoading: () => emit(state.copyWith(status: ExcusesStatus.loading)),
      onSuccess: (results) {
        // results[0] = excuses, results[1] = categories, results[2] = profile
        emit(state.copyWith(
          status: ExcusesStatus.success,
          excuses: results[0],
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: ExcusesStatus.failure,
        message: message,
      )),
    );
  }
}

// Example of the old way vs new way comparison:

/*
OLD WAY (your original code):
```dart
if (await networkInfo.isConnected) {
  try {
    Excuses excuses = await excusesRemoteDataSource.getExcuses(page: event.pageKey);
    emit(state.copyWith(status: ExcusesStatus.success, excuses: excuses));
  } on DioException catch (e) {
    if (e.type == DioExceptionType.badResponse) {
      emit(state.copyWith(status: ExcusesStatus.failure, message: "LocaleKeys.badResponse.tr()"));
    } else if (e.type == DioExceptionType.connectionTimeout) {
      emit(state.copyWith(status: ExcusesStatus.failure, message: "LocaleKeys.connectionTimeout.tr()"));
    } else if (e.type == DioExceptionType.receiveTimeout) {
      emit(state.copyWith(status: ExcusesStatus.failure, message: "LocaleKeys.receiveTimeout.tr()"));
    } else if (e.type == DioExceptionType.sendTimeout) {
      emit(state.copyWith(status: ExcusesStatus.failure, message: "LocaleKeys.sendTimeout.tr()"));
    } else if (e.type == DioExceptionType.cancel) {
      emit(state.copyWith(status: ExcusesStatus.failure, message: "LocaleKeys.requestCancelled.tr()"));
    } else if (e.type == DioExceptionType.unknown) {
      emit(state.copyWith(status: ExcusesStatus.failure, message: "LocaleKeys.unexpectedError.tr()"));
    }
    CustomToast.showToast(e.response.toString());
  } catch (e) {
    CustomToast.showToast(e.toString());
    emit(state.copyWith(status: ExcusesStatus.failure, message: "LocaleKeys.unexpectedError.tr()"));
  }
} else {
  emit(state.copyWith(status: ExcusesStatus.failure, message: LocaleKeys.check_internet_connection.tr()));
}
```

NEW WAY (using the error handling mixin):
```dart
await executeApiCall<Excuses>(
  apiCall: () => excusesRemoteDataSource.getExcuses(page: event.pageKey),
  onLoading: () => emit(state.copyWith(status: ExcusesStatus.loading)),
  onSuccess: (excuses) => emit(state.copyWith(status: ExcusesStatus.success, excuses: excuses)),
  onFailure: (message) => emit(state.copyWith(status: ExcusesStatus.failure, message: message)),
);
```

Benefits of the new approach:
1. ✅ Much cleaner and more readable code
2. ✅ Consistent error handling across all BLoCs
3. ✅ Automatic network connectivity checking
4. ✅ Built-in retry mechanism
5. ✅ Proper error message localization
6. ✅ Support for parallel and sequential API calls
7. ✅ Validation error extraction
8. ✅ Easy to maintain and update error handling logic
*/
