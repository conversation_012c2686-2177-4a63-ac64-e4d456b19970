import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'app_colors.dart';

/// App text styles using Euclid Circular A font family
/// This class contains all text style definitions used throughout the app
class AppTextStyles {
  // Private constructor to prevent instantiation
  AppTextStyles._();

  // Font family constant
  static const String _fontFamily = 'EuclidCircularA';

  // Display text styles (largest)
  static TextStyle displayLarge = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 57.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    color: AppColors.primaryText,
    height: 1.12,
  );

  static TextStyle displayMedium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 45.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.primaryText,
    height: 1.16,
  );

  static TextStyle displaySmall = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 36.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.primaryText,
    height: 1.22,
  );

  // Headline text styles
  static TextStyle headlineLarge = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 32.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.primaryText,
    height: 1.25,
  );

  static TextStyle headlineMedium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 28.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.primaryText,
    height: 1.29,
  );

  static TextStyle headlineSmall = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 24.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    color: AppColors.primaryText,
    height: 1.33,
  );

  // Title text styles
  static TextStyle titleLarge = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 22.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0,
    color: AppColors.primaryText,
    height: 1.27,
  );

  static TextStyle titleMedium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
    color: AppColors.primaryText,
    height: 1.50,
  );

  static TextStyle titleSmall = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    color: AppColors.primaryText,
    height: 1.43,
  );

  // Label text styles
  static TextStyle labelLarge = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    color: AppColors.primaryText,
    height: 1.43,
  );

  static TextStyle labelMedium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    color: AppColors.primaryText,
    height: 1.33,
  );

  static TextStyle labelSmall = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 11.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    color: AppColors.primaryText,
    height: 1.45,
  );

  // Body text styles
  static TextStyle bodyLarge = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    color: AppColors.primaryText,
    height: 1.50,
  );

  static TextStyle bodyMedium = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 14.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    color: AppColors.primaryText,
    height: 1.43,
  );

  static TextStyle bodySmall = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    color: AppColors.primaryText,
    height: 1.33,
  );

  // Custom app-specific text styles
  static TextStyle buttonText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 16.sp,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    color: AppColors.white,
    height: 1.25,
  );

  static TextStyle captionText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 12.sp,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    color: AppColors.secondaryText,
    height: 1.33,
  );

  static TextStyle overlineText = TextStyle(
    fontFamily: _fontFamily,
    fontSize: 10.sp,
    fontWeight: FontWeight.w500,
    letterSpacing: 1.5,
    color: AppColors.secondaryText,
    height: 1.6,
  );

  // Utility methods for creating variations
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size.sp);
  }

  // Common text style variations
  static TextStyle get primaryBodyText => bodyMedium;
  static TextStyle get secondaryBodyText => withColor(bodyMedium, AppColors.secondaryText);
  static TextStyle get errorText => withColor(bodySmall, AppColors.error);
  static TextStyle get successText => withColor(bodySmall, AppColors.success);
  static TextStyle get warningText => withColor(bodySmall, AppColors.warning);
  static TextStyle get infoText => withColor(bodySmall, AppColors.info);

  // Bold variations
  static TextStyle get boldHeadline => withWeight(headlineMedium, FontWeight.w700);
  static TextStyle get boldTitle => withWeight(titleMedium, FontWeight.w700);
  static TextStyle get boldBody => withWeight(bodyMedium, FontWeight.w600);

  // Light variations
  static TextStyle get lightHeadline => withWeight(headlineMedium, FontWeight.w300);
  static TextStyle get lightTitle => withWeight(titleMedium, FontWeight.w300);
  static TextStyle get lightBody => withWeight(bodyMedium, FontWeight.w300);
}
