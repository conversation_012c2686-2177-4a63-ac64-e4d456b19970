import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_colors.dart';
import 'app_text_styles.dart';

/// Main app theme configuration
/// This class provides the complete theme data for the app
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();

  /// Dark theme (default theme)
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      fontFamily: 'EuclidCircularA',

      // Color scheme
      colorScheme: const ColorScheme.dark(
        primary: AppColors.cFirstColor,
        onPrimary: AppColors.white,
        secondary: AppColors.cGreenishColor,
        onSecondary: AppColors.white,
        tertiary: AppColors.cCarrotColor,
        onTertiary: AppColors.white,
        error: AppColors.cReddishColor,
        onError: AppColors.white,
        surface: Color(0xFF2C2C2E), // Lighter gray for dialogs in dark theme
        onSurface: AppColors.white,
        surfaceContainerHighest: Color(0xFF3A3A3C),
        onSurfaceVariant: AppColors.cTextGrayColor,
        outline: AppColors.cGrayBorderColor,
        outlineVariant: AppColors.borderLight,
        shadow: AppColors.shadow,
        scrim: AppColors.overlay,
        inverseSurface: AppColors.white,
        onInverseSurface: AppColors.black,
        inversePrimary: AppColors.cFirstColor,
      ),

      // Scaffold theme
      scaffoldBackgroundColor: AppColors.cBackgroundColor,

      // App bar theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.cBackgroundColor,
        foregroundColor: AppColors.primaryText,
        elevation: 0,
        scrolledUnderElevation: 0,
        centerTitle: true,
        titleTextStyle: AppTextStyles.titleLarge,
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),

      // Text theme
      textTheme: TextTheme(
        displayLarge: AppTextStyles.displayLarge,
        displayMedium: AppTextStyles.displayMedium,
        displaySmall: AppTextStyles.displaySmall,
        headlineLarge: AppTextStyles.headlineLarge,
        headlineMedium: AppTextStyles.headlineMedium,
        headlineSmall: AppTextStyles.headlineSmall,
        titleLarge: AppTextStyles.titleLarge,
        titleMedium: AppTextStyles.titleMedium,
        titleSmall: AppTextStyles.titleSmall,
        labelLarge: AppTextStyles.labelLarge,
        labelMedium: AppTextStyles.labelMedium,
        labelSmall: AppTextStyles.labelSmall,
        bodyLarge: AppTextStyles.bodyLarge,
        bodyMedium: AppTextStyles.bodyMedium,
        bodySmall: AppTextStyles.bodySmall,
      ),

      // Card theme - no shadows in dark theme
      cardTheme: CardThemeData(
        color: AppColors.cCardsColor,
        shadowColor: Colors.transparent,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(8),
      ),

      // Elevated button theme - no shadows in dark theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.cFirstColor,
          foregroundColor: AppColors.white,
          textStyle: AppTextStyles.buttonText,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // Outlined button theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.cFirstColor,
          textStyle: AppTextStyles.buttonText,
          side: const BorderSide(color: AppColors.cFirstColor, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // Text button theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.cFirstColor,
          textStyle: AppTextStyles.buttonText,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.cCardsColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cFirstColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cReddishColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cReddishColor, width: 2),
        ),
        labelStyle: AppTextStyles.bodyMedium,
        hintStyle: AppTextStyles.secondaryBodyText,
        errorStyle: AppTextStyles.errorText,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),

      // Icon theme
      iconTheme: const IconThemeData(
        color: AppColors.primaryText,
        size: 24,
      ),

      // Primary icon theme
      primaryIconTheme: const IconThemeData(
        color: AppColors.cFirstColor,
        size: 24,
      ),

      // Divider theme
      dividerTheme: const DividerThemeData(
        color: AppColors.border,
        thickness: 1,
        space: 1,
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.cCardsColor,
        selectedItemColor: AppColors.cFirstColor,
        unselectedItemColor: AppColors.secondaryText,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // Tab bar theme
      tabBarTheme: TabBarThemeData(
        labelColor: AppColors.cFirstColor,
        unselectedLabelColor: AppColors.secondaryText,
        labelStyle: AppTextStyles.labelLarge,
        unselectedLabelStyle: AppTextStyles.labelMedium,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: AppColors.cFirstColor, width: 2),
        ),
      ),

      // Floating action button theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColors.cFirstColor,
        foregroundColor: AppColors.white,
        elevation: 6,
        shape: CircleBorder(),
      ),

      // Switch theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor;
          }
          return AppColors.secondaryText;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor.withOpacity(0.5);
          }
          return AppColors.border;
        }),
      ),

      // Checkbox theme
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor;
          }
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(AppColors.white),
        side: const BorderSide(color: AppColors.border, width: 2),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),

      // Radio theme
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor;
          }
          return AppColors.border;
        }),
      ),

      // Slider theme
      sliderTheme: SliderThemeData(
        activeTrackColor: AppColors.cFirstColor,
        inactiveTrackColor: AppColors.border,
        thumbColor: AppColors.cFirstColor,
        overlayColor: Color(0x1A0065FF),
        valueIndicatorColor: AppColors.cFirstColor,
        valueIndicatorTextStyle: AppTextStyles.labelSmall,
      ),

      // Progress indicator theme
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppColors.cFirstColor,
        linearTrackColor: AppColors.border,
        circularTrackColor: AppColors.border,
      ),

      // Snack bar theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.cCardsColor,
        contentTextStyle: AppTextStyles.bodyMedium,
        actionTextColor: AppColors.cFirstColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),

      // Dialog theme - Dark theme
      dialogTheme: DialogThemeData(
        backgroundColor: AppColors.cCardsColor, // Same as surface color
        titleTextStyle: AppTextStyles.titleLarge.copyWith(color: AppColors.white),
        contentTextStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),

      // Bottom sheet theme
      bottomSheetTheme: const BottomSheetThemeData(
        backgroundColor: AppColors.cCardsColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
      ),
    );
  }

  /// Light theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      fontFamily: 'EuclidCircularA',

      // Color scheme
      colorScheme: const ColorScheme.light(
        primary: AppColors.cFirstColor,
        onPrimary: AppColors.white,
        secondary: AppColors.cGreenishColor,
        onSecondary: AppColors.white,
        tertiary: AppColors.cCarrotColor,
        onTertiary: AppColors.white,
        error: AppColors.cReddishColor,
        onError: AppColors.white,
        surface: AppColors.white, // Pure white for dialogs in light theme
        onSurface: AppColors.black,
        surfaceContainerHighest: Color(0xFFF5F5F5),
        onSurfaceVariant: AppColors.cLightTextGrayColor,
        outline: AppColors.cLightBorderColor,
        outlineVariant: AppColors.cLightBorderColor,
        shadow: AppColors.cardShadowLight,
        scrim: AppColors.overlayLight,
        inverseSurface: AppColors.black,
        onInverseSurface: AppColors.white,
        inversePrimary: AppColors.cFirstColor,
      ),

      // Scaffold theme
      scaffoldBackgroundColor: AppColors.cLightBackgroundColor,

      // App bar theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.cLightBackgroundColor,
        foregroundColor: AppColors.black,
        elevation: 0,
        scrolledUnderElevation: 0,
        centerTitle: true,
        titleTextStyle: AppTextStyles.titleLarge.copyWith(color: AppColors.black),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
      ),

      // Text theme
      textTheme: TextTheme(
        displayLarge: AppTextStyles.displayLarge.copyWith(color: AppColors.black),
        displayMedium: AppTextStyles.displayMedium.copyWith(color: AppColors.black),
        displaySmall: AppTextStyles.displaySmall.copyWith(color: AppColors.black),
        headlineLarge: AppTextStyles.headlineLarge.copyWith(color: AppColors.black),
        headlineMedium: AppTextStyles.headlineMedium.copyWith(color: AppColors.black),
        headlineSmall: AppTextStyles.headlineSmall.copyWith(color: AppColors.black),
        titleLarge: AppTextStyles.titleLarge.copyWith(color: AppColors.black),
        titleMedium: AppTextStyles.titleMedium.copyWith(color: AppColors.black),
        titleSmall: AppTextStyles.titleSmall.copyWith(color: AppColors.black),
        labelLarge: AppTextStyles.labelLarge.copyWith(color: AppColors.black),
        labelMedium: AppTextStyles.labelMedium.copyWith(color: AppColors.black),
        labelSmall: AppTextStyles.labelSmall.copyWith(color: AppColors.black),
        bodyLarge: AppTextStyles.bodyLarge.copyWith(color: AppColors.black),
        bodyMedium: AppTextStyles.bodyMedium.copyWith(color: AppColors.black),
        bodySmall: AppTextStyles.bodySmall.copyWith(color: AppColors.black),
      ),

      // Card theme with beautiful shadows
      cardTheme: CardThemeData(
        color: AppColors.cLightCardsColor,
        shadowColor: AppColors.cardShadowLight,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: const EdgeInsets.all(8),
      ),

      // Elevated button theme with beautiful shadows
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.cFirstColor,
          foregroundColor: AppColors.white,
          textStyle: AppTextStyles.buttonText,
          elevation: 6,
          shadowColor: AppColors.primaryShadowLight,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // Outlined button theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.cFirstColor,
          textStyle: AppTextStyles.buttonText,
          side: const BorderSide(color: AppColors.cFirstColor, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        ),
      ),

      // Text button theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.cFirstColor,
          textStyle: AppTextStyles.buttonText,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
      ),

      // Input decoration theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.cLightCardsColor,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cLightBorderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cLightBorderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cFirstColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cReddishColor),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.cReddishColor, width: 2),
        ),
        labelStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.cLightTextGrayColor),
        hintStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.cLightTextGrayColor),
        errorStyle: AppTextStyles.bodySmall.copyWith(color: AppColors.cReddishColor),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),

      // Icon theme
      iconTheme: const IconThemeData(
        color: AppColors.black,
        size: 24,
      ),

      // Primary icon theme
      primaryIconTheme: const IconThemeData(
        color: AppColors.cFirstColor,
        size: 24,
      ),

      // Divider theme
      dividerTheme: const DividerThemeData(
        color: AppColors.cLightBorderColor,
        thickness: 1,
        space: 1,
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.cLightCardsColor,
        selectedItemColor: AppColors.cFirstColor,
        unselectedItemColor: AppColors.cLightTextGrayColor,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // Tab bar theme
      tabBarTheme: TabBarThemeData(
        labelColor: AppColors.cFirstColor,
        unselectedLabelColor: AppColors.cLightTextGrayColor,
        labelStyle: AppTextStyles.labelLarge,
        unselectedLabelStyle: AppTextStyles.labelMedium,
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(color: AppColors.cFirstColor, width: 2),
        ),
      ),

      // Floating action button theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColors.cFirstColor,
        foregroundColor: AppColors.white,
        elevation: 8,
        shape: CircleBorder(),
      ),

      // Switch theme
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor;
          }
          return AppColors.cLightTextGrayColor;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor.withOpacity(0.5);
          }
          return AppColors.cLightBorderColor;
        }),
      ),

      // Checkbox theme
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor;
          }
          return Colors.transparent;
        }),
        checkColor: MaterialStateProperty.all(AppColors.white),
        side: const BorderSide(color: AppColors.cLightBorderColor, width: 2),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      ),

      // Radio theme
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.cFirstColor;
          }
          return AppColors.cLightBorderColor;
        }),
      ),

      // Slider theme
      sliderTheme: SliderThemeData(
        activeTrackColor: AppColors.cFirstColor,
        inactiveTrackColor: AppColors.cLightBorderColor,
        thumbColor: AppColors.cFirstColor,
        overlayColor: Color(0x1A0065FF),
        valueIndicatorColor: AppColors.cFirstColor,
        valueIndicatorTextStyle: AppTextStyles.labelSmall.copyWith(color: AppColors.white),
      ),

      // Progress indicator theme
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppColors.cFirstColor,
        linearTrackColor: AppColors.cLightBorderColor,
        circularTrackColor: AppColors.cLightBorderColor,
      ),

      // Snack bar theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.cLightCardsColor,
        contentTextStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.black),
        actionTextColor: AppColors.cFirstColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(8)),
        ),
      ),

      // Dialog theme - Light theme
      dialogTheme: DialogThemeData(
        backgroundColor: AppColors.white, // Same as surface color
        titleTextStyle: AppTextStyles.titleLarge.copyWith(color: AppColors.black),
        contentTextStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.black),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
      ),

      // Bottom sheet theme
      bottomSheetTheme: const BottomSheetThemeData(
        backgroundColor: AppColors.cLightCardsColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
      ),
    );
  }
}
