import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../translations/locale_keys.g.dart';

/// Service for handling image loading errors and providing fallback mechanisms
class ImageErrorHandler {
  // Private constructor to prevent instantiation
  ImageErrorHandler._();

  /// Check if the error is related to Android ImageDecoder
  static bool isAndroidImageDecoderError(Object error) {
    if (!Platform.isAndroid) return false;
    
    final errorString = error.toString().toLowerCase();
    return errorString.contains('imagedecoder') ||
           errorString.contains('failed to create image decoder') ||
           errorString.contains('unimplemented') ||
           errorString.contains('input contained an error');
  }

  /// Check if the error is a network-related error
  static bool isNetworkError(Object error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('timeout') ||
           errorString.contains('unreachable') ||
           errorString.contains('no internet') ||
           errorString.contains('socketexception');
  }

  /// Check if the error is related to invalid/corrupted image data
  static bool isCorruptedImageError(Object error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('invalid image data') ||
           errorString.contains('corrupt') ||
           errorString.contains('malformed') ||
           errorString.contains('invalid format') ||
           errorString.contains('unsupported format');
  }

  /// Check if the error is a 404 or similar HTTP error
  static bool isHttpError(Object error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('404') ||
           errorString.contains('not found') ||
           errorString.contains('403') ||
           errorString.contains('forbidden') ||
           errorString.contains('401') ||
           errorString.contains('unauthorized') ||
           errorString.contains('500') ||
           errorString.contains('server error');
  }

  /// Get a user-friendly error message based on the error type
  static String getUserFriendlyMessage(Object error) {
    if (isAndroidImageDecoderError(error)) {
      return LocaleKeys.image_errors_unsupported_format.tr();
    } else if (isNetworkError(error)) {
      return LocaleKeys.image_errors_network_error.tr();
    } else if (isCorruptedImageError(error)) {
      return LocaleKeys.image_errors_corrupted_image.tr();
    } else if (isHttpError(error)) {
      return LocaleKeys.image_errors_image_not_found.tr();
    } else {
      return LocaleKeys.image_errors_loading_error.tr();
    }
  }

  /// Determine if the error should trigger a retry
  static bool shouldRetry(Object error) {
    // Don't retry for Android ImageDecoder errors (format issues)
    if (isAndroidImageDecoderError(error)) return false;
    
    // Don't retry for 404 errors
    if (isHttpError(error) && error.toString().contains('404')) return false;
    
    // Don't retry for corrupted image data
    if (isCorruptedImageError(error)) return false;
    
    // Retry for network errors and other temporary issues
    return isNetworkError(error) || 
           error.toString().contains('timeout') ||
           error.toString().contains('500');
  }

  /// Get retry delay based on error type
  static Duration getRetryDelay(Object error, int retryCount) {
    if (isNetworkError(error)) {
      // Exponential backoff for network errors
      return Duration(seconds: (retryCount + 1) * 2);
    } else {
      // Standard delay for other errors
      return Duration(seconds: retryCount + 1);
    }
  }

  /// Validate image URL format
  static bool isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    
    // Check for common image extensions
    final imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];

    // If URL has query parameters, check the path part
    final pathPart = url.split('?').first.toLowerCase();

    return imageExtensions.any((ext) => pathPart.endsWith(ext)) ||
           url.startsWith('http://') ||
           url.startsWith('https://') ||
           url.startsWith('data:image/');
  }

  /// Clean and normalize image URL
  static String? cleanImageUrl(String? url) {
    if (url == null || url.isEmpty) return null;
    
    // Remove extra whitespace
    url = url.trim();
    
    // Handle relative URLs
    if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('data:')) {
      // This will be handled by RobustNetworkImage
      return url;
    }
    
    return url;
  }

  /// Log image error for debugging
  static void logImageError(String? imageUrl, Object error, StackTrace? stackTrace) {
    if (kDebugMode) {
      print('=== IMAGE ERROR ===');
      print('URL: $imageUrl');
      print('Error: $error');
      print('Error Type: ${_getErrorType(error)}');
      print('Should Retry: ${shouldRetry(error)}');
      if (stackTrace != null) {
        print('Stack Trace: $stackTrace');
      }
      print('==================');
    }
  }

  static String _getErrorType(Object error) {
    if (isAndroidImageDecoderError(error)) return 'Android ImageDecoder';
    if (isNetworkError(error)) return 'Network';
    if (isCorruptedImageError(error)) return 'Corrupted Image';
    if (isHttpError(error)) return 'HTTP';
    return 'Unknown';
  }

  /// Preload image to check if it's valid
  static Future<bool> preloadImage(String imageUrl) async {
    try {
      // This is a simple check - in a real app you might want to make a HEAD request
      // to check if the image exists without downloading it
      return isValidImageUrl(imageUrl);
    } catch (e) {
      return false;
    }
  }

  /// Get fallback image URLs for different scenarios
  static List<String> getFallbackUrls(String originalUrl) {
    final fallbacks = <String>[];
    
    // Try different image sizes if the URL contains size parameters
    if (originalUrl.contains('w=') || originalUrl.contains('width=')) {
      // Try smaller sizes
      fallbacks.add(originalUrl.replaceAll(RegExp(r'w=\d+'), 'w=400'));
      fallbacks.add(originalUrl.replaceAll(RegExp(r'width=\d+'), 'width=400'));
    }
    
    // Try different formats
    if (originalUrl.endsWith('.webp')) {
      fallbacks.add(originalUrl.replaceAll('.webp', '.jpg'));
      fallbacks.add(originalUrl.replaceAll('.webp', '.png'));
    }
    
    return fallbacks;
  }
}
