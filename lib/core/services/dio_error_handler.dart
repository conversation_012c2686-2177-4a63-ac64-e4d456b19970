import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../translations/locale_keys.g.dart';
import '../utils/app_functions.dart';

/// Universal Dio error handling service
/// Provides consistent error handling and user-friendly messages across the app
class DioErrorHandler {
  /// Handle Dio exceptions and return appropriate error messages
  static String handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return LocaleKeys.errors_connection_timeout.tr();

      case DioExceptionType.sendTimeout:
        return LocaleKeys.errors_send_timeout.tr();

      case DioExceptionType.receiveTimeout:
        return LocaleKeys.errors_receive_timeout.tr();

      case DioExceptionType.badResponse:
        return _handleBadResponse(error);

      case DioExceptionType.cancel:
        return LocaleKeys.errors_request_cancelled.tr();

      case DioExceptionType.connectionError:
        return LocaleKeys.errors_connection_error.tr();

      case DioExceptionType.badCertificate:
        return LocaleKeys.errors_server_error.tr();

      case DioExceptionType.unknown:
      default:
        return LocaleKeys.errors_generic_error.tr(namedArgs: {'error': error.message ?? 'Unknown'});
    }
  }

  /// Handle bad response errors based on status code
  static String _handleBadResponse(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    // Try to extract error message from response
    String? serverMessage;
    if (responseData is Map<String, dynamic>) {
      serverMessage = responseData['message'] ?? 
                     responseData['error'] ?? 
                     responseData['detail'];
    }

    switch (statusCode) {
      case 400:
        return serverMessage ?? LocaleKeys.errors_bad_request_detailed.tr();

      case 401:
        return serverMessage ?? LocaleKeys.errors_unauthorized_detailed.tr();

      case 403:
        return serverMessage ?? LocaleKeys.errors_forbidden_detailed.tr();

      case 404:
        return serverMessage ?? LocaleKeys.errors_not_found_detailed.tr();

      case 409:
        return serverMessage ?? LocaleKeys.errors_conflict.tr();

      case 422:
        return serverMessage ?? LocaleKeys.errors_validation_error.tr();

      case 429:
        return serverMessage ?? LocaleKeys.errors_too_many_requests.tr();

      case 500:
        return serverMessage ?? LocaleKeys.errors_internal_server_detailed.tr();

      case 502:
        return serverMessage ?? LocaleKeys.errors_bad_gateway.tr();

      case 503:
        return serverMessage ?? LocaleKeys.errors_service_unavailable.tr();

      default:
        return serverMessage ?? LocaleKeys.errors_server_error_with_code.tr(namedArgs: {'code': statusCode.toString()});
    }
  }

  /// Handle any exception and return appropriate error message
  static String handleGenericError(dynamic error) {
    if (error is DioException) {
      return handleDioError(error);
    } else {
      return LocaleKeys.errors_generic_error.tr(namedArgs: {'error': error.toString()});
    }
  }

  /// Show error message as toast and return the message
  static String handleErrorWithToast(dynamic error, {required BuildContext context}) {
    final message = handleGenericError(error);
    AppFunctions.showErrorSnackBar(context, message);
    return message;
  }

  /// Show error message as snackbar (requires context)
  static void showErrorSnackBar(dynamic error, BuildContext context) {
    final message = handleGenericError(error);
    AppFunctions.showErrorSnackBar(context, message);
  }

  /// Extract validation errors from 422 response
  static Map<String, List<String>> extractValidationErrors(DioException error) {
    final Map<String, List<String>> validationErrors = {};
    
    if (error.response?.statusCode == 422 && 
        error.response?.data is Map<String, dynamic>) {
      
      final data = error.response!.data as Map<String, dynamic>;
      
      // Handle Laravel-style validation errors
      if (data.containsKey('errors')) {
        final errors = data['errors'] as Map<String, dynamic>;
        errors.forEach((field, messages) {
          if (messages is List) {
            validationErrors[field] = messages.cast<String>();
          } else if (messages is String) {
            validationErrors[field] = [messages];
          }
        });
      }
      
      // Handle other validation error formats
      else if (data.containsKey('details')) {
        final details = data['details'];
        if (details is Map<String, dynamic>) {
          details.forEach((field, messages) {
            if (messages is List) {
              validationErrors[field] = messages.cast<String>();
            } else if (messages is String) {
              validationErrors[field] = [messages];
            }
          });
        }
      }
    }
    
    return validationErrors;
  }

  /// Check if error is a network connectivity issue
  static bool isNetworkError(dynamic error) {
    if (error is DioException) {
      return error.type == DioExceptionType.connectionError ||
             error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.receiveTimeout ||
             error.type == DioExceptionType.sendTimeout;
    }
    return false;
  }

  /// Check if error requires user re-authentication
  static bool requiresReauth(dynamic error) {
    if (error is DioException) {
      return error.response?.statusCode == 401;
    }
    return false;
  }

  /// Get error code from DioException
  static String? getErrorCode(DioException error) {
    final responseData = error.response?.data;
    if (responseData is Map<String, dynamic>) {
      return responseData['code']?.toString() ?? 
             responseData['error_code']?.toString();
    }
    return null;
  }
}
