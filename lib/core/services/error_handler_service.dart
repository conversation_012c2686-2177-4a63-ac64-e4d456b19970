import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../translations/locale_keys.g.dart';
import '../utils/app_functions.dart';
import 'dio_error_handler.dart';

/// Result wrapper for API calls
class ApiResult<T> {
  final bool isSuccess;
  final T? data;
  final String? errorMessage;
  final int? statusCode;
  final Map<String, List<String>>? validationErrors;

  const ApiResult._({
    required this.isSuccess,
    this.data,
    this.errorMessage,
    this.statusCode,
    this.validationErrors,
  });

  factory ApiResult.success(T data, {int? statusCode}) {
    return ApiResult._(
      isSuccess: true,
      data: data,
      statusCode: statusCode,
    );
  }

  factory ApiResult.failure({
    required String errorMessage,
    int? statusCode,
    Map<String, List<String>>? validationErrors,
  }) {
    return ApiResult._(
      isSuccess: false,
      errorMessage: errorMessage,
      statusCode: statusCode,
      validationErrors: validationErrors,
    );
  }
}

/// Universal error handling service for API calls
class ErrorHandlerService {
  /// Execute API call with comprehensive error handling
  static Future<ApiResult<T>> executeApiCall<T>(
    Future<T> Function() apiCall, {
    BuildContext? context,
    bool showToast = true,
    bool checkNetwork = true,
  }) async {
    try {
      // Check network connectivity if required
      if (checkNetwork) {
        // You can implement network checking here
        // For now, we'll skip this check
      }

      final result = await apiCall();
      return ApiResult.success(result);
    } on DioException catch (e) {
      final errorMessage = DioErrorHandler.handleDioError(e);
      final validationErrors = DioErrorHandler.extractValidationErrors(e);

      if (showToast && context != null) {
        AppFunctions.showErrorSnackBar(context, errorMessage);
      }

      return ApiResult.failure(
        errorMessage: errorMessage,
        statusCode: e.response?.statusCode,
        validationErrors: validationErrors.isNotEmpty ? validationErrors : null,
      );
    } catch (e) {
      final errorMessage = DioErrorHandler.handleGenericError(e);

      if (showToast && context != null) {
        AppFunctions.showErrorSnackBar(context, errorMessage);
      }

      return ApiResult.failure(errorMessage: errorMessage);
    }
  }

  /// Execute API call and emit BLoC states
  static Future<void> executeWithBlocStates<T>({
    required Future<T> Function() apiCall,
    required Function(T data) onSuccess,
    required Function(String message) onError,
    BuildContext? context,
    bool showToast = false, // Don't show toast for BLoC as it handles UI
  }) async {
    final result = await executeApiCall(
      apiCall,
      context: context,
      showToast: showToast,
    );

    if (result.isSuccess && result.data != null) {
      onSuccess(result.data as T);
    } else {
      onError(result.errorMessage ?? LocaleKeys.errors_unknown_error.tr());
    }
  }

  /// Handle pagination API calls
  static Future<ApiResult<T>> executePaginatedApiCall<T>(
    Future<T> Function() apiCall, {
    BuildContext? context,
    bool showToast = true,
  }) async {
    return executeApiCall(
      apiCall,
      context: context,
      showToast: showToast,
    );
  }

  /// Handle file upload with progress
  static Future<ApiResult<T>> executeFileUpload<T>(
    Future<T> Function() uploadCall, {
    BuildContext? context,
    Function(double progress)? onProgress,
    bool showToast = true,
  }) async {
    try {
      final result = await uploadCall();
      return ApiResult.success(result);
    } on DioException catch (e) {
      final errorMessage = DioErrorHandler.handleDioError(e);

      if (showToast && context != null) {
        AppFunctions.showErrorSnackBar(context, errorMessage);
      }

      return ApiResult.failure(
        errorMessage: errorMessage,
        statusCode: e.response?.statusCode,
      );
    } catch (e) {
      final errorMessage = 'Fayl yuklashda xatolik: ${e.toString()}';

      if (showToast && context != null) {
        AppFunctions.showErrorSnackBar(context, errorMessage);
      }

      return ApiResult.failure(errorMessage: errorMessage);
    }
  }

  /// Check if should retry the request
  static bool shouldRetry(ApiResult result) {
    if (!result.isSuccess && result.statusCode != null) {
      // Retry for server errors but not client errors
      return result.statusCode! >= 500;
    }
    return false;
  }

  /// Execute with retry logic
  static Future<ApiResult<T>> executeWithRetry<T>(
    Future<T> Function() apiCall, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 1),
    BuildContext? context,
    bool showToast = true,
  }) async {
    ApiResult<T>? lastResult;

    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      lastResult = await executeApiCall(
        apiCall,
        context: context,
        showToast: attempt == maxRetries
            ? showToast
            : false, // Only show toast on final attempt
      );

      if (lastResult.isSuccess || !shouldRetry(lastResult)) {
        break;
      }

      if (attempt < maxRetries) {
        await Future.delayed(retryDelay * (attempt + 1)); // Exponential backoff
      }
    }

    return lastResult!;
  }
}
