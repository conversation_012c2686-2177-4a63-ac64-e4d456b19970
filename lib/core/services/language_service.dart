import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

/// Service for managing language selection and persistence
class LanguageService {
  static const String _languageKey = 'selected_language';
  static final GetStorage _storage = GetStorage();

  /// Get the currently selected language code
  static String getCurrentLanguage() {
    return _storage.read(_languageKey) ?? 'uz';
  }

  /// Set the language and persist it
  static Future<void> setLanguage(BuildContext context, String languageCode) async {
    await _storage.write(_languageKey, languageCode);
    
    // Update the app locale
    final locale = Locale(languageCode);
    await context.setLocale(locale);
  }

  /// Get the display name for a language code
  static String getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'uz':
        return 'O\'zbek';
      case 'ru':
        return 'Русский';
      default:
        return 'O\'zbek';
    }
  }

  /// Get all supported languages
  static List<Map<String, String>> getSupportedLanguages() {
    return [
      {'code': 'uz', 'name': 'O\'zbek'},
      {'code': 'ru', 'name': 'Русский'},
    ];
  }

  /// Initialize language on app startup
  static Future<void> initializeLanguage(BuildContext context) async {
    final savedLanguage = getCurrentLanguage();
    final locale = Locale(savedLanguage);
    await context.setLocale(locale);
  }

  /// Reset language to default (Uzbek) - used during logout
  static Future<void> resetToDefault(BuildContext? context) async {
    await _storage.write(_languageKey, 'uz');

    if (context != null) {
      final locale = const Locale('uz');
      await context.setLocale(locale);
    }
  }
}
