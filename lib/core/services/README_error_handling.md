# Universal Dio Error Handling Service

This package provides a comprehensive error handling solution for Dio HTTP client in Flutter applications.

## Components

### 1. DioErrorHandler
Core service for handling Dio exceptions and converting them to user-friendly messages.

### 2. ErrorHandlerService
High-level service for executing API calls with automatic error handling.

### 3. ErrorHandlerMixin
BLoC mixin that provides error handling methods for consistent error management in BLoC classes.

## Usage Examples

### Basic API Call with Error Handling

```dart
// Using ErrorHandlerService
final result = await ErrorHandlerService.executeApiCall<UserModel>(
  () => userRepository.getUser(userId),
  context: context,
  showToast: true,
);

if (result.isSuccess) {
  // Handle success
  final user = result.data!;
  print('User loaded: ${user.name}');
} else {
  // Handle error
  print('Error: ${result.errorMessage}');
}
```

### Using with BLoC (Recommended)

```dart
class UserBloc extends Bloc<UserEvent, UserState> 
    with ErrorHandlerMixin<UserEvent, UserState> {
  
  final UserRepository userRepository;

  UserBloc({required this.userRepository}) : super(UserInitial()) {
    on<LoadUserEvent>(_onLoadUser);
  }

  Future<void> _onLoadUser(LoadUserEvent event, Emitter<UserState> emit) async {
    await executeApiCall<UserModel>(
      apiCall: () => userRepository.getUser(event.userId),
      onLoading: () => emit(UserLoading()),
      onSuccess: (user) => emit(UserLoaded(user)),
      onFailure: (message) => emit(UserError(message)),
    );
  }
}
```

### Handling Multiple API Calls

```dart
// Parallel execution
await executeParallelApiCalls(
  apiCalls: [
    () => userRepository.getUser(userId),
    () => postRepository.getUserPosts(userId),
    () => friendRepository.getUserFriends(userId),
  ],
  onLoading: () => emit(LoadingState()),
  onSuccess: (results) {
    final user = results[0] as UserModel;
    final posts = results[1] as List<PostModel>;
    final friends = results[2] as List<UserModel>;
    emit(AllDataLoaded(user, posts, friends));
  },
  onFailure: (message) => emit(ErrorState(message)),
);

// Sequential execution
await executeSequentialApiCalls(
  apiCalls: [
    () => authRepository.refreshToken(),
    () => userRepository.getUser(userId),
    () => analyticsRepository.trackUserView(userId),
  ],
  onSuccess: (results) => emit(SuccessState()),
  onFailure: (message) => emit(ErrorState(message)),
  stopOnFirstError: true,
);
```

### Retry Mechanism

```dart
await executeWithRetry<UserModel>(
  apiCall: () => userRepository.getUser(userId),
  onLoading: () => emit(UserLoading()),
  onSuccess: (user) => emit(UserLoaded(user)),
  onFailure: (message) => emit(UserError(message)),
  maxRetries: 3,
  retryDelay: const Duration(seconds: 2),
);
```

### File Upload with Progress

```dart
final result = await ErrorHandlerService.executeFileUpload<UploadResponse>(
  () => fileRepository.uploadFile(
    file,
    onProgress: (progress) => print('Upload progress: ${progress * 100}%'),
  ),
  context: context,
  onProgress: (progress) {
    // Update UI with upload progress
    emit(FileUploadProgress(progress));
  },
);
```

### Validation Error Handling

```dart
// In your BLoC
Future<void> _onSubmitForm(SubmitFormEvent event, Emitter<FormState> emit) async {
  await executeApiCall<void>(
    apiCall: () => formRepository.submitForm(event.formData),
    onSuccess: (_) => emit(FormSubmitted()),
    onFailure: (message) {
      // Check if it's a validation error
      if (state is FormError && (state as FormError).validationErrors != null) {
        emit(FormValidationError((state as FormError).validationErrors!));
      } else {
        emit(FormError(message));
      }
    },
  );
}
```

## Error Types Handled

### Network Errors
- Connection timeout
- Send timeout
- Receive timeout
- Connection error
- No internet connection

### HTTP Status Codes
- 400: Bad Request
- 401: Unauthorized (triggers re-authentication)
- 403: Forbidden
- 404: Not Found
- 409: Conflict
- 422: Validation Error (extracts field-specific errors)
- 429: Too Many Requests
- 500: Internal Server Error
- 502: Bad Gateway
- 503: Service Unavailable

### Custom Error Messages
All error messages are in Uzbek language and user-friendly.

## Configuration

### Network Connectivity Check
Override the `isNetworkConnected()` method in your BLoC:

```dart
@override
Future<bool> isNetworkConnected() async {
  final connectivityResult = await Connectivity().checkConnectivity();
  return connectivityResult != ConnectivityResult.none;
}
```

### Authentication Error Handling
Override the `handleAuthError()` method:

```dart
@override
void handleAuthError() {
  // Clear tokens and navigate to login
  authRepository.logout();
  navigator.pushAndRemoveUntil(
    MaterialPageRoute(builder: (_) => LoginPage()),
    (route) => false,
  );
}
```

## Benefits

1. **Consistent Error Handling**: All API calls use the same error handling logic
2. **User-Friendly Messages**: Automatic conversion of technical errors to readable messages
3. **Reduced Boilerplate**: Eliminates repetitive try-catch blocks
4. **Network Awareness**: Automatic network connectivity checking
5. **Retry Logic**: Built-in retry mechanism for network failures
6. **Validation Support**: Automatic extraction of validation errors
7. **BLoC Integration**: Seamless integration with BLoC pattern
8. **Localization Ready**: Easy to extend for multiple languages

## Migration from Old Code

### Before (Your Original Code)
```dart
if (await networkInfo.isConnected) {
  try {
    Excuses excuses = await excusesRemoteDataSource.getExcuses(page: event.pageKey);
    emit(state.copyWith(status: ExcusesStatus.success, excuses: excuses));
  } on DioException catch (e) {
    // 20+ lines of error handling...
  } catch (e) {
    // More error handling...
  }
} else {
  emit(state.copyWith(status: ExcusesStatus.failure, message: "No internet"));
}
```

### After (Using Error Handler)
```dart
await executeApiCall<Excuses>(
  apiCall: () => excusesRemoteDataSource.getExcuses(page: event.pageKey),
  onLoading: () => emit(state.copyWith(status: ExcusesStatus.loading)),
  onSuccess: (excuses) => emit(state.copyWith(status: ExcusesStatus.success, excuses: excuses)),
  onFailure: (message) => emit(state.copyWith(status: ExcusesStatus.failure, message: message)),
);
```

**Result**: 25+ lines reduced to 5 lines with better error handling!
