import 'package:flutter/material.dart';
import '../../main_naz_navigation_page.dart';
import '../../main_sot_navigation_page.dart';
import '../widgets/role_switcher.dart';

class MainAppWrapper extends StatefulWidget {
  final UserRole initialRole;

  const MainAppWrapper({
    super.key,
    this.initialRole = UserRole.nazoratchi,
  });

  @override
  State<MainAppWrapper> createState() => _MainAppWrapperState();
}

class _MainAppWrapperState extends State<MainAppWrapper>
    with SingleTickerProviderStateMixin {
  late UserRole _currentRole;
  late AnimationController _transitionController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _currentRole = widget.initialRole;
    
    _transitionController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _transitionController,
      curve: Curves.easeInOut,
    ));

    _transitionController.forward();
  }

  @override
  void dispose() {
    _transitionController.dispose();
    super.dispose();
  }

  void _handleRoleChange(UserRole newRole) {
    if (newRole == _currentRole) return;

    // Animate transition for role changes only
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _transitionController,
      curve: Curves.easeInOut,
    ));

    setState(() {
      _currentRole = newRole;
    });

    _transitionController.reset();
    _transitionController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: _buildCurrentRoleNavigation(),
        );
      },
    );
  }

  Widget _buildCurrentRoleNavigation() {
    switch (_currentRole) {
      case UserRole.nazoratchi:
        return MainNazNavigationPage(
          onRoleChanged: _handleRoleChange,
        );
      case UserRole.sotuvchi:
        return MainSotNavigationPage(
          onRoleChanged: _handleRoleChange,
        );
    }
  }
}
