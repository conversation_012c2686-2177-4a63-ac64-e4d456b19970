// All sizes
const double cRadius8 = 8.0;
const double cRadius10 = 10.0;
const double cRadius12 = 12.0;
const double cRadius16 = 16.0;
const double cRadius22 = 22.0;
const double cRadius36 = 36.0;

const double HEADER_SIZE = 120;
const double DESIGN_WIDTH = 375;
const double DESIGN_HEIGHT = 812;
const double HEADER_SIZE_SUB = 40;

// Variables
String APP_VERSION = "app_version";

//Contacts
const String TOKEN = 'token';
const String REFRESH_TOKEN = 'refreshToken';
const String USER_ROLE = 'user_role';
const String USER_ID = 'user_id';
const String USER_PROFILE = 'user_profile';
const String MARKET_ID = 'market_id';
const String is_demo = "is_demo";

// Storage keys
const String local_approved = "local_approved";
const String server_approved = "server_approved";
const String functional_live = 'functional_live';

// System checks
const String is_time_correct = "is_time_correct";
const String is_gps_active = "is_gps_active";
const String is_not_mocked = "is_not_mocked";

// Configuration values
const String MAX_IDENTIFY = "0.8";
const String MAX_LIVENESS = "0.7";

// Environment constants
const bool EMULATOR = true;
const String baseUrlPref = "base_url_pref";
const String language_pref = "language_pref";

// Configuration values
const int ALLOWED_MINUTE = 3;

// Event messages
const String EVENT_MESSAGE_DEFAULT_UZ = "Vaqt o'zgartirildi!";
const String EVENT_MESSAGE_SUCCESS_UZ = "Vaqt o'zgartirildi";
const String EVENT_MESSAGE_SUCCESS_RU = "Время изменено";
const String EVENT_MESSAGE_SUCCESS_QQ = "Время изменено";
const String ERROR = "ERROR";
const String STREAM_COMPLETE = "STREAM_COMPLETE";

// Support contact information
const String SUPPORT_TEL1 = "+998 90 123 45 67";
const String SUPPORT_TEL2 = "+998 90 765 43 21";
const String TELEGRAM_URL = "https://t.me/support";

// API Configuration
const String GUEST_TOKEN =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2ODcxMDA3ZTI5Y2YyNzdlNWU0OTgwODgiLCJyb2xlIjoic3VwZXJ2aXNvciIsImlhdCI6MTc1MjMwMzQ3NSwiZXhwIjoxNzUyOTA4Mjc1fQ.nfHaTSZzjolrmkkm8JKEyTgHH0OAB6cimi8fexTocqo";
const String API_BASE_URL = "https://softlab.uz/api/v1";
const String RASTALAR_HARD_ENDPOINT =
    "/mobile/place?pavilion=686f93f97a45e2d46aa07100&block=6870980ac980fa0eb4c472ae";

class AppStrings {
  static const strNoRouteFound = "no_route_found";
  static const strAppName = "app_name";

  static const String success = "success";

  // error handler
  static const String strBadRequestError = "bad_request_error";
  static const String strNoContent = "no_content";
  static const String strForbiddenError = "forbidden_error";
  static const String strUnauthorizedError = "unauthorized_error";
  static const String strNotFoundError = "not_found_error";
  static const String strConflictError = "conflict_error";
  static const String strInternalServerError = "internal_server_error";
  static const String strUnknownError = "unknown_error";
  static const String strTimeoutError = "timeout_error";
  static const String strDefaultError = "default_error";
  static const String strCacheError = "cache_error";
  static const String strNoInternetError = "no_internet_error";

  ///FaceTokens

  static const String androidFaceToken =
      "j63rQnZifPT82LEDGFa+wzorKx+M55JQlNr+S0bFfvMULrNYt+UEWIsa11V/Wk1bU9Srti0/FQqp"
      "UczeCxFtiEcABmZGuTzNd27XnwXHUSIMaFOkrpNyNE4MHb7HBm5kU/0J/SAMfybICCWyFajuZ4fL"
      "agozJV5DPKj22oFVaueWMjO/9fMvcps4u1AIiHH2rjP4mEYfiAE8nhHBa1Ou3u/WkXj6jdDafyJo"
      "AFtQHYJYKDU+hcbtCZ3P1f8y1JB5JxOf92ItK4euAt6/OFG9jGfKpo/Fs2mAgwxH3HoWMLJQ16Iy"
      "u2K6boMyDxRQtBJFTiktuJ+ltlay+dVqIi3Jpg==";

  static const String iOSFaceToken =
      "nWsdDhTp12Ay5yAm4cHGqx2rfEv0U+Wyq/tDPopH2yz6RqyKmRU+eovPeDcAp3T3IJJYm2LbPSEz"
      "+e+YlQ4hz+1n8BNlh2gHo+UTVll40OEWkZ0VyxkhszsKN+3UIdNXGaQ6QL0lQunTwfamWuDNx7Ss"
      "efK/3IojqJAF0Bv7spdll3sfhE1IO/m7OyDcrbl5hkT9pFhFA/iCGARcCuCLk4A6r3mLkK57be4r"
      "T52DKtyutnu0PDTzPeaOVZRJdF0eifYXNvhE41CLGiAWwfjqOQOHfKdunXMDqF17s+LFLWwkeNAD"
      "PKMT+F/kRCjnTcC8WPX3bgNzyUBGsFw9fcneKA==";
}
