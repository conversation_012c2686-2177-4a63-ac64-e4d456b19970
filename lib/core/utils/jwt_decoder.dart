import 'dart:convert';

/// JWT token decoder utility
class JwtDecoder {
  /// Decode JWT token and extract payload
  static Map<String, dynamic>? decodeToken(String token) {
    try {
      // Split the token into parts
      final parts = token.split('.');
      if (parts.length != 3) {
        return null;
      }

      // Get the payload part (second part)
      String payload = parts[1];
      
      // Add padding if needed for base64 decoding
      switch (payload.length % 4) {
        case 0:
          break;
        case 2:
          payload += '==';
          break;
        case 3:
          payload += '=';
          break;
        default:
          return null;
      }

      // Decode base64
      final bytes = base64Url.decode(payload);
      final jsonString = utf8.decode(bytes);
      
      // Parse JSON
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      print('Error decoding JWT token: $e');
      return null;
    }
  }

  /// Check if token is expired
  static bool isTokenExpired(String token) {
    final payload = decodeToken(token);
    if (payload == null) return true;

    final exp = payload['exp'];
    if (exp == null) return true;

    final expirationDate = DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    return DateTime.now().isAfter(expirationDate);
  }

  /// Extract user role from token
  static String? getUserRole(String token) {
    final payload = decodeToken(token);
    return payload?['role'] as String?;
  }

  /// Extract user ID from token
  static String? getUserId(String token) {
    final payload = decodeToken(token);
    return payload?['_id'] as String?;
  }
}
