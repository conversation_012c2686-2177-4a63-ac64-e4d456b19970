/// API Path constants for the application
class ApiPath {
  // Base URLs for different environments
  static const String baseUrl = 'https://softlab.uz/api/v1/';
  static const String baseUrlFile = 'https://softlab.uz/files/';
  // static const String devBaseUrl = 'https://softlab.uz/api/v1/';
  // static const String localBaseUrl = 'https://softlab.uz/api/v1/';
  //
  // static const String prodUrl = "https://softlab.uz/api/v1/";
  // static const String prodFileBaseUrl = "https://softlab.uz/api/v1/file/";
  // static const String testUrl = "https://softlab.uz/api/v1/";
  // static const String testFileBaseUrl = "https://softlab.uz/api/v1/file/";
  static const String demoUrl = "https://softlab.uz/api/v1/";
  static const String refreshPath = "/mobile/auth/refresh-token";
  static const String rastalarBlocks = '/page/blocks';
  static const String pavilionPath = 'mobile/place/pavilion';
  static const String freePlacePath = 'mobile/place/freePlace';
  static const String placeCheckPath = 'mobile/place/check';

  //Api Path Nazoratchi
  static const String nazLoginPath = 'mobile/auth/supervisor';
  static const String nazAuthPath = 'mobile/auth/supervisor/verify';
  static const String supervisorProfilePath = 'mobile/supervisor/getOne';
  static const String supervisorUpdatePath = 'mobile/supervisor/update';

  // Statistics API paths
  static const String planStatisticsPath = 'mobile/report/plan';
  static const String placeStatisticsPath = 'mobile/report/place';
  static const String paymentStatisticsPath = 'mobile/report/payment';
  static const String dailyPaymentHistoryPath = 'mobile/payment/daily';
  static const String debtPath = 'mobile/place/debt';

  //Api Path Sotuvchi
  static const String sotLoginPath = 'mobile/auth/seller';
  static const String sotAuthPath = 'mobile/auth/seller/verify';
  static const String sellerProfilePath = 'mobile/seller/getOne';
  static const String sellerUpdatePath = 'mobile/seller/update';
  static const String freePlacesPath = 'mobile/place/freePlace/seller';
  static const String paymentHistoryPath = 'mobile/payment/seller';

  // Helper methods
  static String getRastalarBlock(String blockId) => '$rastalarBlocks/$blockId';
}
