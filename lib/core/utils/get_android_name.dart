String getAndroidName(int apiLevel) {
  String androidVersion;

  switch (apiLevel) {
    case 9:
      androidVersion = "Android 2.3 - 2.3.2 Gingerbread";
      break;
    case 10:
      androidVersion = "Android 2.3.3 - 2.3.7 Gingerbread";
      break;
    case 11:
      androidVersion = "Android 3.0 Honeycomb";
      break;
    case 12:
      androidVersion = "Android 3.1 Honeycomb";
      break;
    case 13:
      androidVersion = "Android 3.2 Honeycomb";
      break;
    case 14:
      androidVersion = "Android 4.0 - 4.0.2 Ice Cream Sandwich";
      break;
    case 15:
      androidVersion = "Android 4.0.3 - 4.0.4 Ice Cream Sandwich";
      break;
    case 16:
      androidVersion = "Android 4.1 Jelly Bean";
      break;
    case 17:
      androidVersion = "Android 4.2 Jelly Bean";
      break;
    case 18:
      androidVersion = "Android 4.3 Jelly Bean";
      break;
    case 19:
      androidVersion = "Android 4.4 KitKat";
      break;
    case 20:
      androidVersion = "Android 4.4W KitKat";
      break;
    case 21:
      androidVersion = "Android 5.0 Lollipop";
      break;
    case 22:
      androidVersion = "Android 5.1 Lollipop";
      break;
    case 23:
      androidVersion = "Android 6.0 Marshmallow";
      break;
    case 24:
      androidVersion = "Android 7.0 Nougat";
      break;
    case 25:
      androidVersion = "Android 7.1 Nougat";
      break;
    case 26:
      androidVersion = "Android 8.0 Oreo";
      break;
    case 27:
      androidVersion = "Android 8.1 Oreo";
      break;
    case 28:
      androidVersion = "Android 9 Pie";
      break;
    case 29:
      androidVersion = "Android 10";
      break;
    case 30:
      androidVersion = "Android 11";
      break;
    case 31:
      androidVersion = "Android 12";
      break;
    case 32:
      androidVersion = "Android 12.1";
      break;
    case 33:
      androidVersion = "Android 12.2";
      break;
    case 34:
      androidVersion = "Android 13";
      break;
    default:
      androidVersion = apiLevel.toString();
      break;
  }

  return androidVersion;
}