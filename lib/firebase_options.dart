// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCPzSUTAXbu-e_Q-GcS3aYyA-TxZ-t6Wiw',
    appId: '1:13243199731:web:f4788fae9ecbcd91840786',
    messagingSenderId: '13243199731',
    projectId: 'mobile-premium-bazaar',
    authDomain: 'mobile-premium-bazaar.firebaseapp.com',
    storageBucket: 'mobile-premium-bazaar.firebasestorage.app',
    measurementId: 'G-P6EGNJDXSH',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCwYpch8_mG3RXUFtrcZbBWfJnV-AqwSKo',
    appId: '1:13243199731:android:53128e258f9b2029840786',
    messagingSenderId: '13243199731',
    projectId: 'mobile-premium-bazaar',
    storageBucket: 'mobile-premium-bazaar.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCrWI6OnGtpaUMiOQcrxnc-EWJw9mFgs7A',
    appId: '1:13243199731:ios:9805806b03c6e949840786',
    messagingSenderId: '13243199731',
    projectId: 'mobile-premium-bazaar',
    storageBucket: 'mobile-premium-bazaar.firebasestorage.app',
    iosBundleId: 'com.example.clickBazaar',
  );
}
