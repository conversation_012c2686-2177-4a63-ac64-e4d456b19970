import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:camera/camera.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gal/gal.dart';
import 'package:gap/gap.dart';
import 'package:image_editor/image_editor.dart' hide ImageSource;
// Add image_picker to pubspec.yaml
import 'package:image_picker/image_picker.dart';
import 'package:mno_zoom_widget/zoom_widget.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_compress/video_compress.dart';
import 'package:zoom_tap_animation/zoom_tap_animation.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../core/function/functions.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/utils/app_constants.dart';
import '../../core/widgets/custom_toast.dart';
import '../../di/dependency_injection.dart';
import '../../translations/locale_keys.g.dart';

// A screen that allows users to take a picture using a given camera.
class TakePictureScreen extends StatefulWidget {
  final List<CameraDescription> cameras;
  final bool isVideo;
  final bool isHD;
  final bool isFront;
  final String? previewMessage;

  const TakePictureScreen(
      {Key? key,
        required this.cameras,
        required this.isVideo,
        required this.isHD,
        required this.isFront,
        this.previewMessage})
      : super(key: key);

  @override
  TakePictureScreenState createState() => TakePictureScreenState();
}

class TakePictureScreenState extends State<TakePictureScreen> {
  CameraController? _controller;
  Future<void>? _initializeControllerFuture;
  SharedPreferences? prefs = di();

  final ValueNotifier<bool> _buttonLoading = ValueNotifier<bool>(false);
  bool isFlashOn = false;
  bool isFrontOn = false;
  int turns = 0; // Device orientation for image rotation

  // Settings variables
  bool _isHDQuality = true;
  bool _showGridLines = false;
  bool _autoSaveToGallery = true;
  String _imageFormat = 'JPEG'; // JPEG or PNG

  final ImagePicker _picker = ImagePicker();

  Future<void> initCamera(CameraDescription camera) async {
    // To display the current output from the Camera,
    // create a CameraController.
    _controller = CameraController(
      // Get a specific camera from the list of available cameras.
      camera,
      // Define the resolution to use.
      _isHDQuality ? ResolutionPreset.high : ResolutionPreset.medium,
      imageFormatGroup: ImageFormatGroup.yuv420,
    );

    // Next, initialize the controller. This returns a Future.
    _initializeControllerFuture = _controller?.initialize().then((value) {
      /// Guess, this causes error sometimes
      _controller?.setFlashMode(FlashMode.off);
      _controller?.lockCaptureOrientation(DeviceOrientation.portraitUp);
    });
  }

  Future<void> _loadSettings() async {
    if (prefs != null) {
      setState(() {
        _isHDQuality = prefs!.getBool('hd_quality') ?? true;
        _showGridLines = prefs!.getBool('show_grid_lines') ?? false;
        _autoSaveToGallery = prefs!.getBool('auto_save_gallery') ?? true;
        _imageFormat = prefs!.getString('image_format') ?? 'JPEG';
      });
    }
  }

  Future<void> _saveSettings() async {
    if (prefs != null) {
      await prefs!.setBool('hd_quality', _isHDQuality);
      await prefs!.setBool('show_grid_lines', _showGridLines);
      await prefs!.setBool('auto_save_gallery', _autoSaveToGallery);
      await prefs!.setString('image_format', _imageFormat);
    }
  }

  Future<File> _mirrorPhoto(XFile image) async {
    Uint8List? imageBytes = await image.readAsBytes();

    // 1. flip the image on the X axis
    final ImageEditorOption option = ImageEditorOption();
    option.addOption(FlipOption(horizontal: true));
    imageBytes = await ImageEditor.editImage(
        image: imageBytes, imageEditorOption: option);

    // 2. write the image back to disk
    await File(image.path).delete();
    return await File(image.path).writeAsBytes(imageBytes!, flush: true);

    ///This doesn't work on some devices (Samsung)
    // img.Image? originalImage = img.decodeImage(imageBytes);
    // img.Image fixedImage = img.flipHorizontal(originalImage!);
    //
    // File file = File(image.path);
    // return await file.writeAsBytes(
    //   img.encodeJpg(fixedImage),
    //   flush: true,
    // );
  }

  List<String> split(String string, String separator, {int max = 0}) {
    var result = <String>[];

    if (separator.isEmpty) {
      result.add(string);
      return result;
    }

    while (true) {
      var index = string.indexOf(separator, 0);
      if (index == -1 || (max > 0 && result.length >= max)) {
        result.add(string);
        break;
      }

      result.add(string.substring(0, index));
      string = string.substring(index + separator.length);
    }

    return result;
  }

  @override
  void initState() {
    super.initState();
    _loadSettings();

    if (widget.isFront) {
      isFrontOn = true;
      initCamera(widget.cameras.firstWhere(
              (camera) => camera.lensDirection == CameraLensDirection.front));
    } else {
      initCamera(widget.cameras.first);
    }
  }

  @override
  void dispose() {
    // Dispose of the controller when the widget is disposed.
    _controller?.dispose();
    super.dispose();
  }

  /// Pick image from gallery
  Future<void> _pickFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: _isHDQuality ? 100 : 85,
      );

      if (image != null) {
        // Navigate to preview screen with picked image
        final callback = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DisplayPictureScreen(
              message: widget.previewMessage,
              imagePath: image.path,
              orientation: 0, // Gallery images are already oriented correctly
              isFromGallery: true,
            ),
          ),
        );

        if (callback != null) {
          String savedPath = await saveFileToInternalStorage(image.path);
          Navigator.pop(context, savedPath);
        }
      }
    } catch (e) {
      CustomToast.showToast(LocaleKeys.camera_gallery_selection_error.tr(namedArgs: {'error': e.toString()}));
    }
  }

  @override
  Widget build(BuildContext context) {
    var ctx;

    return Scaffold(
      extendBody: true,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          LocaleKeys.camera_title.tr(),
          style: AppTextStyles.titleLarge.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        toolbarHeight: 60.h,
        centerTitle: true,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Theme.of(context).colorScheme.onSurface,
            size: 24.w,
          ),
          onPressed: () => Navigator.pop(context, ''),
        ),
        actions: [
          // Camera flip button
          Container(
            margin: EdgeInsets.only(right: 8.w),
            decoration: BoxDecoration(
              color: AppColors.cCardsColor.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: IconButton(
              icon: Icon(
                isFrontOn ? Icons.camera_rear : Icons.camera_front,
                color: AppColors.white,
                size: 24.w,
              ),
              onPressed: () async {
                if (!isFrontOn) {
                  initCamera(widget.cameras.firstWhere((camera) =>
                  camera.lensDirection == CameraLensDirection.front));
                  setState(() {
                    isFrontOn = true;
                  });
                } else {
                  initCamera(widget.cameras.firstWhere((camera) =>
                  camera.lensDirection == CameraLensDirection.back));
                  setState(() {
                    isFrontOn = false;
                  });
                }
              },
            ),
          ),
          // Flash button
          Container(
            margin: EdgeInsets.only(right: 16.w),
            decoration: BoxDecoration(
              color: AppColors.cCardsColor.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: IconButton(
              icon: Icon(
                isFlashOn ? Icons.flash_on : Icons.flash_off,
                color: isFlashOn ? AppColors.cYellowishColor : AppColors.white,
                size: 24.w,
              ),
              onPressed: () async {
                if (!isFlashOn) {
                  _controller?.setFlashMode(FlashMode.torch);
                  setState(() {
                    isFlashOn = true;
                  });
                } else {
                  _controller?.setFlashMode(FlashMode.off);
                  setState(() {
                    isFlashOn = false;
                  });
                }
              },
            ),
          ),
        ],
      ),
      // Wait until the controller is initialized before displaying the
      // camera preview. Use a FutureBuilder to display a loading spinner
      // until the controller has finished initializing.
      body: NativeDeviceOrientationReader(
        useSensor: true,
        builder: (contextRotation) {
          ctx = contextRotation;

          NativeDeviceOrientation orientation =
          NativeDeviceOrientationReader.orientation(ctx);

          switch (orientation) {
            case NativeDeviceOrientation.landscapeLeft:
              turns = -1;
              break;
            case NativeDeviceOrientation.landscapeRight:
              turns = 1;
              break;
            case NativeDeviceOrientation.portraitDown:
              turns = 2;
              break;
            default:
              turns = 0;
              break;
          }
          final size = MediaQuery.of(context).size;

          return FutureBuilder<void>(
            future: _initializeControllerFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.done) {
                // If the Future is complete, display the preview.
                return Container(
                  width: size.width,
                  height: size.height,
                  decoration: BoxDecoration(
                    color: AppColors.black,
                    borderRadius: BorderRadius.circular(0),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(0),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        // Camera preview
                        FittedBox(
                          fit: BoxFit.cover,
                          child: SizedBox(
                            width: size.width,
                            child: CameraPreview(_controller!),
                          ),
                        ),
                        // Camera overlay with guidelines
                        _buildCameraOverlay(),
                      ],
                    ),
                  ),
                );
              } else {
                // Loading state with theme colors
                return Container(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          color: Theme.of(context).colorScheme.primary,
                          strokeWidth: 3,
                        ),
                        Gap(16.h),
                        Text(
                          LocaleKeys.camera_camera_preparing.tr(),
                          style: AppTextStyles.bodyMedium.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
            },
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: _buildCaptureButton(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// Build camera overlay with guidelines
  Widget _buildCameraOverlay() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.cFirstColor.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          // Grid lines (if enabled)
          if (_showGridLines) _buildGridLines(),
          // Center guidelines
          Center(
            child: Container(
              width: 200.w,
              height: 200.w,
              decoration: BoxDecoration(
                border: Border.all(
                  color: AppColors.white.withValues(alpha: 0.5),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
          // Corner indicators
          Positioned(
            top: 50.h,
            left: 50.w,
            child: _buildCornerIndicator(),
          ),
          Positioned(
            top: 50.h,
            right: 50.w,
            child: Transform.rotate(
              angle: 1.5708, // 90 degrees
              child: _buildCornerIndicator(),
            ),
          ),
          Positioned(
            bottom: 150.h,
            left: 50.w,
            child: Transform.rotate(
              angle: -1.5708, // -90 degrees
              child: _buildCornerIndicator(),
            ),
          ),
          Positioned(
            bottom: 150.h,
            right: 50.w,
            child: Transform.rotate(
              angle: 3.14159, // 180 degrees
              child: _buildCornerIndicator(),
            ),
          ),
        ],
      ),
    );
  }

  /// Build grid lines for rule of thirds
  Widget _buildGridLines() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(
        painter: GridPainter(),
      ),
    );
  }

  /// Build corner indicator for camera overlay
  Widget _buildCornerIndicator() {
    return Container(
      width: 20.w,
      height: 20.w,
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: AppColors.cFirstColor, width: 3),
          left: BorderSide(color: AppColors.cFirstColor, width: 3),
        ),
      ),
    );
  }

  /// Build capture button
  Widget _buildCaptureButton() {
    return ZoomTapAnimation(
      begin: 1,
      end: 0.9,
      child: Container(
        width: 80.w,
        height: 80.w,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.white,
          border: Border.all(
            color: AppColors.cFirstColor,
            width: 4,
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.black.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: ValueListenableBuilder<bool>(
          valueListenable: _buttonLoading,
          builder: (ctx, value, _) {
            if (value) {
              return Center(
                child: CircularProgressIndicator(
                  color: AppColors.cFirstColor,
                  strokeWidth: 3,
                ),
              );
            } else {
              return Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(40.w),
                  onTap: _captureImage,
                  child: Center(
                    child: Container(
                      width: 60.w,
                      height: 60.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.cFirstColor,
                      ),
                      child: Icon(
                        Icons.camera_alt,
                        color: AppColors.white,
                        size: 28.w,
                      ),
                    ),
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  /// Build bottom bar
  Widget _buildBottomBar() {
    return Container(
      height: 100.h,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 24.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // // Gallery button
              // _buildBottomButton(
              //   icon: Icons.photo_library_outlined,
              //   label: LocaleKeys.camera_gallery.tr(),
              //   onTap: _pickFromGallery,
              // ),
              // SizedBox(width: 80.w), // Space for FAB

              // Settings button
              _buildBottomButton(
                icon: Icons.settings_outlined,
                label: LocaleKeys.camera_settings.tr(),
                onTap: () {
                  _showCameraSettings();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build bottom button
  Widget _buildBottomButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: AppColors.cCardsColor,
              borderRadius: BorderRadius.circular(12.r),
              border: Border.all(
                color: AppColors.cGrayBorderColor,
                width: 1,
              ),
            ),
            child: Icon(
              icon,
              color: AppColors.white,
              size: 24.w,
            ),
          ),
          Gap(4.h),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.secondaryText,
              fontSize: 10.sp,
            ),
          ),
        ],
      ),
    );
  }

  /// Capture image method
  Future<void> _captureImage() async {
    if (_buttonLoading.value) return;

    try {
      _buttonLoading.value = true;

      // Lock focus and exposure for better image quality
      await _controller?.setFocusMode(FocusMode.locked);
      await _controller?.setExposureMode(ExposureMode.locked);

      // Take the picture
      dynamic image = await _controller?.takePicture();

      // Reset focus and exposure
      await _controller?.setFocusMode(FocusMode.auto);
      await _controller?.setExposureMode(ExposureMode.auto);

      _buttonLoading.value = false;

      if (image != null) {
        // Turn off flash after taking picture
        if (isFlashOn) {
          await _controller?.setFlashMode(FlashMode.off);
          setState(() {
            isFlashOn = false;
          });
        }

        // Mirror image if using front camera
        if (isFrontOn) {
          var mirroredFile = await _mirrorPhoto(image);
          image = XFile(mirroredFile.path);
        }

        // Navigate to preview screen
        final callback = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DisplayPictureScreen(
              message: widget.previewMessage,
              imagePath: image.path,
              orientation: turns,
            ),
          ),
        );

        if (callback != null) {
          int turn = callback;
          var modifiedPath = split(image.path, image.name).first +
              "mod" +
              image.name;

          print(turn.toString());

          // Compress and rotate image
          XFile? rotatedResult =
          await FlutterImageCompress.compressAndGetFile(
            image.path,
            modifiedPath,
            keepExif: true,
            quality: _isHDQuality ? 95 : 85,
            rotate: turn == -1
                ? -90
                : turn == 1
                ? 90
                : turn == 2
                ? 180
                : 0,
          );

          var exists = await File(rotatedResult?.path ?? '').exists();

          if (rotatedResult != null && exists) {
            // Auto save to gallery if enabled
            if (_autoSaveToGallery) {
              await Gal.putImage(rotatedResult.path);
            }
            String savedPath = await saveFileToInternalStorage(rotatedResult.path);
            print("Cache file: ${rotatedResult.path}");
            print("Saved file: ${savedPath}");
            Navigator.pop(context, savedPath);
          } else {
            Navigator.pop(context, image.path);
            CustomToast.showToast("Rotated result doesn't exist!");
          }
        }
      }
    } catch (e) {
      _buttonLoading.value = false;
      CustomToast.showToast(LocaleKeys.camera_capture_error.tr(namedArgs: {'error': e.toString()}));
    }
  }

  /// Show camera settings bottom sheet
  void _showCameraSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          padding: EdgeInsets.all(24.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20.r),
              topRight: Radius.circular(20.r),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              Gap(20.h),
              Text(
                LocaleKeys.camera_settings_title.tr(),
                style: AppTextStyles.titleMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Gap(24.h),

              // HD Quality setting
              _buildSettingToggle(
                title: LocaleKeys.camera_hd_quality.tr(),
                subtitle: LocaleKeys.camera_hd_quality_subtitle.tr(),
                icon: Icons.high_quality,
                value: _isHDQuality,
                onChanged: (value) {
                  setModalState(() {
                    _isHDQuality = value;
                  });
                  setState(() {
                    _isHDQuality = value;
                  });
                  _saveSettings();
                  // Reinitialize camera with new quality
                  if (isFrontOn) {
                    initCamera(widget.cameras.firstWhere(
                            (camera) => camera.lensDirection == CameraLensDirection.front));
                  } else {
                    initCamera(widget.cameras.first);
                  }
                },
              ),
              Gap(16.h),

              // Grid lines setting
              _buildSettingToggle(
                title: LocaleKeys.camera_grid_lines.tr(),
                subtitle: LocaleKeys.camera_grid_lines_subtitle.tr(),
                icon: Icons.grid_on,
                value: _showGridLines,
                onChanged: (value) {
                  setModalState(() {
                    _showGridLines = value;
                  });
                  setState(() {
                    _showGridLines = value;
                  });
                  _saveSettings();
                },
              ),
              Gap(16.h),

              // Auto save to gallery setting
              _buildSettingToggle(
                title: LocaleKeys.camera_auto_save.tr(),
                subtitle: LocaleKeys.camera_auto_save_subtitle.tr(),
                icon: Icons.save_alt,
                value: _autoSaveToGallery,
                onChanged: (value) {
                  setModalState(() {
                    _autoSaveToGallery = value;
                  });
                  setState(() {
                    _autoSaveToGallery = value;
                  });
                  _saveSettings();
                },
              ),
              Gap(16.h),

              // Image format setting
              _buildSettingRow(
                title: LocaleKeys.camera_image_format.tr(),
                subtitle: LocaleKeys.camera_image_format_current.tr(namedArgs: {'format': _imageFormat}),
                icon: Icons.image,
                onTap: () {
                  _showImageFormatDialog(setModalState);
                },
              ),
              Gap(24.h),
            ],
          ),
        ),
      ),
    );
  }

  /// Build setting toggle
  Widget _buildSettingToggle({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: AppColors.cFirstColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              icon,
              color: AppColors.cFirstColor,
              size: 20.w,
            ),
          ),
          Gap(12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(2.h),
                Text(
                  subtitle,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
          CupertinoSwitch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.cFirstColor,
          ),
        ],
      ),
    );
  }

  /// Build setting row
  Widget _buildSettingRow({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                color: AppColors.cFirstColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: AppColors.cFirstColor,
                size: 20.w,
              ),
            ),
            Gap(12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Gap(2.h),
                  Text(
                    subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.secondaryText,
              size: 16.w,
            ),
          ],
        ),
      ),
    );
  }

  /// Show image format selection dialog
  void _showImageFormatDialog(StateSetter setModalState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text(
          LocaleKeys.camera_select_image_format.tr(),
          style: AppTextStyles.titleMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              onTap: () {
                Navigator.pop(context);
                setModalState(() {
                  _imageFormat = 'JPEG';
                });
                setState(() {
                  _imageFormat = 'JPEG';
                });
                _saveSettings();
              },
              title: Text(
                'JPEG',
                style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
              ),
              subtitle: Text(
                LocaleKeys.camera_jpeg_description.tr(),
                style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),
              leading: Radio<String>(
                value: 'JPEG',
                groupValue: _imageFormat,
                onChanged: (value) {
                  Navigator.pop(context);
                  setModalState(() {
                    _imageFormat = value!;
                  });
                  setState(() {
                    _imageFormat = value!;
                  });
                  _saveSettings();
                },
                activeColor: AppColors.cFirstColor,
              ),
            ),
            ListTile(
              onTap: () {
                Navigator.pop(context);
                setModalState(() {
                  _imageFormat = 'PNG';
                });
                setState(() {
                  _imageFormat = 'PNG';
                });
                _saveSettings();
              },
              title: Text(
                'PNG',
                style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
              ),
              subtitle: Text(
                LocaleKeys.camera_png_description.tr(),
                style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),
              leading: Radio<String>(
                value: 'PNG',
                groupValue: _imageFormat,
                onChanged: (value) {
                  Navigator.pop(context);
                  setModalState(() {
                    _imageFormat = value!;
                  });
                  setState(() {
                    _imageFormat = value!;
                  });
                  _saveSettings();
                },
                activeColor: AppColors.cFirstColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              LocaleKeys.camera_cancel.tr(),
              style: TextStyle(color: AppColors.secondaryText),
            ),
          ),
        ],
      ),
    );
  }
}

/// Custom painter for grid lines
class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppColors.white.withValues(alpha: 0.3)
      ..strokeWidth = 1.0;

    // Vertical lines
    canvas.drawLine(
      Offset(size.width / 3, 0),
      Offset(size.width / 3, size.height),
      paint,
    );
    canvas.drawLine(
      Offset(2 * size.width / 3, 0),
      Offset(2 * size.width / 3, size.height),
      paint,
    );

    // Horizontal lines
    canvas.drawLine(
      Offset(0, size.height / 3),
      Offset(size.width, size.height / 3),
      paint,
    );
    canvas.drawLine(
      Offset(0, 2 * size.height / 3),
      Offset(size.width, 2 * size.height / 3),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// A widget that displays the picture taken by the user..\
// If the picture was taken, pop and return callback path.
class DisplayPictureScreen extends StatefulWidget {
  final String imagePath;
  final int orientation;
  final String? message;
  final bool isFromGallery;

  const DisplayPictureScreen({
    Key? key,
    required this.imagePath,
    required this.orientation,
    this.message,
    this.isFromGallery = false,
  }) : super(key: key);

  @override
  State<DisplayPictureScreen> createState() => _DisplayPictureScreenState();
}

class _DisplayPictureScreenState extends State<DisplayPictureScreen> {
  int orientation = 0;

  @override
  void initState() {
    orientation = widget.orientation;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: ZoomTapAnimation(
        begin: 1,
        end: 0.8,
        child: SizedBox(
          height: 55.h,
          width: 55.h,
          child: FloatingActionButton(
            backgroundColor: Theme.of(context).colorScheme.primary,
            shape: CircleBorder(),
            child: Icon(
              Icons.check_circle_outline,
              color: Theme.of(context).colorScheme.onPrimary,
              size: 22.h,
            ),
            onPressed: () {
              Navigator.pop(context, orientation);
            },
          ),
        ),
      ),
      appBar: AppBar(
        title: Text(
          widget.isFromGallery ? LocaleKeys.camera_selected_image.tr() : LocaleKeys.camera_captured_image.tr(),
          style: AppTextStyles.titleLarge.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        toolbarHeight: 60.h,
        centerTitle: true,
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios_rounded,
            color: Theme.of(context).colorScheme.onSurface,
            size: 24.w,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          // Share button
          // Container(
          //   margin: EdgeInsets.only(right: 8.w),
          //   decoration: BoxDecoration(
          //     color: AppColors.cCardsColor.withValues(alpha: 0.8),
          //     borderRadius: BorderRadius.circular(8.r),
          //   ),
          //   child: IconButton(
          //     icon: Icon(
          //       Icons.share,
          //       color: AppColors.white,
          //       size: 24.w,
          //     ),
          //     onPressed: () async {
          //       // TODO: Implement share functionality
          //       CustomToast.showToast('Ulashish funksiyasi tez orada qo\'shiladi');
          //     },
          //   ),
          // ),
          // Save to gallery button (if not from gallery)
          if (!widget.isFromGallery)
            Container(
              margin: EdgeInsets.only(right: 16.w),
              decoration: BoxDecoration(
                color: AppColors.cCardsColor.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.save_alt,
                  color: AppColors.white,
                  size: 24.w,
                ),
                onPressed: () async {
                  try {
                    await Gal.putImage(widget.imagePath);
                    CustomToast.showToast(LocaleKeys.camera_image_saved_to_gallery.tr());
                  } catch (e) {
                    CustomToast.showToast(LocaleKeys.camera_save_error.tr(namedArgs: {'error': e.toString()}));
                  }
                },
              ),
            ),
        ],
      ),
      bottomNavigationBar: BottomAppBar(
        height: 50.h,
        color: Theme.of(context).colorScheme.surface,
        shape: CircularNotchedRectangle(),
        notchMargin: 4.0,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            Spacer(),
            InkWell(
              child: SizedBox(
                height: 32.h,
                width: 32.h,
                child: Icon(
                  Icons.rotate_90_degrees_ccw,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 22.h,
                ),
              ),
              onTap: () {
                setState(() {
                  switch (orientation) {
                    case -1:
                      orientation = 2;
                      break;
                    case 1:
                      orientation = 0;
                      break;
                    case 2:
                      orientation = 1;
                      break;
                    case 0:
                      orientation = -1;
                      break;
                  }
                });
              },
            ),
            Spacer(
              flex: 20,
            ),
            InkWell(
              child: SizedBox(
                height: 32.h,
                width: 32.h,
                child: Icon(
                  Icons.rotate_90_degrees_cw_outlined,
                  color: Colors.white,
                  size: 22.h,
                ),
              ),
              onTap: () {
                setState(() {
                  switch (orientation) {
                    case -1:
                      orientation = 0;
                      break;
                    case 1:
                      orientation = 2;
                      break;
                    case 2:
                      orientation = -1;
                      break;
                    case 0:
                      orientation = 1;
                      break;
                  }
                });
              },
            ),
            Spacer(),
          ],
        ),
      ),
      body: Stack(
        alignment: AlignmentDirectional.topCenter,
        children: [
          Zoom(
            maxZoomWidth: 8000,
            maxZoomHeight: 8000,
            scrollWeight: 10.0,
            canvasColor: AppColors.cFirstColor.withValues(alpha: 0.6),
            centerOnScale: true,
            enableScroll: true,
            doubleTapZoom: true,
            zoomSensibility: 2.3,
            initZoom: 0.0,
            axis: Axis.horizontal,
            child: RotatedBox(
              quarterTurns: orientation,
              child: Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    fit: BoxFit.contain,
                    image: FileImage(File(widget.imagePath)),
                  ),
                ),
              ),
            ),
          ),
          Visibility(
            visible: widget.message != null,
            child: Padding(
              padding: EdgeInsets.all(30.h),
              child: Card(
                color: AppColors.cCardsColor.withValues(alpha: 0.9),
                child: Padding(
                  padding: EdgeInsets.all(12.h),
                  child: Text(
                    widget.message ?? '...',
                    textAlign: TextAlign.center,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ThumbnailViewer extends StatefulWidget {
  final String filePath;

  ThumbnailViewer({Key? key, required this.filePath}) : super(key: key);

  @override
  _ThumbnailViewerState createState() => _ThumbnailViewerState();
}

class _ThumbnailViewerState extends State<ThumbnailViewer> {
  late final thumbnailFile;

  @override
  void dispose() {
    super.dispose();
  }

  Future _initVideoPlayer() async {
    thumbnailFile = await VideoCompress.getFileThumbnail(
      widget.filePath,
      quality: 100, // default(100)
    );
  }

  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: FutureBuilder(
        future: _initVideoPlayer(),
        builder: (context, state) {
          if (state.connectionState == ConnectionState.waiting) {
            return Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
            );
          } else {
            return Stack(
              alignment: Alignment.center,
              fit: StackFit.expand,
              children: [
                Image.file(
                  thumbnailFile,
                  cacheHeight: 200,
                  cacheWidth: 200,
                  fit: BoxFit.cover,
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.3),
                    shape: BoxShape.circle,
                  ),
                  padding: EdgeInsets.all(20.w),
                  child: Icon(
                    Icons.play_arrow,
                    size: 60.h,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            );
          }
        },
      ),
    );
  }
}