import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:auto_sms_verification/auto_sms_verification.dart';
import '../../../core/network/network_info.dart';
import '../../../core/utils/api_path.dart';
import '../../../core/utils/app_constants.dart';
import '../../../core/utils/app_functions.dart';
import '../../../core/widgets/role_switcher.dart';
import '../models/auth_models.dart';
import '../../../translations/locale_keys.g.dart';

/// Authentication service for handling login and SMS verification
class AuthService {
  final Dio _dio;
  final GetStorage _storage;
  final NetworkInfo _networkInfo;

  AuthService({
    required Dio dio,
    required GetStorage storage,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _storage = storage,
        _networkInfo = networkInfo;

  /// Send SMS code to phone number
  Future<AuthResponse> sendSmsCode(LoginRequest request) async {
    try {
      // For demo purposes, always return success
      // In real implementation, this would make an API call
      await Future.delayed(const Duration(milliseconds: 500));

      return AuthResponse(
        success: true,
        message: LocaleKeys.auth_services_sms_sent.tr(),
      );

      /* Real API implementation:
      final response = await _dio.post(
        '${ApiPath.baseUrl}/auth/send-sms',
        data: request.toJson(),
      );

      return AuthResponse.fromJson(response.data);
      */
    } catch (e) {
      return AuthResponse(
        success: false,
        message: '${LocaleKeys.auth_services_error_occurred.tr()}$e',
      );
    }
  }

  /// Send SMS code for resend functionality using real API
  Future<AuthResponse> resendSmsCode(
      String phoneNumber, UserRole userRole) async {
    try {
      // Check network connectivity first
      if (!await _networkInfo.isConnected) {
        return AuthResponse(
          success: false,
          message: LocaleKeys.auth_services_no_internet.tr(),
        );
      }

      // Extract phone number without formatting for API call
      String cleanPhoneNumber =
          phoneNumber.trim().replaceAll('+998 ', '').replaceAll(' ', '');

      // Get app signature for Android
      String appSignature = 'undefined';
      try {
        appSignature = await AutoSmsVerification.appSignature() ?? 'undefined';
      } catch (e) {
        // Ignore error and use default signature
        appSignature = 'undefined';
      }

      // Use the same API endpoints as login flow
      final response = await _dio.post(
        userRole == UserRole.nazoratchi
            ? ApiPath.nazLoginPath
            : ApiPath.sotLoginPath,
        data: {
          'phone': cleanPhoneNumber,
          'appSignature': appSignature,
        },
      );

      final data = response.data;
      if (response.statusCode == 200) {
        if (data.containsKey('message') && data['message'] == 'success') {
          return AuthResponse(
            success: true,
            message: LocaleKeys.auth_services_sms_resent.tr(),
          );
        } else {
          return AuthResponse(
            success: false,
            message: data['message'] ?? LocaleKeys.auth_services_sms_send_error.tr(),
          );
        }
      } else {
        return AuthResponse(
          success: false,
          message: LocaleKeys.auth_services_server_error.tr(),
        );
      }
    } on DioException catch (e) {
      // Handle Dio exceptions
      String errorMessage = LocaleKeys.auth_services_general_error.tr();
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = LocaleKeys.auth_services_connection_timeout.tr();
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = LocaleKeys.auth_services_receive_timeout.tr();
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = LocaleKeys.auth_services_connection_error.tr();
      } else if (e.response?.statusCode == 400) {
        errorMessage = LocaleKeys.auth_services_bad_request.tr();
      } else if (e.response?.statusCode == 500) {
        errorMessage = LocaleKeys.auth_services_server_error.tr();
      }

      return AuthResponse(
        success: false,
        message: errorMessage,
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: LocaleKeys.auth_services_unexpected_error.tr(),
      );
    }
  }

  /// Verify SMS code and authenticate user
  Future<AuthResponse> verifySmsCode(SmsVerificationRequest request) async {
    try {
      // For demo purposes, accept any 6-digit code
      if (request.smsCode.length == 6) {
        await Future.delayed(const Duration(milliseconds: 500));

        // Store demo token
        await _storage.write(
            TOKEN, 'demo_token_${DateTime.now().millisecondsSinceEpoch}');
        await _storage.write(is_demo, true);

        return AuthResponse(
          success: true,
          token: 'demo_token',
          message: LocaleKeys.auth_services_login_success.tr(),
        );
      } else {
        return AuthResponse(
          success: false,
          message: LocaleKeys.auth_services_invalid_sms_code.tr(),
        );
      }

      /* Real API implementation:
      final response = await _dio.post(
        '${ApiPath.baseUrl}/auth/verify-sms',
        data: request.toJson(),
      );

      final authResponse = AuthResponse.fromJson(response.data);
      
      if (authResponse.success && authResponse.token != null) {
        await _storage.write(TOKEN, authResponse.token);
        if (authResponse.refreshToken != null) {
          await _storage.write(REFRESH_TOKEN, authResponse.refreshToken);
        }
      }

      return authResponse;
      */
    } catch (e) {
      return AuthResponse(
        success: false,
        message: '${LocaleKeys.auth_services_error_occurred.tr()}$e',
      );
    }
  }

  /// Demo login without SMS verification (true guest mode)
  Future<AuthResponse> demoLogin() async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));

      // Clear any existing tokens for true guest mode
      await _storage.remove(TOKEN);
      await _storage.remove(REFRESH_TOKEN);
      // Set demo flag to indicate guest mode
      await _storage.write(is_demo, true);

      return AuthResponse(
        success: true,
        token: null, // No token for true guest mode
        message: LocaleKeys.auth_services_demo_login.tr(),
      );
    } catch (e) {
      return AuthResponse(
        success: false,
        message: '${LocaleKeys.auth_services_error_occurred.tr()}$e',
      );
    }
  }

  /// Check if user is authenticated
  bool get isAuthenticated {
    return _storage.read(TOKEN) != null;
  }

  /// Check if in demo mode
  bool get isDemoMode {
    return _storage.read(is_demo) ?? false;
  }

  // /// Logout user (basic logout - for compatibility)
  // Future<void> logout() async {
  //   await _storage.remove(TOKEN);
  //   await _storage.remove(REFRESH_TOKEN);
  //   await _storage.remove(is_demo);
  //   await _storage.remove(USER_ROLE);
  //   await _storage.remove(USER_ID);
  //   await _storage.remove(USER_PROFILE);
  //   await _storage.remove(MARKET_ID);
  // }
  //
  // /// Complete logout with full storage erasure
  // Future<void> completeLogout() async {
  //   try {
  //     await _storage.erase();
  //   } catch (e) {
  //     // Fallback to individual key removal
  //     await logout();
  //   }
  // }
}
