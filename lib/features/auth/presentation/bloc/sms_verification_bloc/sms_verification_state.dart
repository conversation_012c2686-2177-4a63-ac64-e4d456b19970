part of 'sms_verification_bloc.dart';

enum SmsVerificationStatus { initial, loading, success, failure }

class SmsVerificationState extends Equatable {
  final SmsVerificationStatus status;
  final String? message;
  final String? token;
  final String? userRole;
  final String? userId;

  const SmsVerificationState({
    this.status = SmsVerificationStatus.initial,
    this.message,
    this.token,
    this.userRole,
    this.userId,
  });

  SmsVerificationState copyWith({
    SmsVerificationStatus? status,
    String? message,
    String? token,
    String? userRole,
    String? userId,
  }) {
    return SmsVerificationState(
      status: status ?? this.status,
      message: message ?? this.message,
      token: token ?? this.token,
      userRole: userRole ?? this.userRole,
      userId: userId ?? this.userId,
    );
  }

  @override
  List<Object?> get props => [status, message, token, userRole, userId];
}
