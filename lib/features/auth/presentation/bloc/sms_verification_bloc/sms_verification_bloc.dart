import 'package:bloc/bloc.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:click_bazaar/di/dependency_injection.dart';
import 'package:dio/dio.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:click_bazaar/core/services/logout_service.dart';
import 'package:click_bazaar/features/auth/datasources/sms_verification_remote_datasource.dart';
import 'package:equatable/equatable.dart';
import 'package:get_storage/get_storage.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../translations/locale_keys.g.dart';

part 'sms_verification_event.dart';
part 'sms_verification_state.dart';

class SmsVerificationBloc extends Bloc<SmsVerificationEvent, SmsVerificationState>
    with ErrorHandlerMixin<SmsVerificationEvent, SmsVerificationState> {
  final NetworkInfo networkInfo;
  final SmsVerificationRemoteDatasourceImpl smsVerificationRemoteDatasource;
  final GetStorage storage;

  SmsVerificationBloc({
    required this.networkInfo,
    required this.smsVerificationRemoteDatasource,
    required this.storage,
  }) : super(const SmsVerificationState()) {
    on<VerifySmsCodeEvent>(_handleSmsVerification);
  }

  Future<void> _handleSmsVerification(
      VerifySmsCodeEvent event, Emitter<SmsVerificationState> emit) async {
    emit(state.copyWith(status: SmsVerificationStatus.loading));

    try {
      var response = await smsVerificationRemoteDatasource.verifySmsCode(
          event.phone, event.verifyCode,event.userRole);
      
      print("SMS Verification Result: $response");
      
      if (response['success'] == true) {
        // Store token and user info in GetStorage
        final token = response['token'] as String;
        final refreshToken = response['refreshToken'] as String?;
        final userRole = response['userRole'] as String?;
        final userId = response['userId'] as String?;

        await storage.write(TOKEN, token);

        // Store refresh token if available
        if (refreshToken != null) {
          await storage.write(REFRESH_TOKEN, refreshToken);
          print('Refresh token stored successfully');
        } else {
          print('No refresh token provided by API');
        }

        if (userRole != null) {
          await storage.write(USER_ROLE, userRole);
        }
        if (userId != null) {
          await storage.write(USER_ID, userId);
        }
        
        emit(state.copyWith(
          status: SmsVerificationStatus.success,
          token: token,
          userRole: userRole,
          userId: userId,
          message: response['message'] as String?,
        ));
      } else {
        emit(state.copyWith(
          status: SmsVerificationStatus.failure,
          message: response['message'] as String? ?? LocaleKeys.auth_sms_verification_verification_failed.tr(),
        ));
      }
    } on DioException catch (e) {
      // Use universal Dio error handling
      final errorMessage = SimpleErrorHandler.handleError(e);
      emit(state.copyWith(
        status: SmsVerificationStatus.failure,
        message: errorMessage,
      ));

      // Handle specific cases
      if (SimpleErrorHandler.requiresReauth(e)) {
        // Handle logout if needed using LogoutService
        final logoutService = LogoutService(storage: storage, prefs: di());
        await logoutService.performCompleteLogout();
      }
    } catch (e) {
      emit(state.copyWith(
        status: SmsVerificationStatus.failure,
        message: '${LocaleKeys.auth_sms_verification_unexpected_error.tr()}$e',
      ));
    }
  }

  // Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await networkInfo.isConnected;
  }
}
