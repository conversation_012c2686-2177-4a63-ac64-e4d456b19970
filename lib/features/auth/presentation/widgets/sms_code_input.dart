import 'package:click_bazaar/core/utils/app_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';

/// Custom SMS code input widget with 6 individual text fields
class SmsCodeInput extends StatefulWidget {
  final Function(String) onCompleted;
  final Function(String)? onChanged;

  const SmsCodeInput({
    super.key,
    required this.onCompleted,
    this.onChanged,
  });

  @override
  State<SmsCodeInput> createState() => SmsCodeInputState();
}

class SmsCodeInputState extends State<SmsCodeInput> {
  TextEditingController? _textEditingController;
  FocusNode? _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode?.requestFocus();
    _textEditingController = TextEditingController();
  }

  /// Method to set SMS code programmatically (for auto-fill)
  void setSmsCode(String code) {
    if (!mounted || _textEditingController == null) return;

    if (code.length == 6 && RegExp(r'^\d+$').hasMatch(code)) {
      _textEditingController!.text = code;
      widget.onChanged?.call(code);
      widget.onCompleted(code);
    }
  }

  /// Method to clear SMS code
  void clearSmsCode() {
    if (!mounted || _textEditingController == null) return;

    _textEditingController!.clear();
    widget.onChanged?.call('');
  }

  @override
  void dispose() {
    _textEditingController = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_textEditingController == null) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: const BoxDecoration(
        color: Colors.transparent, // Ensure no background color
      ),
      child: Builder(
        builder: (context) => PinCodeTextField(
        appContext: context,
        length: 6,
        autoFocus: true,
        focusNode: _focusNode,
        controller: _textEditingController!,
        keyboardType: TextInputType.number,
        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
        backgroundColor: Colors.transparent,
        // Remove any default background
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        textStyle: AppTextStyles.headlineSmall.copyWith(
          color: Theme.of(context).colorScheme.onSurface,
          fontWeight: FontWeight.w600,
        ),
        pinTheme: PinTheme(
          shape: PinCodeFieldShape.box,
          borderRadius: BorderRadius.circular(12.r),
          fieldHeight: 63.h,
          fieldWidth: 51.w,
          activeFillColor: Theme.of(context).colorScheme.surface,
          inactiveFillColor: Theme.of(context).colorScheme.surface,
          selectedFillColor: Theme.of(context).colorScheme.surface,
          activeColor: Theme.of(context).colorScheme.primary,
          inactiveColor: Colors.transparent,
          selectedColor: Theme.of(context).colorScheme.primary,
          borderWidth: 2,
          disabledColor: Colors.transparent,
          errorBorderColor: Colors.transparent,
        ),
        enableActiveFill: true,
        cursorColor: Theme.of(context).colorScheme.primary,
        animationType: AnimationType.fade,
        animationDuration: const Duration(milliseconds: 300),
        onChanged: (value) {
          widget.onChanged?.call(value);
          AppFunctions.lightHaptic();
        },
        onCompleted: (value) {
          widget.onCompleted(value);
        },
        beforeTextPaste: (text) {
          // Allow pasting only if it's 6 digits
          return text?.length == 6 && RegExp(r'^\d+$').hasMatch(text!);
        },
        ),
      ),
    );
  }
}
