/// User profile model for supervisor/user information
class UserProfile {
  final String? id;
  final String? firstName;
  final String? lastName;
  final String? middleName;
  final String? role;
  final String? image;
  final String? phone;
  final String? address;
  final int? blocks;
  final int? places;
  final bool? gender;

  const UserProfile({
    this.id,
    this.firstName,
    this.lastName,
    this.middleName,
    this.role,
    this.image,
    this.phone,
    this.address,
    this.blocks,
    this.places,
    this.gender,
  });

  /// Create UserProfile from JSON
  factory UserProfile.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return const UserProfile();
    }

    return UserProfile(
      id: json['_id'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      middleName: json['middleName'] as String?,
      role: json['role'] as String?,
      image: json['image'] as String?,
      phone: json['phone'] as String?,
      address: json['address'] as String?,
      blocks: json['blocks'] as int?,
      places: json['places'] as int?,
      gender: json['gender'] as bool?,
    );
  }

  /// Convert UserProfile to JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'firstName': firstName,
      'lastName': lastName,
      'middleName': middleName,
      'role': role,
      'image': image,
      'phone': phone,
      'address': address,
      'blocks': blocks,
      'places': places,
      'gender': gender,
    };
  }

  /// Get full name
  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();

  /// Get full name with middle name
  String get fullNameWithMiddle => '${firstName ?? ''} ${lastName ?? ''} ${middleName ?? ''}'.trim();

  /// Get formatted phone number
  String get formattedPhone {
    final phoneValue = phone ?? '';
    if (phoneValue.length >= 9) {
      final cleanPhone = phoneValue.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanPhone.length == 9) {
        return '+998 ${cleanPhone.substring(0, 2)} ${cleanPhone.substring(2, 5)}-${cleanPhone.substring(5, 7)}-${cleanPhone.substring(7)}';
      }
    }
    return phoneValue;
  }

  /// Get gender display text
  String get genderText => (gender ?? true) ? 'Erkak' : 'Ayol';

  /// Get blocks display text
  String get blocksText => '${blocks ?? 0} ta blok';

  /// Get places display text
  String get placesText => '${places ?? 0} ta rasta';

  /// Face control compatibility methods
  /// Get face image URL (alias for image field for face control compatibility)
  String? get face => image;

  /// Get name (alias for fullName for face control compatibility)
  String get name => fullName;

  /// Get phoneNumber (alias for phone for face control compatibility)
  String get phoneNumber => phone ?? '';

  /// Check if user is supervisor
  bool get isSupervisor => role == 'supervisor';

  /// Check if user is seller
  bool get isSeller => role == 'seller';

  /// Copy with method for updating specific fields
  UserProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? middleName,
    String? role,
    String? image,
    String? phone,
    String? address,
    int? blocks,
    int? places,
    bool? gender,
  }) {
    return UserProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      middleName: middleName ?? this.middleName,
      role: role ?? this.role,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      blocks: blocks ?? this.blocks,
      places: places ?? this.places,
      gender: gender ?? this.gender,
    );
  }

  @override
  String toString() {
    return 'UserProfile(id: $id, firstName: $firstName, lastName: $lastName, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserProfile && other.id == id;
  }

  @override
  int get hashCode => id?.hashCode ?? 0;

  /// Check if the profile is empty/null
  bool get isEmpty {
    return id == null &&
           firstName == null &&
           lastName == null &&
           role == null;
  }

  /// Check if the profile has valid data
  bool get isValid {
    return id != null &&
           id!.isNotEmpty &&
           (firstName != null || lastName != null);
  }
}

/// Response model for user profile API
class UserProfileResponse {
  final bool success;
  final UserProfile? userProfile;
  final String? message;
  final String? error;

  const UserProfileResponse({
    required this.success,
    this.userProfile,
    this.message,
    this.error,
  });

  factory UserProfileResponse.fromJson(Map<String, dynamic> json) {
    return UserProfileResponse(
      success: true, // API returns user data directly on success
      userProfile: UserProfile.fromJson(json),
      message: 'Profile loaded successfully',
    );
  }

  factory UserProfileResponse.error(String error) {
    return UserProfileResponse(
      success: false,
      error: error,
      message: 'Failed to load profile',
    );
  }
}
