import '../../../core/widgets/role_switcher.dart';

/// Authentication related data models
class LoginRequest {
  final String phoneNumber;
  final bool agreeToTerms;

  const LoginRequest({
    required this.phoneNumber,
    required this.agreeToTerms,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone_number': phoneNumber,
      'agree_to_terms': agreeToTerms,
    };
  }
}

class SmsVerificationRequest {
  final String phoneNumber;
  final String smsCode;

  const SmsVerificationRequest({
    required this.phoneNumber,
    required this.smsCode,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone_number': phoneNumber,
      'sms_code': smsCode,
    };
  }
}

class AuthResponse {
  final bool success;
  final String? token;
  final String? refreshToken;
  final String? message;
  final UserRole? userRole;

  const AuthResponse({
    required this.success,
    this.token,
    this.refreshToken,
    this.message,
    this.userRole,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      token: json['token'],
      refreshToken: json['refresh_token'],
      message: json['message'],
      userRole: json['user_role'] != null 
          ? UserRole.values.firstWhere(
              (role) => role.name == json['user_role'],
              orElse: () => UserRole.nazoratchi,
            )
          : null,
    );
  }
}

