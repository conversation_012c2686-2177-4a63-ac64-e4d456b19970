# Login BLoC Implementation

## Overview

This document describes the implementation of login BLoC logic into the LoginPage. The implementation supports two different authentication flows based on user roles:

1. **Supervisor (Nazoratchi) Login**: Direct authentication using LoginBloc with phone and appSignature
2. **Seller (Sotuvchi) Login**: SMS-based authentication using AuthService

## Architecture

### Components

1. **LoginBloc**: Handles supervisor authentication with comprehensive error handling
2. **LoginRemoteDatasourceImpl**: Manages API calls for supervisor login
3. **AuthService**: Handles SMS-based authentication for sellers
4. **LoginPage**: UI component with role-based login logic

### Dependencies

The following dependencies are registered in the DI container (`lib/di/dependency_injection.dart`):

```dart
// Register Auth dependencies
di.registerLazySingleton<LoginRemoteDatasourceImpl>(
  () => LoginRemoteDatasourceImpl(
    networkInfo: di(),
    dio: di(),
  ),
);

di.registerFactory<LoginBloc>(
  () => LoginBloc(
    networkInfo: di(),
    loginRemoteDatasource: di(),
  ),
);
```

## Implementation Details

### LoginPage Structure

The LoginPage is now structured as follows:

1. **LoginPage**: StatelessWidget that provides the LoginBloc
2. **_LoginPageContent**: StatefulWidget that contains the actual UI and logic

```dart
class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => di<LoginBloc>(),
      child: const _LoginPageContent(),
    );
  }
}
```

### BLoC Integration

The page uses `BlocConsumer` to handle both state changes and UI updates:

```dart
return BlocConsumer<LoginBloc, LoginState>(
  listener: (context, state) {
    // Handle loading states
    if (state.status == LoginStatus.loading) {
      setState(() => _isLoading = true);
    } else {
      setState(() => _isLoading = false);
    }

    // Handle success/failure navigation and messages
    if (state.status == LoginStatus.success) {
      Navigator.pushReplacement(context, ...);
    } else if (state.status == LoginStatus.failure) {
      ScaffoldMessenger.of(context).showSnackBar(...);
    }
  },
  builder: (context, state) {
    return Scaffold(...);
  },
);
```

### Role-Based Login Logic

The login flow is determined by the selected user role:

```dart
Future<void> _handleLogin() async {
  // Validation logic...
  
  if (_selectedRole == UserRole.nazoratchi) {
    _supervisorLogin();  // Uses LoginBloc
  } else {
    _sendSmsCode();      // Uses AuthService
  }
}
```

### Supervisor Login Implementation

For supervisor login, the page dispatches a LoginEvent to the BLoC:

```dart
void _supervisorLogin() {
  String phoneNumber = _phoneController.text.trim()
      .replaceAll('+998 ', '')
      .replaceAll(' ', '');

  context.read<LoginBloc>().add(LoginEvent(
    phone: phoneNumber,
    appSignature: 'mobile_app_signature',
    userRole: _selectedRole,
  ));
}
```

## Features

### Dynamic Button Text

The login button text changes based on the selected role:
- **Nazoratchi**: "Kirish" (Login)
- **Sotuvchi**: "SMS yuborish" (Send SMS)

### Error Handling

- **Network errors**: Handled by the ErrorHandlerMixin in LoginBloc
- **Validation errors**: Handled at the UI level
- **API errors**: Displayed via SnackBar with appropriate messages

### Loading States

- Loading indicators are shown during authentication
- Button is disabled during loading to prevent multiple submissions

## API Endpoints

### Supervisor Login
- **Endpoint**: `{{apiUrl}}/mobile/auth/supervisor`
- **Method**: POST
- **Body**: `{ "phone": "string", "appSignature": "string" }`

### SMS-based Login
- Handled by AuthService (demo implementation)
- Real implementation would use appropriate SMS API endpoints

## Testing

A comprehensive test suite is provided in `test/features/auth/login_bloc_integration_test.dart` that covers:

1. Loading state display during supervisor login
2. Role-based button text changes
3. BLoC state transitions for success/failure scenarios
4. Widget interactions and navigation

## Usage

The LoginPage can be used directly in the app:

```dart
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const LoginPage()),
);
```

The BLoC is automatically provided and disposed of by the BlocProvider.

## Error Handling Strategy

The implementation follows the established error handling patterns:

1. **Network connectivity**: Checked at the datasource level
2. **API errors**: Handled by SimpleErrorHandler with user-friendly messages
3. **Validation errors**: Handled at the UI level with immediate feedback
4. **State management**: All states properly managed through BLoC pattern

## Future Improvements

1. **Dynamic appSignature generation**: Currently uses a static signature
2. **Biometric authentication**: Could be added for supervisor login
3. **Remember login state**: Could cache supervisor credentials securely
4. **Offline support**: Could cache authentication tokens for offline use
