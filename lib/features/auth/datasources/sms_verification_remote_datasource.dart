import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/utils/jwt_decoder.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';

class SmsVerificationRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  SmsVerificationRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  Future<Map<String, dynamic>> verifySmsCode(String phone, String verifyCode, UserRole? userRole) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: userRole == UserRole.nazoratchi
            ? ApiPath.nazAuthPath
            : ApiPath.sotAuthPath,),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      var response = await dio.post(
        userRole == UserRole.nazoratchi
            ? ApiPath.nazAuthPath
            : ApiPath.sotAuthPath,
        data: {
          'phone': phone,
          'verifyCode': int.parse(verifyCode),
        },
      );

      final data = response.data;
      if (response.statusCode == 200) {
        print("SMS Verification Data: $data");
        
        if (data.containsKey('message') && data['message'] == 'success') {
          final token = data['token'] as String?;
          final refreshToken = data['refreshToken'] as String?; // Check for refresh token

          if (token != null) {
            // Decode token to extract user info
            final userRole = JwtDecoder.getUserRole(token);
            final userId = JwtDecoder.getUserId(token);

            final result = {
              'success': true,
              'token': token,
              'userRole': userRole,
              'userId': userId,
              'message': 'Muvaffaqiyatli kirish',
            };

            // Include refresh token if available
            if (refreshToken != null) {
              result['refreshToken'] = refreshToken;
            }

            return result;
          }
        }
        
        return {
          'success': false,
          'message': data['message'] ?? 'Noto\'g\'ri SMS kod',
        };
      }
      
      return {
        'success': false,
        'message': 'Server xatoligi',
      };
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path:userRole == UserRole.nazoratchi
            ? ApiPath.nazAuthPath
            : ApiPath.sotAuthPath,),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
