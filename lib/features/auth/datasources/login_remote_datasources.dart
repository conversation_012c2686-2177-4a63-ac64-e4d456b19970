import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import '../models/auth_models.dart';
import '../../../translations/locale_keys.g.dart';

class LoginRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  LoginRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  Future<bool> login(
      String phone, String appSignature, UserRole userRole) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: userRole == UserRole.nazoratchi
              ? ApiPath.nazLoginPath
              : ApiPath.sotLoginPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }
    try {
      var response = await dio.post(
        userRole == UserRole.nazoratchi
            ? ApiPath.nazLoginPath
            : ApiPath.sotLoginPath,
        data: {
          'phone': phone,
          'appSignature': appSignature,
        },
      );
      final data = response.data;
      if (response.statusCode == 200) {
        if (data.containsKey('message')) {
          return data['message'] == 'success';
        }
        return false;
      }
      return false;
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.nazLoginPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
