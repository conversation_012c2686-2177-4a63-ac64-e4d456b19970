import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../core/widgets/universal_loading.dart';

class EmptyPlaceShimmer extends StatelessWidget {
  const EmptyPlaceShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return UniversalLoading.shimmer(
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        itemCount: 6, // Show 6 skeleton cards
        itemBuilder: (context, index) {
          return _buildShimmerCard(context);
        },
      ),
    );
  }

  /// Build individual shimmer card skeleton
  Widget _buildShimmerCard(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(cRadius12.r),
        boxShadow: Theme.of(context).brightness == Brightness.light ? [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Row(
        children: [
          // Image placeholder
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(cRadius8.r),
              color: AppColors.cTextGrayColor,
            ),
          ),
          Gap(16.w),
          // Content placeholder
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Block title placeholder
                Container(
                  width: 100.w,
                  height: 16.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.r),
                    color: AppColors.cTextGrayColor,
                  ),
                ),
                Gap(4.h),
                // Rasta number placeholder
                Container(
                  width: 80.w,
                  height: 14.h,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.r),
                    color: AppColors.cTextGrayColor,
                  ),
                ),
                Gap(12.h),
                // Tags placeholder
                Row(
                  children: [
                    Container(
                      width: 70.w,
                      height: 24.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius8.r),
                        color: AppColors.cTextGrayColor,
                      ),
                    ),
                    Gap(8.w),
                    Container(
                      width: 90.w,
                      height: 24.h,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(cRadius8.r),
                        color: AppColors.cTextGrayColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
