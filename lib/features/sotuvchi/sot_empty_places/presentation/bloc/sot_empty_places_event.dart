part of 'sot_empty_places_bloc.dart';

sealed class SotEmptyPlacesEvent extends Equatable {
  const SotEmptyPlacesEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load free places
class LoadFreePlaces extends SotEmptyPlacesEvent {
  final String marketId;
  final int page;
  final int limit;
  final bool isRefresh;

  const LoadFreePlaces({
    required this.marketId,
    this.page = 1,
    this.limit = 20,
    this.isRefresh = false,
  });

  @override
  List<Object?> get props => [marketId, page, limit, isRefresh];
}

/// Event to refresh free places
class RefreshFreePlaces extends SotEmptyPlacesEvent {
  final String marketId;

  const RefreshFreePlaces({required this.marketId});

  @override
  List<Object?> get props => [marketId];
}

/// Event to load more free places (pagination)
class LoadMoreFreePlaces extends SotEmptyPlacesEvent {
  final String marketId;

  const LoadMoreFreePlaces({required this.marketId});

  @override
  List<Object?> get props => [marketId];
}
