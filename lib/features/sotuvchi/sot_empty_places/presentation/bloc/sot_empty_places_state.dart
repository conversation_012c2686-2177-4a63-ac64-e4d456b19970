part of 'sot_empty_places_bloc.dart';

enum SotEmptyPlacesStatus { initial, loading, success, failure, loadAgain }

/// Error type enum for better error handling
enum SotEmptyPlacesErrorType { network, api, unknown }

class SotEmptyPlacesState extends Equatable {
  final SotEmptyPlacesStatus status;
  final List<EmptyPlace> emptyPlaces;
  final String? message;
  final SotEmptyPlacesErrorType? errorType;
  final int currentPage;
  final int totalPages;
  final bool hasNextPage;
  final bool isRefreshing;
  final bool isLoadingMore;
  final bool hasRefreshError;

  const SotEmptyPlacesState({
    this.status = SotEmptyPlacesStatus.initial,
    this.emptyPlaces = const [],
    this.message,
    this.errorType,
    this.currentPage = 1,
    this.totalPages = 1,
    this.hasNextPage = false,
    this.isRefreshing = false,
    this.isLoadingMore = false,
    this.hasRefreshError = false,
  });

  SotEmptyPlacesState copyWith({
    SotEmptyPlacesStatus? status,
    List<EmptyPlace>? emptyPlaces,
    String? message,
    SotEmptyPlacesErrorType? errorType,
    int? currentPage,
    int? totalPages,
    bool? hasNextPage,
    bool? isRefreshing,
    bool? isLoadingMore,
    bool? hasRefreshError,
  }) {
    return SotEmptyPlacesState(
      status: status ?? this.status,
      emptyPlaces: emptyPlaces ?? this.emptyPlaces,
      message: message ?? this.message,
      errorType: errorType ?? this.errorType,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasRefreshError: hasRefreshError ?? this.hasRefreshError,
    );
  }

  /// Check if there are any empty places loaded
  bool get hasAnyEmptyPlaces => emptyPlaces.isNotEmpty;

  /// Check if state is loading
  bool get isLoading => status == SotEmptyPlacesStatus.loading;

  /// Check if state is success
  bool get isSuccess => status == SotEmptyPlacesStatus.success;

  /// Check if state is failure
  bool get isFailure => status == SotEmptyPlacesStatus.failure;

  /// Check if state is initial
  bool get isInitial => status == SotEmptyPlacesStatus.initial;

  /// Check if error is network related
  bool get isNetworkError => isFailure && errorType == SotEmptyPlacesErrorType.network;

  /// Check if error is API related
  bool get isApiError => isFailure && errorType == SotEmptyPlacesErrorType.api;

  @override
  List<Object?> get props => [
        status,
        emptyPlaces,
        message,
        errorType,
        currentPage,
        totalPages,
        hasNextPage,
        isRefreshing,
        isLoadingMore,
        hasRefreshError,
      ];

  @override
  String toString() {
    return 'SotEmptyPlacesState(status: $status, message: $message, isRefreshing: $isRefreshing, hasRefreshError: $hasRefreshError, emptyPlacesCount: ${emptyPlaces.length})';
  }
}
