import 'package:equatable/equatable.dart';

/// Model for empty places response from API
class EmptyPlacesResponse extends Equatable {
  final List<EmptyPlace> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  const EmptyPlacesResponse({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  /// Factory constructor to create EmptyPlacesResponse from JSON
  factory EmptyPlacesResponse.fromJson(Map<String, dynamic> json) {
    return EmptyPlacesResponse(
      docs: (json['docs'] as List<dynamic>?)
              ?.map((e) => EmptyPlace.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      totalDocs: json['totalDocs'] as int? ?? 0,
      limit: json['limit'] as int? ?? 20,
      totalPages: json['totalPages'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      pagingCounter: json['pagingCounter'] as int? ?? 1,
      hasPrevPage: json['hasPrevPage'] as bool? ?? false,
      hasNextPage: json['hasNextPage'] as bool? ?? false,
      prevPage: json['prevPage'] as int?,
      nextPage: json['nextPage'] as int?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'docs': docs.map((e) => e.toJson()).toList(),
      'totalDocs': totalDocs,
      'limit': limit,
      'totalPages': totalPages,
      'page': page,
      'pagingCounter': pagingCounter,
      'hasPrevPage': hasPrevPage,
      'hasNextPage': hasNextPage,
      'prevPage': prevPage,
      'nextPage': nextPage,
    };
  }

  @override
  List<Object?> get props => [
        docs,
        totalDocs,
        limit,
        totalPages,
        page,
        pagingCounter,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      ];
}

/// Model for empty place data from API
class EmptyPlace extends Equatable {
  final String id;
  final String title;
  final double price;
  final Block block;
  final Pavilion pavilion;
  final String? imageUrl;

  const EmptyPlace({
    required this.id,
    required this.title,
    required this.price,
    required this.block,
    required this.pavilion,
    this.imageUrl,
  });

  /// Factory constructor to create EmptyPlace from JSON
  factory EmptyPlace.fromJson(Map<String, dynamic> json) {
    // Clean and validate image URL
    String? imageUrl = json['photo']?.toString();
    if (imageUrl != null && imageUrl.isNotEmpty) {
      imageUrl = imageUrl.trim();
      // Remove any invalid characters or malformed URLs
      if (imageUrl.startsWith('//')) {
        imageUrl = 'https:$imageUrl';
      }
    }

    return EmptyPlace(
      id: json['_id']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      price: _parsePrice(json['price']),
      block: Block.fromJson(json['block'] ?? {}),
      pavilion: Pavilion.fromJson(json['pavilion'] ?? {}),
      imageUrl: imageUrl,
    );
  }

  /// Helper method to safely parse price
  static double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is double) return price;
    if (price is int) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }

  /// Convert EmptyPlace to JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'price': price,
      'block': block.toJson(),
      'pavilion': pavilion.toJson(),
      'imageUrl': imageUrl,
    };
  }

  /// Get formatted block title for display (e.g., "B-block" -> "2-blok")

  /// Get formatted pavilion title for display (e.g., "1-pavillion" -> "Ho'l mevalar")

  /// Get formatted price for display
  String get displayPrice {
    if (price == 0) return '0 UZS';
    
    // Format with thousand separators
    final formatter = price.toInt().toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]} ',
    );
    return '$formatter UZS';
  }

  /// Get rasta number for display
  String get displayRastaNumber => title;

  @override
  List<Object?> get props => [id, title, price, block, pavilion, imageUrl];
}

/// Block model
class Block extends Equatable {
  final String id;
  final String title;

  const Block({
    required this.id,
    required this.title,
  });

  factory Block.fromJson(Map<String, dynamic> json) {
    return Block(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
    };
  }

  @override
  List<Object?> get props => [id, title];
}

/// Pavilion model
class Pavilion extends Equatable {
  final String id;
  final String title;

  const Pavilion({
    required this.id,
    required this.title,
  });

  factory Pavilion.fromJson(Map<String, dynamic> json) {
    return Pavilion(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
    };
  }

  @override
  List<Object?> get props => [id, title];
}
