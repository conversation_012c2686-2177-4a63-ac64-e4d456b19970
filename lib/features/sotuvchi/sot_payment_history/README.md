# Seller Payment History Feature

## Overview

This feature provides payment history functionality for sellers (sotuvchi) with paginated API support, following the established architectural patterns from auth and other sotuvchi features.

## API Integration

- **Endpoint**: `{{apiUrl}}/mobile/payment/seller?page=1&limit=20`
- **Method**: GET
- **Authentication**: Required (JWT token)
- **Pagination**: Supported with page and limit parameters

## Response Structure

```json
{
  "docs": [
    {
      "_id": "payment_id",
      "price": 120000,
      "date": "2025-01-15 14:30",
      "paymentType": 1,
      "places": [
        {
          "_id": "place_id",
          "title": 45
        }
      ]
    }
  ],
  "totalDocs": 100,
  "limit": 20,
  "totalPages": 5,
  "page": 1,
  "pagingCounter": 1,
  "hasPrevPage": false,
  "hasNextPage": true,
  "prevPage": null,
  "nextPage": 2
}
```

## Data Display Mapping

- `price` → "Tarif" with comma formatting (e.g., "120,000")
- `date` → "To'langan" in DD.MM.YYYY format
- `places` → "Rasta raqami" as comma-separated place numbers with # prefix (e.g., "#2, #4, #5")

## Architecture

### Folder Structure

```
lib/features/sotuvchi/sot_payment_history/
├── models/
│   └── payment_history_model.dart
├── datasources/
│   └── payment_history_remote_datasource.dart
├── presentation/
│   ├── bloc/
│   │   ├── payment_history_bloc.dart
│   │   ├── payment_history_event.dart
│   │   └── payment_history_state.dart
│   ├── page/
│   │   └── sot_payment_history_page.dart
│   ├── widget/
│   │   └── payment_history_card.dart
│   └── widgets/
│       └── payment_history_shimmer.dart
├── sot_payment_history.dart (exports)
└── README.md
```

### BLoC Pattern

- **Single State Architecture**: Following LoginBloc/SmsVerificationBloc patterns
- **ErrorHandlerMixin**: Integrated for consistent error handling
- **Events**: LoadPaymentHistoryEvent, RefreshPaymentHistoryEvent
- **States**: PaymentHistoryStatus enum with loading, success, failure states

### Key Components

1. **PaymentHistoryModel**: Handles JSON parsing and display formatting
2. **PaymentHistoryRemoteDatasource**: API communication with error handling
3. **PaymentHistoryBloc**: State management with ErrorHandlerMixin
4. **PaymentHistoryShimmer**: Loading skeleton matching card design
5. **PaymentHistoryCard**: Updated to use new data structure

## Features

- ✅ Paginated API support (page 1, limit 20)
- ✅ Swipe-to-refresh functionality
- ✅ Shimmer skeleton loading
- ✅ Empty state handling
- ✅ Error state with retry functionality
- ✅ Comma-separated number formatting
- ✅ Multiple place numbers display
- ✅ Consistent UI design maintenance

## Dependencies

Registered in `lib/di/dependency_injection.dart`:

```dart
// Payment History dependencies
di.registerLazySingleton<PaymentHistoryRemoteDatasource>(
  () => PaymentHistoryRemoteDatasourceImpl(
    dio: di(),
    networkInfo: di(),
  ),
);

di.registerFactory<PaymentHistoryBloc>(
  () => PaymentHistoryBloc(
    remoteDatasource: di(),
    networkInfo: di(),
  ),
);
```

## Usage

The feature is automatically integrated into the seller navigation through `MainSotNavigationPage`. The payment history page is accessible via the bottom navigation.

## Error Handling

- Network connectivity checks
- DioException handling with user-friendly messages
- Automatic retry functionality
- Graceful fallbacks for data parsing errors

## Future Enhancements

- Infinite scroll pagination
- Date range filtering
- Payment type filtering
- Export functionality
