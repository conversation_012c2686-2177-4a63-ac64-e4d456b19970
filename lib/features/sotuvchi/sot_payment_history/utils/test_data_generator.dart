import '../models/payment_history_model.dart';

/// Utility class for generating test data for payment history
/// This is useful for development and testing purposes
class PaymentHistoryTestDataGenerator {
  /// Generate sample payment history response for testing
  static PaymentHistoryResponse generateSampleResponse({
    int page = 1,
    int limit = 20,
    int totalItems = 50,
  }) {
    final docs = List.generate(limit, (index) {
      final paymentIndex = (page - 1) * limit + index + 1;
      return PaymentHistory(
        id: 'payment_$paymentIndex',
        price: _generateRandomPrice(),
        date: _generateRandomDate(),
        paymentType: _generateRandomPaymentType(),
        places: _generateRandomPlaces(),
      );
    });

    final totalPages = (totalItems / limit).ceil();
    final hasNextPage = page < totalPages;
    final hasPrevPage = page > 1;

    return PaymentHistoryResponse(
      docs: docs,
      totalDocs: totalItems,
      limit: limit,
      totalPages: totalPages,
      page: page,
      pagingCounter: (page - 1) * limit + 1,
      hasPrevPage: hasPrevPage,
      hasNextPage: hasNextPage,
      prevPage: hasPrevPage ? page - 1 : null,
      nextPage: hasNextPage ? page + 1 : null,
    );
  }

  /// Generate sample JSON response for testing API parsing
  static Map<String, dynamic> generateSampleJsonResponse({
    int page = 1,
    int limit = 20,
    int totalItems = 50,
  }) {
    final response = generateSampleResponse(
      page: page,
      limit: limit,
      totalItems: totalItems,
    );
    return response.toJson();
  }

  /// Generate random price between 50,000 and 500,000
  static double _generateRandomPrice() {
    final prices = [50000, 75000, 100000, 120000, 150000, 200000, 250000, 300000, 400000, 500000];
    prices.shuffle();
    return prices.first.toDouble();
  }

  /// Generate random date in the last 30 days
  static String _generateRandomDate() {
    final now = DateTime.now();
    final daysAgo = (DateTime.now().millisecondsSinceEpoch % 30) + 1;
    final randomDate = now.subtract(Duration(days: daysAgo.toInt()));
    
    // Format as "YYYY-MM-DD HH:mm"
    return '${randomDate.year}-${randomDate.month.toString().padLeft(2, '0')}-${randomDate.day.toString().padLeft(2, '0')} ${randomDate.hour.toString().padLeft(2, '0')}:${randomDate.minute.toString().padLeft(2, '0')}';
  }

  /// Generate random payment type (1, 2, or 3)
  static int _generateRandomPaymentType() {
    final types = [1, 2, 3];
    types.shuffle();
    return types.first;
  }

  /// Generate random places (1-3 places)
  static List<PaymentPlace> _generateRandomPlaces() {
    final placeCount = (DateTime.now().millisecondsSinceEpoch % 3) + 1;
    final places = <PaymentPlace>[];
    
    for (int i = 0; i < placeCount; i++) {
      final placeNumber = 40 + i + (DateTime.now().millisecondsSinceEpoch % 20).toInt();
      places.add(PaymentPlace(
        id: 'place_${placeNumber}',
        title: placeNumber,
      ));
    }
    
    return places;
  }

  /// Generate sample payment history for empty state testing
  static PaymentHistoryResponse generateEmptyResponse() {
    return const PaymentHistoryResponse(
      docs: [],
      totalDocs: 0,
      limit: 20,
      totalPages: 0,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    );
  }

  /// Generate sample single payment for testing
  static PaymentHistory generateSamplePayment() {
    return PaymentHistory(
      id: 'sample_payment_123',
      price: 120000.0,
      date: '2025-01-15 14:30',
      paymentType: 1,
      places: [
        const PaymentPlace(id: 'place_45', title: 45),
        const PaymentPlace(id: 'place_46', title: 46),
      ],
    );
  }
}
