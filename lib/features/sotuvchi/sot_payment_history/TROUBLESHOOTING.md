# Payment History Feature - Troubleshooting Guide

## Common Issues and Solutions

### 1. Compilation Errors

#### Missing Dependencies
If you get import errors, ensure all dependencies are properly registered in `lib/di/dependency_injection.dart`:

```dart
// Check these imports are present:
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/datasources/payment_history_remote_datasource.dart';
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/presentation/bloc/payment_history_bloc.dart';

// Check these registrations are present:
di.registerLazySingleton<PaymentHistoryRemoteDatasource>(
  () => PaymentHistoryRemoteDatasourceImpl(
    dio: di(),
    networkInfo: di(),
  ),
);

di.registerFactory<PaymentHistoryBloc>(
  () => PaymentHistoryBloc(
    remoteDatasource: di(),
    networkInfo: di(),
  ),
);
```

#### API Path Missing
Ensure `lib/core/utils/api_path.dart` contains:
```dart
static const String paymentHistoryPath = 'mobile/payment/seller';
```

### 2. Runtime Errors

#### BLoC Not Found
If you get "No provider found for PaymentHistoryBloc", check:
1. Dependencies are registered in DI
2. BlocProvider is properly set up in the page
3. The page is using `di<PaymentHistoryBloc>()` correctly

#### API Errors
Check the console logs for:
```
PaymentHistoryRemoteDatasource: Fetching payment history - page: 1, limit: 20
PaymentHistoryRemoteDatasource: Response status: 200
PaymentHistoryRemoteDatasource: Response data type: _Map<String, dynamic>
```

Common API issues:
- **401/403**: Authentication token expired or invalid
- **404**: Endpoint not found (check API path)
- **500**: Server error (check API implementation)

### 3. Data Display Issues

#### Empty Place Numbers
If place numbers show as "-", check:
1. API response contains `places` array
2. Each place has valid `title` field (number > 0)
3. JSON parsing is working correctly
4. Place numbers should display with # prefix (e.g., "#45, #46")

#### Incorrect Date Format
If dates don't display correctly:
1. Check API returns date in "YYYY-MM-DD HH:mm" format
2. Verify `displayDate` getter handles your date format
3. Check console for date parsing errors

#### Price Formatting Issues
If prices don't show comma separators:
1. Verify API returns numeric price values
2. Check `displayPrice` getter implementation
3. Ensure price parsing handles different number formats

### 4. UI Issues

#### Shimmer Not Showing
If loading state doesn't show shimmer:
1. Check BLoC state is `PaymentHistoryStatus.loading`
2. Verify `PaymentHistoryShimmer` widget is imported correctly
3. Ensure `UniversalLoading.shimmer` is working

#### Empty State Not Showing
If empty state doesn't appear when no data:
1. Check API returns empty array in `docs`
2. Verify empty state condition: `paymentHistory.isEmpty && !isRefreshing`
3. Check empty state widget implementation

#### Error State Issues
If error state doesn't show properly:
1. Check BLoC emits `PaymentHistoryStatus.failure`
2. Verify error message is passed correctly
3. Check error state widget styling

### 5. Testing the Feature

#### Manual Testing Steps
1. **Navigate to Payment History**: Use bottom navigation in seller app
2. **Check Loading**: Should show shimmer skeleton
3. **Check Data**: Should display formatted payment data
4. **Test Refresh**: Pull down to refresh data
5. **Test Error**: Disconnect internet and retry

#### Using Test Data
For development testing, you can use the test data generator:

```dart
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/utils/test_data_generator.dart';

// Generate sample data
final sampleResponse = PaymentHistoryTestDataGenerator.generateSampleResponse();
final sampleJson = PaymentHistoryTestDataGenerator.generateSampleJsonResponse();
```

### 6. Debug Console Logs

Enable debug logging to see:
```
PaymentHistoryRemoteDatasource: Fetching payment history - page: 1, limit: 20
PaymentHistoryRemoteDatasource: Response status: 200
PaymentHistoryRemoteDatasource: Response data type: _Map<String, dynamic>
```

For errors:
```
PaymentHistoryRemoteDatasource: DioException - Connection timeout
PaymentHistoryRemoteDatasource: Unexpected error - FormatException
```

### 7. API Response Validation

Expected API response structure:
```json
{
  "docs": [
    {
      "_id": "string",
      "price": number,
      "date": "YYYY-MM-DD HH:mm",
      "paymentType": number,
      "places": [
        {
          "_id": "string",
          "title": number
        }
      ]
    }
  ],
  "totalDocs": number,
  "limit": number,
  "totalPages": number,
  "page": number,
  "pagingCounter": number,
  "hasPrevPage": boolean,
  "hasNextPage": boolean,
  "prevPage": number | null,
  "nextPage": number | null
}
```

### 8. Performance Issues

#### Slow Loading
If the feature loads slowly:
1. Check network connectivity
2. Verify API response time
3. Consider reducing limit parameter
4. Check for memory leaks in BLoC

#### Memory Issues
If app crashes or becomes slow:
1. Ensure BLoC is properly disposed
2. Check for circular references
3. Verify image loading (if any) is optimized

### 9. Quick Fixes

#### Reset Feature State
To reset the payment history feature:
```dart
context.read<PaymentHistoryBloc>().add(const LoadPaymentHistoryEvent());
```

#### Force Refresh
To force refresh data:
```dart
context.read<PaymentHistoryBloc>().add(const RefreshPaymentHistoryEvent());
```

#### Check Feature Status
To verify feature is working:
1. Check DI registrations
2. Verify API path constant
3. Test with sample data
4. Check console logs
5. Verify UI components render correctly

### 10. Getting Help

If issues persist:
1. Check console logs for detailed error messages
2. Verify API endpoint is working with tools like Postman
3. Test with sample data using `PaymentHistoryTestDataGenerator`
4. Compare implementation with working features like `sot_empty_places`
5. Check network connectivity and authentication tokens
