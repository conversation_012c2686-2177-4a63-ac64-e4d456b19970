import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import '../models/payment_history_model.dart';

/// Abstract class for payment history remote datasource
abstract class PaymentHistoryRemoteDatasource {
  Future<PaymentHistoryResponse> getPaymentHistory({
    int page = 1,
    int limit = 20,
  });
}

/// Implementation of payment history remote datasource
class PaymentHistoryRemoteDatasourceImpl
    implements PaymentHistoryRemoteDatasource {
  final NetworkInfo networkInfo;
  final Dio dio;

  PaymentHistoryRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  @override
  Future<PaymentHistoryResponse> getPaymentHistory({
    int page = 1,
    int limit = 20,
  }) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(
          path: ApiPath.paymentHistoryPath,
        ),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      print(
          'PaymentHistoryRemoteDatasource: Fetching payment history - page: $page, limit: $limit');

      final response = await dio.get(
        ApiPath.paymentHistoryPath,
        queryParameters: {
          'page': page,
          'limit': limit,
        },
      );

      print(
          'PaymentHistoryRemoteDatasource: Response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = response.data;
        print(
            'PaymentHistoryRemoteDatasource: Response data type: ${data.runtimeType}');

        // Handle both direct array response and paginated response
        if (data is List) {
          // If API returns direct array, wrap it in pagination structure
          return PaymentHistoryResponse(
            docs: data
                .map((e) => PaymentHistory.fromJson(e as Map<String, dynamic>))
                .toList(),
            totalDocs: data.length,
            limit: limit,
            totalPages: 1,
            page: page,
            pagingCounter: 1,
            hasPrevPage: false,
            hasNextPage: false,
            prevPage: null,
            nextPage: null,
          );
        } else if (data is Map<String, dynamic>) {
          // If API returns paginated response
          return PaymentHistoryResponse.fromJson(data);
        } else {
          throw DioException(
            requestOptions: RequestOptions(path: ApiPath.paymentHistoryPath),
            type: DioExceptionType.badResponse,
            message: LocaleKeys.statistics_history_invalid_response_format.tr(),
          );
        }
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.paymentHistoryPath),
          type: DioExceptionType.badResponse,
          message:
              '${LocaleKeys.errors_server_error.tr()}: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('PaymentHistoryRemoteDatasource: DioException - ${e.message}');
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      print('PaymentHistoryRemoteDatasource: Unexpected error - $e');
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.paymentHistoryPath),
        type: DioExceptionType.unknown,
        error: e,
        message: '${LocaleKeys.auth_sms_verification_unexpected_error.tr()} $e',
      );
    }
  }
}
