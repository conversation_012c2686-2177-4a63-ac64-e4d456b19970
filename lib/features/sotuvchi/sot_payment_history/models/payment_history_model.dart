import 'package:equatable/equatable.dart';

/// Model for payment history response from API
class PaymentHistoryResponse extends Equatable {
  final List<PaymentHistory> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  const PaymentHistoryResponse({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  /// Factory constructor to create PaymentHistoryResponse from JSON
  factory PaymentHistoryResponse.fromJson(Map<String, dynamic> json) {
    return PaymentHistoryResponse(
      docs: (json['docs'] as List<dynamic>?)
              ?.map((e) => PaymentHistory.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      totalDocs: json['totalDocs'] as int? ?? 0,
      limit: json['limit'] as int? ?? 20,
      totalPages: json['totalPages'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      pagingCounter: json['pagingCounter'] as int? ?? 1,
      hasPrevPage: json['hasPrevPage'] as bool? ?? false,
      hasNextPage: json['hasNextPage'] as bool? ?? false,
      prevPage: json['prevPage'] as int?,
      nextPage: json['nextPage'] as int?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'docs': docs.map((e) => e.toJson()).toList(),
      'totalDocs': totalDocs,
      'limit': limit,
      'totalPages': totalPages,
      'page': page,
      'pagingCounter': pagingCounter,
      'hasPrevPage': hasPrevPage,
      'hasNextPage': hasNextPage,
      'prevPage': prevPage,
      'nextPage': nextPage,
    };
  }

  @override
  List<Object?> get props => [
        docs,
        totalDocs,
        limit,
        totalPages,
        page,
        pagingCounter,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      ];
}

/// Model for individual payment history item
class PaymentHistory extends Equatable {
  final String id;
  final double price;
  final String date;
  final int paymentType;
  final List<PaymentPlace> places;

  const PaymentHistory({
    required this.id,
    required this.price,
    required this.date,
    required this.paymentType,
    required this.places,
  });

  /// Factory constructor to create PaymentHistory from JSON
  factory PaymentHistory.fromJson(Map<String, dynamic> json) {
    return PaymentHistory(
      id: json['_id']?.toString() ?? '',
      price: _parsePrice(json['price']),
      date: json['date']?.toString() ?? '',
      paymentType: json['paymentType'] as int? ?? 1,
      places: (json['places'] as List<dynamic>?)
              ?.map((e) => PaymentPlace.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  /// Helper method to parse price from various formats
  static double _parsePrice(dynamic price) {
    if (price == null) return 0.0;
    if (price is num) return price.toDouble();
    if (price is String) {
      return double.tryParse(price) ?? 0.0;
    }
    return 0.0;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'price': price,
      'date': date,
      'paymentType': paymentType,
      'places': places.map((e) => e.toJson()).toList(),
    };
  }

  /// Get formatted price for display with comma separation
  String get displayPrice {
    if (price == 0) return '0';
    
    // Format with thousand separators
    final formatter = price.toInt().toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
    return formatter;
  }

  /// Get formatted date for display
  String get displayDate {
    try {
      // Handle different date formats
      DateTime dateTime;

      if (date.contains(' ')) {
        // Format: "YYYY-MM-DD HH:mm"
        dateTime = DateTime.parse(date);
      } else if (date.contains('-')) {
        // Format: "YYYY-MM-DD"
        dateTime = DateTime.parse(date);
      } else {
        // Try to parse as is
        dateTime = DateTime.parse(date);
      }

      return '${dateTime.day.toString().padLeft(2, '0')}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.year}';
    } catch (e) {
      // If parsing fails, try to extract date parts manually
      if (date.length >= 10 && date.contains('-')) {
        try {
          final parts = date.substring(0, 10).split('-');
          if (parts.length == 3) {
            final year = parts[0];
            final month = parts[1].padLeft(2, '0');
            final day = parts[2].padLeft(2, '0');
            return '$day.$month.$year';
          }
        } catch (e) {
          // Ignore and return original
        }
      }
      return date; // Return original if all parsing fails
    }
  }

  /// Get comma-separated place numbers for display
  String get displayPlaceNumbers {
    if (places.isEmpty) return '-';

    final placeNumbers = places
        .map((place) => place.title)
        .where((title) => title > 0) // Only include valid place numbers
        .map((title) => '#$title')
        .toList();

    return placeNumbers.isNotEmpty ? placeNumbers.join(', ') : '-';
  }

  @override
  List<Object?> get props => [id, price, date, paymentType, places];
}

/// Model for place information in payment
class PaymentPlace extends Equatable {
  final String id;
  final int title;

  const PaymentPlace({
    required this.id,
    required this.title,
  });

  /// Factory constructor to create PaymentPlace from JSON
  factory PaymentPlace.fromJson(Map<String, dynamic> json) {
    return PaymentPlace(
      id: json['_id']?.toString() ?? '',
      title: _parseTitle(json['title']),
    );
  }

  /// Helper method to parse title from various formats
  static int _parseTitle(dynamic title) {
    if (title == null) return 0;
    if (title is int) return title;
    if (title is String) {
      return int.tryParse(title) ?? 0;
    }
    if (title is double) return title.toInt();
    return 0;
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
    };
  }

  @override
  List<Object?> get props => [id, title];
}
