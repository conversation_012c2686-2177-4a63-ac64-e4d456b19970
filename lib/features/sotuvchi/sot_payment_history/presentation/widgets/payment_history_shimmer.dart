import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/widgets/universal_loading.dart';

/// Shimmer loading widget for payment history
class PaymentHistoryShimmer extends StatelessWidget {
  const PaymentHistoryShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return UniversalLoading.shimmer(
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: 6, // Show 6 skeleton cards
        separatorBuilder: (context, index) => const SizedBox(height: 12),
        itemBuilder: (context, index) {
          return _buildShimmerCard();
        },
      ),
    );
  }

  /// Build individual shimmer card skeleton
  Widget _buildShimmerCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.cGrayBorderColor,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Rasta raqami row
          _buildShimmerRow(),
          const SizedBox(height: 12),
          _buildShimmerDivider(),
          const SizedBox(height: 12),
          // Tarif row
          _buildShimmerRow(),
          const SizedBox(height: 12),
          _buildShimmerDivider(),
          const SizedBox(height: 12),
          // To'langan row
          _buildShimmerRow(),
        ],
      ),
    );
  }

  /// Build shimmer row (label + value)
  Widget _buildShimmerRow() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Label placeholder
        Container(
          width: 80.w,
          height: 14.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            color: AppColors.cTextGrayColor.withOpacity(0.2),
          ),
        ),
        // Value placeholder
        Container(
          width: 100.w,
          height: 14.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            color: AppColors.white.withOpacity(0.2),
          ),
        ),
      ],
    );
  }

  /// Build shimmer divider
  Widget _buildShimmerDivider() {
    return Container(
      height: 1,
      width: double.infinity,
      color: AppColors.cGrayBorderColor.withOpacity(0.3),
    );
  }
}
