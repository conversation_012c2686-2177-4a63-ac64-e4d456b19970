import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../translations/locale_keys.g.dart';
import '../../models/payment_history_model.dart';

class PaymentHistoryCard extends StatelessWidget {
  final PaymentHistory paymentHistory;

  const PaymentHistoryCard({
    super.key,
    required this.paymentHistory,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          _buildPaymentRow(context,LocaleKeys.sotuvchi_payment_history_place_number.tr(), paymentHistory.displayPlaceNumbers),
          const SizedBox(height: 12),
          _buildDivider(context),
          const SizedBox(height: 12),
          _buildPaymentRow(context,LocaleKeys.sotuvchi_payment_history_tariff.tr(), "${paymentHistory.displayPrice} UZS"),
          const SizedBox(height: 12),
          _buildDivider(context),
          const SizedBox(height: 12),
          _buildPaymentRow(context,LocaleKeys.sotuvchi_payment_history_paid_date.tr(), paymentHistory.displayDate),
        ],
      ),
    );
  }

  Widget _buildPaymentRow(BuildContext context, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14,
            fontWeight: FontWeight.w400,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Container(
      height: 1,
      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
    );
  }
}
