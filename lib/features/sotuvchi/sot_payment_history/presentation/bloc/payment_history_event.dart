part of 'payment_history_bloc.dart';

/// Base class for payment history events
abstract class PaymentHistoryEvent extends Equatable {
  const PaymentHistoryEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load payment history
class LoadPaymentHistoryEvent extends PaymentHistoryEvent {
  final int page;
  final int limit;
  final bool isRefresh;

  const LoadPaymentHistoryEvent({
    this.page = 1,
    this.limit = 20,
    this.isRefresh = false,
  });

  @override
  List<Object?> get props => [page, limit, isRefresh];
}

/// Event to refresh payment history
class RefreshPaymentHistoryEvent extends PaymentHistoryEvent {
  const RefreshPaymentHistoryEvent();
}

/// Event to load more payment history (pagination)
class LoadMorePaymentHistoryEvent extends PaymentHistoryEvent {
  const LoadMorePaymentHistoryEvent();
}
