part of 'payment_history_bloc.dart';

/// Enum for payment history status
enum PaymentHistoryStatus {
  initial,
  loading,
  success,
  failure,
  loadAgain
}

/// Error type enum for better error handling
enum PaymentHistoryErrorType { network, api, unknown }

/// State class for payment history
class PaymentHistoryState extends Equatable {
  final PaymentHistoryStatus status;
  final List<PaymentHistory> paymentHistory;
  final String? message;
  final PaymentHistoryErrorType? errorType;
  final int currentPage;
  final int totalPages;
  final bool hasNextPage;
  final bool isRefreshing;
  final bool isLoadingMore;
  final bool hasRefreshError;

  const PaymentHistoryState({
    this.status = PaymentHistoryStatus.initial,
    this.paymentHistory = const [],
    this.message,
    this.errorType,
    this.currentPage = 1,
    this.totalPages = 1,
    this.hasNextPage = false,
    this.isRefreshing = false,
    this.isLoadingMore = false,
    this.hasRefreshError = false,
  });

  /// Copy with method for state updates
  PaymentHistoryState copyWith({
    PaymentHistoryStatus? status,
    List<PaymentHistory>? paymentHistory,
    String? message,
    PaymentHistoryErrorType? errorType,
    int? currentPage,
    int? totalPages,
    bool? hasNextPage,
    bool? isRefreshing,
    bool? isLoadingMore,
    bool? hasRefreshError,
  }) {
    return PaymentHistoryState(
      status: status ?? this.status,
      paymentHistory: paymentHistory ?? this.paymentHistory,
      message: message ?? this.message,
      errorType: errorType ?? this.errorType,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasRefreshError: hasRefreshError ?? this.hasRefreshError,
    );
  }

  /// Check if there are any payment history items loaded
  bool get hasAnyPaymentHistory => paymentHistory.isNotEmpty;

  /// Check if state is loading
  bool get isLoading => status == PaymentHistoryStatus.loading;

  /// Check if state is success
  bool get isSuccess => status == PaymentHistoryStatus.success;

  /// Check if state is failure
  bool get isFailure => status == PaymentHistoryStatus.failure;

  /// Check if state is initial
  bool get isInitial => status == PaymentHistoryStatus.initial;

  /// Check if error is network related
  bool get isNetworkError => isFailure && errorType == PaymentHistoryErrorType.network;

  /// Check if error is API related
  bool get isApiError => isFailure && errorType == PaymentHistoryErrorType.api;

  @override
  List<Object?> get props => [
        status,
        paymentHistory,
        message,
        errorType,
        currentPage,
        totalPages,
        hasNextPage,
        isRefreshing,
        isLoadingMore,
        hasRefreshError,
      ];

  @override
  String toString() {
    return 'PaymentHistoryState(status: $status, message: $message, isRefreshing: $isRefreshing, hasRefreshError: $hasRefreshError, paymentHistoryCount: ${paymentHistory.length})';
  }
}
