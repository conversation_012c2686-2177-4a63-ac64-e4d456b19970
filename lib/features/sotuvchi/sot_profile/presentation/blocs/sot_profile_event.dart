import 'package:equatable/equatable.dart';

/// Base class for sot profile events
abstract class SotProfileEvent extends Equatable {
  const SotProfileEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load user profile
class LoadSotProfileEvent extends SotProfileEvent {
  final String userId;
  final bool forceRefresh;

  const LoadSotProfileEvent({
    required this.userId,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [userId, forceRefresh];
}

/// Event to refresh user profile
class RefreshSotProfileEvent extends SotProfileEvent {
  final String userId;

  const RefreshSotProfileEvent({required this.userId});

  @override
  List<Object?> get props => [userId];
}

/// Event to clear user profile cache
class ClearSotProfileEvent extends SotProfileEvent {
  const ClearSotProfileEvent();
}

/// Event to load cached user profile
class LoadCachedSotProfileEvent extends SotProfileEvent {
  const LoadCachedSotProfileEvent();
}

/// Event to update user profile image
class UpdateSotProfileImageEvent extends SotProfileEvent {
  final String userId;
  final String imagePath;

  const UpdateSotProfileImageEvent({
    required this.userId,
    required this.imagePath,
  });

  @override
  List<Object?> get props => [userId, imagePath];
}
