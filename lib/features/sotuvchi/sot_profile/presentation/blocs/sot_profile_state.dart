import 'package:equatable/equatable.dart';
import '../../models/sotuvchi_profile_model.dart';

/// Base class for sot profile states
abstract class SotProfileState extends Equatable {
  const SotProfileState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class SotProfileInitial extends SotProfileState {
  const SotProfileInitial();
}

/// Loading state
class SotProfileLoading extends SotProfileState {
  const SotProfileLoading();
}

/// Success state with user profile data
class SotProfileLoaded extends SotProfileState {
  final SotuvchiProfile userProfile;
  final bool isFromCache;
  final String? message;

  const SotProfileLoaded({
    required this.userProfile,
    this.isFromCache = false,
    this.message,
  });

  @override
  List<Object?> get props => [userProfile, isFromCache, message];
}

/// Error state
class SotProfileError extends SotProfileState {
  final String message;
  final SotuvchiProfile? cachedProfile;

  const SotProfileError({
    required this.message,
    this.cachedProfile,
  });

  @override
  List<Object?> get props => [message, cachedProfile];
}

/// Refreshing state (when profile is already loaded but refreshing)
class SotProfileRefreshing extends SotProfileState {
  final SotuvchiProfile currentProfile;

  const SotProfileRefreshing({required this.currentProfile});

  @override
  List<Object?> get props => [currentProfile];
}

/// Updating profile image state
class SotProfileImageUpdating extends SotProfileState {
  final SotuvchiProfile currentProfile;

  const SotProfileImageUpdating({required this.currentProfile});

  @override
  List<Object?> get props => [currentProfile];
}

/// Profile image updated successfully state
class SotProfileImageUpdated extends SotProfileState {
  final SotuvchiProfile updatedProfile;
  final String message;

  const SotProfileImageUpdated({
    required this.updatedProfile,
    required this.message,
  });

  @override
  List<Object?> get props => [updatedProfile, message];
}

/// Profile image update failed state
class SotProfileImageUpdateFailed extends SotProfileState {
  final String message;
  final SotuvchiProfile originalProfile;

  const SotProfileImageUpdateFailed({
    required this.message,
    required this.originalProfile,
  });

  @override
  List<Object?> get props => [message, originalProfile];
}
