/// Sotuvchi profile model for seller information
class SotuvchiProfile {
  final String id;
  final String firstName;
  final String lastName;
  final String middleName;
  final String role;
  final Market? market;
  final Pavilion? pavilion;
  final String? image;
  final String phone;
  final String address;
  final List<Block> blocks;
  final List<Place> places;
  final bool gender;

  const SotuvchiProfile({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.middleName,
    required this.role,
    this.market,
    this.pavilion,
    this.image,
    required this.phone,
    required this.address,
    required this.blocks,
    required this.places,
    required this.gender,
  });

  /// Create SotuvchiProfile from JSON
  factory SotuvchiProfile.fromJson(Map<String, dynamic> json) {
    return SotuvchiProfile(
      id: json['_id'] ?? '',
      firstName: json['firstName'] ?? '',
      lastName: json['lastName'] ?? '',
      middleName: json['middleName'] ?? '',
      role: json['role'] ?? '',
      market: json['market'] != null ? Market.fromJson(json['market']) : null,
      pavilion: json['pavilion'] != null ? Pavilion.fromJson(json['pavilion']) : null,
      image: json['image'],
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      blocks: (json['blocks'] as List<dynamic>?)
          ?.map((block) => Block.fromJson(block))
          .toList() ?? [],
      places: (json['places'] as List<dynamic>?)
          ?.map((place) => Place.fromJson(place))
          .toList() ?? [],
      gender: json['gender'] ?? true,
    );
  }

  /// Convert SotuvchiProfile to JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'firstName': firstName,
      'lastName': lastName,
      'middleName': middleName,
      'role': role,
      'market': market?.toJson(),
      'pavilion': pavilion?.toJson(),
      'image': image,
      'phone': phone,
      'address': address,
      'blocks': blocks.map((block) => block.toJson()).toList(),
      'places': places.map((place) => place.toJson()).toList(),
      'gender': gender,
    };
  }

  /// Get full name
  String get fullName => '$firstName $lastName';

  /// Get full name with middle name
  String get fullNameWithMiddle => '$firstName $lastName $middleName';

  /// Get formatted phone number
  String get formattedPhone {
    if (phone.length >= 9) {
      final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanPhone.length == 9) {
        return '+998 ${cleanPhone.substring(0, 2)} ${cleanPhone.substring(2, 5)}-${cleanPhone.substring(5, 7)}-${cleanPhone.substring(7)}';
      }
    }
    return phone;
  }

  /// Get places display text (as "Rasta")

  /// Copy with method for updating specific fields
  SotuvchiProfile copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? middleName,
    String? role,
    Market? market,
    Pavilion? pavilion,
    String? image,
    String? phone,
    String? address,
    List<Block>? blocks,
    List<Place>? places,
    bool? gender,
  }) {
    return SotuvchiProfile(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      middleName: middleName ?? this.middleName,
      role: role ?? this.role,
      market: market ?? this.market,
      pavilion: pavilion ?? this.pavilion,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      blocks: blocks ?? this.blocks,
      places: places ?? this.places,
      gender: gender ?? this.gender,
    );
  }

  @override
  String toString() {
    return 'SotuvchiProfile(id: $id, firstName: $firstName, lastName: $lastName, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SotuvchiProfile &&
        other.id == id &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.middleName == middleName &&
        other.role == role &&
        other.market == market &&
        other.pavilion == pavilion &&
        other.image == image &&
        other.phone == phone &&
        other.address == address &&
        other.blocks == blocks &&
        other.places == places &&
        other.gender == gender;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      firstName,
      lastName,
      middleName,
      role,
      market,
      pavilion,
      image,
      phone,
      address,
      blocks,
      places,
      gender,
    );
  }
}

/// Market model
class Market {
  final String id;
  final String title;

  const Market({
    required this.id,
    required this.title,
  });

  factory Market.fromJson(Map<String, dynamic> json) {
    return Market(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Market && other.id == id && other.title == title;
  }

  @override
  int get hashCode => Object.hash(id, title);
}

/// Pavilion model
class Pavilion {
  final String id;
  final String title;

  const Pavilion({
    required this.id,
    required this.title,
  });

  factory Pavilion.fromJson(Map<String, dynamic> json) {
    return Pavilion(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Pavilion && other.id == id && other.title == title;
  }

  @override
  int get hashCode => Object.hash(id, title);
}

/// Block model
class Block {
  final String id;
  final String title;
  final String? shortDesc;

  const Block({
    required this.id,
    required this.title,
    this.shortDesc,
  });

  factory Block.fromJson(Map<String, dynamic> json) {
    return Block(
      id: json['_id'] ?? '',
      title: json['title'] ?? '',
      shortDesc: json['shortDesc'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'shortDesc': shortDesc,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Block &&
        other.id == id &&
        other.title == title &&
        other.shortDesc == shortDesc;
  }

  @override
  int get hashCode => Object.hash(id, title, shortDesc);
}

/// Place model
class Place {
  final String id;
  final dynamic title; // Can be int or String
  final int price; // Price field for daily rate calculation

  const Place({
    required this.id,
    required this.title,
    required this.price,
  });

  factory Place.fromJson(Map<String, dynamic> json) {
    return Place(
      id: json['_id'] ?? '',
      title: json['title'], // Keep as dynamic to handle both int and String
      price: json['price'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'price': price,
    };
  }

  String get displayTitle => title.toString();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Place &&
           other.id == id &&
           other.title == title &&
           other.price == price;
  }

  @override
  int get hashCode => Object.hash(id, title, price);
}

/// Response model for sotuvchi profile API
class SotuvchiProfileResponse {
  final bool success;
  final SotuvchiProfile? sotuvchiProfile;
  final String? message;
  final String? error;

  const SotuvchiProfileResponse({
    required this.success,
    this.sotuvchiProfile,
    this.message,
    this.error,
  });

  factory SotuvchiProfileResponse.fromJson(Map<String, dynamic> json) {
    return SotuvchiProfileResponse(
      success: true, // API returns user data directly on success
      sotuvchiProfile: SotuvchiProfile.fromJson(json),
      message: 'Profile loaded successfully',
    );
  }

  factory SotuvchiProfileResponse.error(String error) {
    return SotuvchiProfileResponse(
      success: false,
      error: error,
      message: 'Failed to load profile',
    );
  }
}
