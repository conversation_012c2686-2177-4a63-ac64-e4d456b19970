import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:get_storage/get_storage.dart';
import 'package:settings_ui/settings_ui.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../translations/locale_keys.g.dart';
import 'bio_lock_page.dart';

class SettingsPage extends StatefulWidget {
  final BioLockPageState homePageState;

  const SettingsPage({super.key, required this.homePageState});

  @override
  SettingsPageState createState() => SettingsPageState();
}

class LivenessDetectionLevel {
  String levelName;
  int levelValue;

  LivenessDetectionLevel(this.levelName, this.levelValue);
}

const double _kItemExtent = 40.0;
const List<String> _livenessLevelNames = <String>[
  'Best Accuracy',
  'Light Weight',
];

class SettingsPageState extends State<SettingsPage> {
  bool _cameraLens = false;
  String _livenessThreshold = MAX_LIVENESS;
  String _identifyThreshold = MAX_IDENTIFY;
  List<LivenessDetectionLevel> livenessDetectionLevel = [
    LivenessDetectionLevel('Best Accuracy', 0),
    LivenessDetectionLevel('Light Weight', 1),
  ];
  int _selectedLivenessLevel = 0;

  final livenessController = TextEditingController();
  final identifyController = TextEditingController();

  static Future<void> initSettings() async {
    final storage = GetStorage();
    var firstWrite = storage.read("first_write") ?? 0;
    if (firstWrite == 0) {
      await storage.write("first_write", 1);
      await storage.write("camera_lens", 1);
      await storage.write("liveness_level", 0);
      await storage.write("liveness_threshold", MAX_LIVENESS);
      await storage.write("identify_threshold", MAX_IDENTIFY);
    }
  }

  @override
  void initState() {
    super.initState();

    loadSettings();
  }

  Future<void> loadSettings() async {
    final storage = GetStorage();
    var cameraLens = storage.read("camera_lens") ?? 1;
    var livenessLevel = storage.read("liveness_level") ?? 0;
    var livenessThreshold = storage.read("liveness_threshold") ?? MAX_LIVENESS;
    var identifyThreshold = storage.read("identify_threshold") ?? MAX_IDENTIFY;

    setState(() {
      _cameraLens = cameraLens == 1 ? true : false;
      _livenessThreshold = livenessThreshold;
      _identifyThreshold = identifyThreshold;
      _selectedLivenessLevel = livenessLevel;
      livenessController.text = _livenessThreshold;
      identifyController.text = _identifyThreshold;
    });
  }

  Future<void> restoreSettings() async {
    final storage = GetStorage();
    await storage.write("first_write", 0);
    await initSettings();
    await loadSettings();

    Fluttertoast.showToast(
        msg: "Default settings restored!",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: Colors.red,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  Future<void> updateLivenessLevel(value) async {
    final storage = GetStorage();
    await storage.write("liveness_level", value);
  }

  Future<void> updateCameraLens(value) async {
    final storage = GetStorage();
    await storage.write("camera_lens", value ? 1 : 0);

    setState(() {
      _cameraLens = value;
    });
  }

  Future<void> updateLivenessThreshold(BuildContext context) async {
    try {
      var doubleValue = double.parse(livenessController.text);
      if (doubleValue >= 0 && doubleValue < 1.0) {
        final storage = GetStorage();
        await storage.write("liveness_threshold", livenessController.text);

        setState(() {
          _livenessThreshold = livenessController.text;
        });
      }
    } catch (e) {}

    // ignore: use_build_context_synchronously
    Navigator.pop(context, 'OK');
    setState(() {
      livenessController.text = _livenessThreshold;
    });
  }

  Future<void> updateIdentifyThreshold(BuildContext context) async {
    try {
      var doubleValue = double.parse(identifyController.text);
      if (doubleValue >= 0 && doubleValue < 1.0) {
        final storage = GetStorage();
        await storage.write("identify_threshold", identifyController.text);

        setState(() {
          _identifyThreshold = identifyController.text;
        });
      }
    } catch (e) {}

    // ignore: use_build_context_synchronously
    Navigator.pop(context, 'OK');
    setState(() {
      identifyController.text = _identifyThreshold;
    });
  }

// This shows a CupertinoModalPopup with a reasonable fixed height which hosts CupertinoPicker.
  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: 216,
        padding: const EdgeInsets.only(top: 6.0),
        // The Bottom margin is provided to align the popup above the system navigation bar.
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        // Provide a background color for the popup.
        color: CupertinoColors.systemBackground.resolveFrom(context),
        // Use a SafeArea widget to avoid system overlaps.
        child: SafeArea(
          top: false,
          child: child,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.cFirstColor,
        title: Text(LocaleKeys.face_control_settings_page_title.tr()),
        toolbarHeight: 70,
        centerTitle: true,
      ),
      body: SettingsList(
        sections: [
          SettingsSection(
            title: Text(LocaleKeys.face_control_settings_page_camera_lens.tr()),
            tiles: <SettingsTile>[
              SettingsTile.switchTile(
                onToggle: (value) {
                  updateCameraLens(value);
                },
                initialValue: _cameraLens,
                leading: const Icon(Icons.camera),
                title: Text(LocaleKeys.face_control_settings_page_front.tr()),
              ),
            ],
          ),
          SettingsSection(
            title: Text(LocaleKeys.face_control_settings_page_thresholds.tr()),
            tiles: <SettingsTile>[
              SettingsTile.navigation(
                title: Text(LocaleKeys.face_control_settings_page_liveness_level.tr()),
                value: Text(_livenessLevelNames[_selectedLivenessLevel]),
                leading: const Icon(Icons.person_pin_outlined),
                onPressed: (value) => _showDialog(
                  CupertinoPicker(
                    magnification: 1.22,
                    squeeze: 1.2,
                    useMagnifier: true,
                    itemExtent: _kItemExtent,
                    // This sets the initial item.
                    scrollController: FixedExtentScrollController(
                      initialItem: _selectedLivenessLevel,
                    ),
                    // This is called when selected item is changed.
                    onSelectedItemChanged: (int selectedItem) {
                      setState(() {
                        _selectedLivenessLevel = selectedItem;
                      });
                      updateLivenessLevel(selectedItem);
                    },
                    children: List<Widget>.generate(_livenessLevelNames.length,
                        (int index) {
                      return Center(child: Text(_livenessLevelNames[index]));
                    }),
                  ),
                ),
              ),
              SettingsTile.navigation(
                title: Text(LocaleKeys.face_control_settings_page_liveness_threshold.tr()),
                value: Text(_livenessThreshold),
                leading: const Icon(Icons.person_pin_outlined),
                onPressed: (value) => showDialog<String>(
                  context: context,
                  builder: (BuildContext context) => AlertDialog(
                    title: Text(LocaleKeys.face_control_settings_page_liveness_threshold.tr()),
                    content: TextField(
                      controller: livenessController,
                      onChanged: (value) => {},
                    ),
                    actions: <Widget>[
                      TextButton(
                        onPressed: () => Navigator.pop(context, 'Cancel'),
                        child: Text(LocaleKeys.face_control_settings_page_cancel.tr()),
                      ),
                      TextButton(
                        onPressed: () => updateLivenessThreshold(context),
                        child: Text(LocaleKeys.face_control_settings_page_ok.tr()),
                      ),
                    ],
                  ),
                ),
              ),
              SettingsTile.navigation(
                title: Text(LocaleKeys.face_control_settings_page_identify_threshold.tr()),
                leading: const Icon(Icons.person_search),
                value: Text(_identifyThreshold),
                onPressed: (value) => showDialog<String>(
                  context: context,
                  builder: (BuildContext context) => AlertDialog(
                    title: Text(LocaleKeys.face_control_settings_page_identify_threshold.tr()),
                    content: TextField(
                      controller: identifyController,
                      onChanged: (value) => {},
                    ),
                    actions: <Widget>[
                      TextButton(
                        onPressed: () => Navigator.pop(context, 'Cancel'),
                        child: Text(LocaleKeys.face_control_settings_page_cancel.tr()),
                      ),
                      TextButton(
                        onPressed: () => updateIdentifyThreshold(context),
                        child: Text(LocaleKeys.face_control_settings_page_ok.tr()),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SettingsSection(
            title: Text(LocaleKeys.face_control_settings_page_reset.tr()),
            tiles: <SettingsTile>[
              SettingsTile.navigation(
                title: Text(LocaleKeys.face_control_settings_page_restore_default.tr()),
                leading: const Icon(Icons.restore),
                onPressed: (value) => restoreSettings(),
              ),
              SettingsTile.navigation(
                title: Text(LocaleKeys.face_control_settings_page_clear_all_person.tr()),
                leading: const Icon(Icons.clear_all),
                onPressed: (value) {
                  widget.homePageState.deleteAllPerson();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
