import 'dart:ui';

import 'package:click_bazaar/core/function/functions.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ntp/ntp.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get_storage/get_storage.dart';
import 'package:click_bazaar/core/location/location_service.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/widgets/custom_toast.dart';
import 'package:click_bazaar/di/dependency_injection.dart' as di;
import 'package:click_bazaar/core/extensions/context_extensions.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/utils/jwt_decoder.dart';
import 'package:click_bazaar/features/auth/models/user_profile_model.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:click_bazaar/core/services/navigation_service.dart';

import '../../core/utils/api_path.dart';
import 'lock_switcher.dart';

///TODO: Use cached image for background / Future stuck loading (why)
class FunctionalLockPage extends StatefulWidget {
  const FunctionalLockPage({super.key});

  @override
  State<FunctionalLockPage> createState() => _FunctionalLockPageState();
}

class _FunctionalLockPageState extends State<FunctionalLockPage> {
  final GetStorage storage = di.di();
  final NetworkInfo networkInfo = di.di();
  final Dio dio = di.di();
  var sm = SessionManager();
  late Future<void> _initFuture;
  late Future<void> _initFutureTime;

  late final AppLifecycleListener _listener;
  bool _shouldRequestPermission = false;

  @override
  void initState() {
    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );
    getLive();
    setLive(true);

    reInitializerButton();
    reInitializerButtonTime();
    initialQueries();
    super.initState();
  }

  setLive(bool live) async {
    await sm.set(functional_live, live);
  }

  getLive() async {
    print('IS FUNCTIONAL LIVE: ${await sm.get(functional_live)}');
  }

  reInitializerButton() {
    setState(() {
      _initFuture = permissionWall(context);
    });
  }

  reInitializerButtonTime() {
    setState(() {
      _initFutureTime = getServerTime();
    });
  }

  @override
  void dispose() {
    setLive(false);
    _listener.dispose();
    super.dispose();
  }

  void _onDetached() => print('detached');

  void _onResumed() async {
    if (_shouldRequestPermission) {
      _shouldRequestPermission = false;
      setLive(true);
    }
  }

  void _onInactive() {
    setLive(true);
    _shouldRequestPermission = true;
  }

  void _onHidden() => setLive(true);

  void _onPaused() {
    setLive(true);
    _shouldRequestPermission = true;
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  Future<void> initialQueries() async {
    String? imageUrl;
    UserProfile user = UserProfile();

    final isConnected = await networkInfo.isConnected;
    if (!isConnected) {
      CustomToast.showToast(LocaleKeys.face_control_errors_check_internet.tr());
    }

    if (isConnected) {
      try {
        // Get token and determine user role
        final token = storage.read(TOKEN);
        final isDemoMode = storage.read(is_demo) ?? false;

        String? userId;
        String? userRole;
        String apiEndpoint;

        if (token != null) {
          // Regular authenticated user
          userId = JwtDecoder.getUserId(token);
          userRole = JwtDecoder.getUserRole(token);
        } else if (isDemoMode) {
          // Demo mode - extract user ID from guest token
          userId = JwtDecoder.getUserId(GUEST_TOKEN);
          userRole = JwtDecoder.getUserRole(GUEST_TOKEN);
        }

        if (userId == null || userRole == null) {
          CustomToast.showToast(
              LocaleKeys.face_control_errors_user_not_found.tr());
          return;
        }

        // Determine the correct API endpoint based on user role
        if (userRole == 'supervisor') {
          apiEndpoint =
              '${ApiPath.baseUrl}${ApiPath.supervisorProfilePath}/$userId';
        } else if (userRole == 'seller') {
          apiEndpoint =
              '${ApiPath.baseUrl}${ApiPath.sellerProfilePath}/$userId';
        } else {
          CustomToast.showToast(
              '${LocaleKeys.face_control_errors_unknown_role.tr()}$userRole');
          return;
        }

        print('Fetching user profile from: $apiEndpoint');
        print('User role: $userRole, User ID: $userId');

        final response = await dio.get(apiEndpoint);
        if (response.statusCode == 200 && response.data != null) {
          user = UserProfile.fromJson(response.data);

          // Check if user data is valid
          if (user.isValid) {
            // Cache user profile using universal cache
            final cacheKey = userRole == 'supervisor'
                ? CacheKeys.supervisorProfile
                : CacheKeys.sellerProfile;
            await CacheHelper.cacheUserProfile<UserProfile>(
              cacheKey: cacheKey,
              profile: user,
              toJsonFunction: () => user.toJson(),
            );

            imageUrl = user.face;

            print('User image: $imageUrl');
            print('User model: ${user.toJson()}');
            print('User profile cached with key: $cacheKey');
          } else {
            print('Invalid user data received from API');
            CustomToast.showToast(
                LocaleKeys.face_control_errors_invalid_user_data.tr());
          }
        }
      } catch (e) {
        storage.write(server_approved, false);
        storage.write(local_approved, false);
        print(e);
        CustomToast.showToast(
            '${LocaleKeys.face_control_errors_profile_load_error.tr()}$e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    var time = storage.read(is_time_correct) ?? false;
    var location = storage.read(is_gps_active) ?? false;
    var mockLocation = storage.read(is_not_mocked) ?? false;

    print('REBUILD ----- PAGE');

    print('Time approved: $time\n');
    print('GPS approved: $location\n');
    print('MOCK approved: $mockLocation\n');

    var unlocked = (location && mockLocation && time);

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: Padding(
        padding: EdgeInsets.symmetric(horizontal: 28).copyWith(bottom: 50.h),
        child: SizedBox(
          height: 60.h,
          width: double.infinity,
          child: FloatingActionButton.extended(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0)),
            backgroundColor: unlocked
                ? Theme.of(context).colorScheme.secondary
                : Theme.of(context).colorScheme.error,
            label: SizedBox(
              child: Text(
                LocaleKeys.face_control_functional_page_unlock_app.tr(),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontSize: 15.sp),
              ),
            ),
            onPressed: () {
              if (unlocked) {
                final navigator = NavigationService.navigatorKey.currentState;
                if (navigator != null) {
                  navigator.pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => LockProvider()),
                    (route) => false,
                  );
                }
              } else {
                CustomToast.showToast(LocaleKeys
                    .face_control_functional_page_please_fix_issues
                    .tr());
              }
            },
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
            image: DecorationImage(
          opacity: 0.1,
          image: AssetImage(Assets.imagesLockPattern),
          fit: BoxFit.cover,
        )),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.only(
                      right: context.isTablet ? 30.w : 8.w, top: 40.h),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    onPressed: () {
                      showDialog(
                          context: context,
                          builder: (_) {
                            return BackdropFilter(
                                filter:
                                    ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                                child: Dialog(
                                  backgroundColor:
                                      Theme.of(context).cardTheme.color,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15.r),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.all(20.h),
                                    child: Text(
                                        "${LocaleKeys.face_control_functional_page_support_info.tr()}\n\n$SUPPORT_TEL1\n$SUPPORT_TEL2\n" /* "$TELEGRAM_URL"*/),
                                  ),
                                ));
                          });
                    },
                    icon: Icon(
                      Icons.info,
                      size: context.isTablet ? 40.h : 35.h,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                )),
            Column(
              children: [
                SizedBox(height: 100.h),
                Expanded(
                    flex: 3,
                    child: SvgPicture.asset(
                      Assets.iconsBustedLock,
                      height: 250.h,
                    )),
                Expanded(
                  flex: 4,
                  child: ListView(
                    physics: BouncingScrollPhysics(
                        parent: AlwaysScrollableScrollPhysics()),
                    children: [
                      FutureBuilder(
                          future: _initFutureTime,
                          builder: (context, snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  // Otherwise, display a loading indicator.
                                  return functionButton(
                                      Icons.lock_clock,
                                      'TIME',
                                      LocaleKeys
                                          .face_control_functional_page_time_check
                                          .tr(),
                                      time,
                                      context,
                                      true);
                                }
                              default:
                                return functionButton(
                                    Icons.lock_clock,
                                    'TIME',
                                    LocaleKeys
                                        .face_control_functional_page_time_check
                                        .tr(),
                                    time,
                                    context);
                            }
                          }),
                      FutureBuilder(
                          future: _initFuture,
                          builder: (context, snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  // Otherwise, display a loading indicator.
                                  return functionButton(
                                      Icons.gps_fixed,
                                      'LOCATION',
                                      LocaleKeys
                                          .face_control_functional_page_location_check
                                          .tr(),
                                      location,
                                      context,
                                      true);
                                }
                              default:
                                return functionButton(
                                    Icons.gps_fixed,
                                    'LOCATION',
                                    LocaleKeys
                                        .face_control_functional_page_location_check
                                        .tr(),
                                    location,
                                    context);
                            }
                          }),
                      FutureBuilder(
                          future: _initFuture,
                          builder: (context, snapshot) {
                            switch (snapshot.connectionState) {
                              case ConnectionState.waiting:
                                {
                                  // Otherwise, display a loading indicator.
                                  return functionButton(
                                      Icons.wrong_location_sharp,
                                      'MOCK',
                                      LocaleKeys
                                          .face_control_functional_page_mock_location_check
                                          .tr(),
                                      mockLocation,
                                      context,
                                      true);
                                }
                              default:
                                return functionButton(
                                    Icons.wrong_location_sharp,
                                    'MOCK',
                                    LocaleKeys
                                        .face_control_functional_page_mock_location_check
                                        .tr(),
                                    mockLocation,
                                    context);
                            }
                          })
                    ],
                  ),
                ),
                Spacer(
                  flex: 2,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget functionButton(IconData icon, String functionCode, String functionName,
      bool isGranted, BuildContext context,
      [isLoading = false]) {
    return InkWell(
      onTap: () {
        checkFunction(functionCode, context);
      },
      child: Container(
        width: double.infinity,
        height: 75.h,
        margin: EdgeInsets.only(left: 25.w, right: 25.w, bottom: 10.h),
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 4.h),
        decoration: ShapeDecoration(
          color: Theme.of(context).primaryColor.withAlpha(200),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(36.r),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Expanded(
              child: Icon(
                icon,
                color: Theme.of(context).colorScheme.onSurface,
                size: 25.h,
              ),
            ),
            Expanded(
              flex: 6,
              child: Padding(
                padding: EdgeInsets.all(8.h),
                child: Text(
                  functionName,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 14.sp),
                ),
              ),
            ),
            Expanded(
              child: CircleAvatar(
                radius: 30.r,
                backgroundColor: isLoading
                    ? Theme.of(context).colorScheme.secondary
                    : isGranted
                        ? Theme.of(context).colorScheme.secondary
                        : Theme.of(context).colorScheme.error,
                child: isLoading
                    ? Center(
                        child: CupertinoActivityIndicator(
                        color: Theme.of(context).colorScheme.onSecondary,
                        radius: 8.r,
                      ))
                    : Icon(
                        isGranted ? Icons.gpp_good : Icons.gpp_bad,
                        color: Theme.of(context).colorScheme.onSecondary,
                        size: 22.h,
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.location,
    ].request();

    return statuses;
  }

  Future<void> permissionWall(BuildContext context) async {
    Map<Permission, PermissionStatus> statuses = {};
    statuses = await requestPermissions();

    if (statuses[Permission.location] != PermissionStatus.granted) {
      if (statuses[Permission.location] == PermissionStatus.permanentlyDenied) {
        print(statuses[Permission.location]);

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(context);
      } else {
        ///Points to the recursion
        await permissionWall(context);
      }
    } else {
      try {
        if (await networkInfo.isConnected) {
          await determinePosition();
          setState(() {});
        } else {
          CustomToast.showToast(
              LocaleKeys.face_control_errors_check_internet.tr());
        }
      } catch (e) {
        print(e);
      }
    }
  }

  void checkFunction(String langCode, BuildContext context) async {
    switch (langCode) {
      case "TIME":
        {
          CustomToast.showToast(
              LocaleKeys.face_control_functional_page_waiting.tr());
          reInitializerButtonTime();
        }
        break;

      case "LOCATION":
        {
          CustomToast.showToast(
              LocaleKeys.face_control_functional_page_waiting.tr());
          reInitializerButton();
        }

        break;

      case "MOCK":
        {
          CustomToast.showToast(
              LocaleKeys.face_control_functional_page_waiting.tr());
          reInitializerButton();
        }

        break;

      default:
        // Handle the default case or leave it empty if not needed
        break;
    }
  }

  ///TODO: Write this function to app.class (startup) to prevent hacking time
  getServerTime() async {
    ///TODO: Use forEach if necessary
    var _lookUpAddres = 'time.google.com';

    if (await networkInfo.isConnected) {
      DateTime _myTime;
      DateTime _ntpTime;

      /// Or you could get NTP current (It will call DateTime.now() and add NTP offset to it)
      _myTime = DateTime.now();

      /// Or get NTP offset (in milliseconds) and add it yourself
      final int offset = await NTP.getNtpOffset(
          localTime: _myTime,
          lookUpAddress: _lookUpAddres,
          timeout: Duration(seconds: 5));

      _ntpTime = _myTime.add(Duration(milliseconds: offset));
      var difference = _myTime.difference(_ntpTime).inMinutes.abs();

      print('\n==== $_lookUpAddres ====');
      print('My time: $_myTime');
      print('NTP time: $_ntpTime');
      print('Difference: $difference minute');

      if (difference < ALLOWED_MINUTE) {
        setState(() {
          storage.write(is_time_correct, true);
        });
      } else {
        setState(() {
          storage.write(is_time_correct, false);
        });

        CustomToast.showToast(LocaleKeys
            .face_control_functional_page_time_difference
            .tr(namedArgs: {'Farq': difference.toString()}));
      }

      return;
    } else {
      CustomToast.showToast(LocaleKeys.common_no_internet_connection.tr());
    }
  }
}
