import 'dart:async';
import 'dart:io';

import 'package:click_bazaar/core/utils/jwt_decoder.dart';
import 'package:dio/dio.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:http_parser/http_parser.dart';
import 'package:lottie/lottie.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'package:pausable_timer/pausable_timer.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get_storage/get_storage.dart';
import 'package:facesdk_plugin/facedetection_interface.dart';
import 'package:facesdk_plugin/facesdk_plugin.dart';
import 'package:click_bazaar/core/function/functions.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/widgets/custom_toast.dart';
import 'package:click_bazaar/di/dependency_injection.dart' as di;
import 'package:click_bazaar/features/face_control/bio_lock_page.dart';
import 'package:click_bazaar/features/face_control/lock_switcher.dart';
import 'package:click_bazaar/generated/assets.dart';

import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:click_bazaar/core/services/navigation_service.dart';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/extensions/context_extensions.dart';
import 'package:vibration/vibration.dart';
import 'package:vibration/vibration_presets.dart';
import 'person.dart';

// ignore: must_be_immutable
class FaceRecognitionView extends StatefulWidget {
  List<Person>? personList;
  FaceDetectionViewController? faceDetectionViewController;
  Function()? afterRecognized;

  Widget? child;
  final bool server_approved;
  final bool? debug;
  final bool? getBackAfterRecognized;

  FaceRecognitionView(
      {super.key,
      this.personList,
      this.child,
      this.server_approved = true,
      this.afterRecognized,
      this.debug = false,
      this.getBackAfterRecognized});

  @override
  State<StatefulWidget> createState() => FaceRecognitionViewState();
}

class FaceRecognitionViewState extends State<FaceRecognitionView> {
  dynamic _faces;
  double _livenessThreshold = 0;
  double _identifyThreshold = 0;
  bool _recognized = false;
  String _identifiedName = "";
  String _identifiedSimilarity = "";
  String _identifiedLiveness = "";
  String _identifiedYaw = "";
  String _identifiedRoll = "";
  String _identifiedPitch = "";
  bool invalidHolding = false;
  late PausableTimer timer;
  final StreamController<NativeDeviceOrientation> _orientationStreamController =
      StreamController<NativeDeviceOrientation>();
  late final StreamSubscription streamSubscription;
  var communicator = NativeDeviceOrientationCommunicator();
  final ValueNotifier<NativeDeviceOrientation> invalidNotifier =
      ValueNotifier<NativeDeviceOrientation>(NativeDeviceOrientation.unknown);

  ///For UI
  double _similarity = 0.0;
  double _liveness = 0.0;
  late final AppLifecycleListener _listener;
  bool _shouldRequestPermission = false;

  // ignore: prefer_typing_uninitialized_variables
  var _identifiedFace;

  // ignore: prefer_typing_uninitialized_variables
  var _enrolledFace;
  final _faceSdkPlugin = FacesdkPlugin();
  FaceDetectionViewController? faceDetectionViewController;
  final GetStorage storage = di.di();
  bool _timerInitialized = false;

  ///Cannot request due plugin request
  Future<Map<Permission, PermissionStatus>> requestPermissions() async {
    return await [Permission.camera].request();
  }

  // Function to check camera permission status
  Future<void> _checkCameraPermissionStatus() async {
    var status = await Permission.camera.status;
    if (status.isGranted) {
      debugPrint('Camera permission is granted');
    } else if (status.isDenied) {
      debugPrint('Camera permission is denied');
    } else if (status.isPermanentlyDenied) {
      debugPrint('Camera permission is permanently denied');
    } else if (status.isRestricted) {
      debugPrint('Camera permission is restricted');
    } else if (status.isLimited) {
      debugPrint('Camera permission is limited');
    }
  }

  Future<void> permissionWall() async {
    _checkCameraPermissionStatus();

    var status = await Permission.camera.status;

    if (status != PermissionStatus.granted) {
      if (status == PermissionStatus.permanentlyDenied) {
        debugPrint('$status');

        ///Shows custom dialog after user refuses for giving of any permissions
        showCustomDialog(context);
      } else {
        showCustomDialog(context);
      }
    }
    // Otherwise permissions are granted, no action needed
  }

  @override
  void initState() {
    super.initState();

    // Start by initializing streams and permissions
    startListeningToOrientationStream(useSensor: true);
    permissionWall();

    _listener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );

    // Handle debug mode immediately
    if (widget.debug ?? false) {
      _handleDebugMode();
    }

    // Load settings without vibrating
    loadSettings();

    // Initialize timer without starting it
    initializeTimer();
  }

  // Extracted debug mode handling to a separate method
  void _handleDebugMode() {
    if (widget.afterRecognized != null) {
      widget.afterRecognized!();
    }

    if (widget.child != null) {
      WidgetsBinding.instance.addPostFrameCallback((time) {
        Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => widget.child!));
      });
    } else {
      Navigator.pop(context);
    }
  }

  void startListeningToOrientationStream({bool useSensor = false}) {
    streamSubscription = communicator
        .onOrientationChanged(useSensor: useSensor)
        .listen((orientation) {
      debugPrint('$orientation');
      invalidNotifier.value = orientation;

      if (mounted) {
        setState(() {
          invalidHolding =
              !(orientation == NativeDeviceOrientation.portraitUp ||
                  orientation == NativeDeviceOrientation.portraitDown);
        });
      }

      if (!_orientationStreamController.isClosed) {
        _orientationStreamController.add(orientation);
      }
    });
  }

  @override
  void dispose() {
    _listener.dispose();
    streamSubscription.cancel();
    _orientationStreamController.close();
    timer.cancel();
    faceDetectionViewController?.stopCamera();
    super.dispose();
  }

  void _onDetached() => debugPrint('detached');

  void _onResumed() {
    if (_shouldRequestPermission) {
      permissionWall();
      _shouldRequestPermission = false;
    }
  }

  void _onInactive() => debugPrint('inactive');

  void _onHidden() => debugPrint('hidden');

  void _onPaused() async {
    _shouldRequestPermission = true;
  }

  // Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
        break;
      case AppLifecycleState.resumed:
        _onResumed();
        break;
      case AppLifecycleState.inactive:
        _onInactive();
        break;
      case AppLifecycleState.hidden:
        _onHidden();
        break;
      case AppLifecycleState.paused:
        _onPaused();
        break;
    }
  }

  Future<void> initFaceDetect() async {
    int facePluginState = -1;

    try {
      if (Platform.isAndroid) {
        await _faceSdkPlugin
            .setActivation(AppStrings.androidFaceToken)
            .then((value) => facePluginState = value ?? -1);
      } else {
        await _faceSdkPlugin
            .setActivation(AppStrings.iOSFaceToken)
            .then((value) => facePluginState = value ?? -1);
      }

      if (facePluginState == 0) {
        await _faceSdkPlugin
            .init()
            .then((value) => facePluginState = value ?? -1);
      }
    } catch (e) {
      debugPrint('Error initializing face detection: $e');
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;
  }

  Future<void> loadSettings() async {
    if (widget.personList == null) {
      widget.personList = await loadAllPersons();
      initFaceDetect();
    }

    try {
      final storage = GetStorage();
      String? livenessThreshold = storage.read("liveness_threshold");
      String? identifyThreshold = storage.read("identify_threshold");

      if (mounted) {
        setState(() {
          _livenessThreshold = double.parse(livenessThreshold ?? MAX_LIVENESS);
          _identifyThreshold = double.parse(identifyThreshold ?? MAX_IDENTIFY);
        });
      }
    } catch (e) {
      debugPrint('Error loading settings: $e');
      // Use default values if parsing fails
      _livenessThreshold = double.parse(MAX_LIVENESS);
      _identifyThreshold = double.parse(MAX_IDENTIFY);
    }
  }

  Future<void> faceRecognitionStart() async {
    var cameraLens = storage.read("camera_lens") ?? 1;

    if (mounted) {
      setState(() {
        _faces = null;
        _recognized = false;
      });
    }

    await faceDetectionViewController?.startCamera(cameraLens);
  }

  Future<bool> onFaceDetected(faces) async {
    if (_recognized || !mounted) return false;

    setState(() {
      _faces = faces;
    });

    bool recognized = false;
    double maxSimilarity = -1;
    String maxSimilarityName = "";
    double maxLiveness = -1;
    double maxYaw = -1;
    double maxRoll = -1;
    double maxPitch = -1;
    // ignore: prefer_typing_uninitialized_variables
    var enrolledFace, identifedFace;

    if (faces.isNotEmpty) {
      var face = faces[0];
      for (var person in widget.personList ?? []) {
        double similarity = await _faceSdkPlugin.similarityCalculation(
                face['templates'], person.templates) ??
            -1;

        if (!mounted) return false;

        setState(() {
          ///For UI
          _similarity = similarity;
          _liveness = face['liveness'];
        });

        if (maxSimilarity < similarity) {
          maxSimilarity = similarity;
          maxSimilarityName = person.name;
          maxLiveness = face['liveness'];
          maxYaw = face['yaw'];
          maxRoll = face['roll'];
          maxPitch = face['pitch'];
          identifedFace = face['faceJpg'];
          enrolledFace = person.faceJpg;
        }
      }

      if (maxSimilarity > _identifyThreshold &&
          maxLiveness > _livenessThreshold) {
        recognized = true;
      }
    }

    await Future.delayed(const Duration(milliseconds: 100));

    if (!mounted) return false;

    setState(() {
      _recognized = recognized;
      _identifiedName = maxSimilarityName;
      _identifiedSimilarity = maxSimilarity.toString();
      _identifiedLiveness = maxLiveness.toString();
      _identifiedYaw = maxYaw.toString();
      _identifiedRoll = maxRoll.toString();
      _identifiedPitch = maxPitch.toString();
      _enrolledFace = enrolledFace;
      _identifiedFace = identifedFace;
    });

    if (recognized) {
      // Only vibrate once when recognized
      softVibrate();

      timer.start();

      if (widget.afterRecognized != null) {
        widget.afterRecognized!();
      }

      setState(() {
        _faces = null;
      });
    }

    return recognized;
  }

  // Consistent vibration function with null safety checks
  Future<void> softVibrate() async {
    if (!(await Vibration.hasVibrator())) return;

    if (await Vibration.hasCustomVibrationsSupport()) {
      Vibration.vibrate(
        pattern: [0, 15], // 0ms pause, 15ms vibration
        intensities: [
          0,
          50
        ], // 0 intensity during pause, soft (50) intensity during vibration
      );
    } else {
      // fallback if custom vibrations aren't supported
      Vibration.vibrate(duration: 20);
    }
  }

  Future<bool> sendApprovedFace(dynamic enrolledFace) async {
    if (enrolledFace == null) {
      CustomToast.showToast(LocaleKeys.face_control_error_uploading.tr());
      return false;
    }

    try {
      Dio dio = di.di();

      final token = storage.read(TOKEN);
      final isDemoMode = storage.read(is_demo) ?? false;

      String? userId;
      String? userRole;
      String apiEndpoint;

      if (token != null) {
        // Regular authenticated user
        userId = JwtDecoder.getUserId(token);
        userRole = JwtDecoder.getUserRole(token);
      } else if (isDemoMode) {
        // Demo mode - extract user ID from guest token
        userId = JwtDecoder.getUserId(GUEST_TOKEN);
        userRole = JwtDecoder.getUserRole(GUEST_TOKEN);
      }

      if (userId == null || userRole == null) {
        CustomToast.showToast('User information not found');
        return false;
      }

      // Determine the correct API endpoint based on user role
      if (userRole == 'supervisor') {
        apiEndpoint =
            '${ApiPath.baseUrl}${ApiPath.supervisorUpdatePath}/$userId';
      } else if (userRole == 'seller') {
        apiEndpoint = '${ApiPath.baseUrl}${ApiPath.sellerUpdatePath}/$userId';
      } else {
        CustomToast.showToast('Unknown user role: $userRole');
        return false;
      }

      MultipartFile file = await MultipartFile.fromBytes(
        enrolledFace,
        filename: '$_identifiedName.jpg',
        contentType: MediaType('image', 'jpeg'),
      );

      var data = {'file': file};
      FormData formData = FormData.fromMap(data);

      debugPrint("============ Form data: ${formData.files}");

      Options options = Options(
        receiveDataWhenStatusError: true,
        headers: {
          "Content-Type": "multipart/form-data",
          "Accept": "application/json",
        },
      );

      print('Uploading face image to: $apiEndpoint');

      final response = await dio.put(
        apiEndpoint,
        data: formData,
        options: options,
      );

      debugPrint("=== Upload response: $response");

      if (response.statusCode == 200) {
        debugPrint('Success: 200!');
        return true;
      } else {
        CustomToast.showToast(LocaleKeys.face_control_error_uploading.tr());
        return false;
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.badResponse) {
        if (e.response != null) {
          final data = e.response?.data;
          final hasMessage = data is Map && data.containsKey('message');

          CustomToast.showToast(
            hasMessage ? "${LocaleKeys.common_error.tr()}: ${data['message']}" : LocaleKeys.face_control_error_uploading.tr(),
          );
        }
      } else {
        CustomToast.showToast(LocaleKeys.face_control_error_uploading.tr());
      }
      debugPrint("=== Upload error: $e");
      return false;
    } catch (e) {
      CustomToast.showToast(LocaleKeys.face_control_error_uploading.tr());
      debugPrint("=== Upload error: $e");
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        _orientationStreamController.close();
        faceDetectionViewController?.stopCamera();
        CustomToast.showToast(LocaleKeys.face_control_leader_not_confirmed.tr());
        timer.cancel();
        return true;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: Theme.of(context).colorScheme.surface,
          surfaceTintColor: Colors.transparent,
          title: Text(
            LocaleKeys.face_control_face_control_title.tr(),
            style: TextStyle(
              fontSize: context.isTablet ? 16.sp : 20.sp,
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          toolbarHeight: 70.h,
          centerTitle: true,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.r),
              bottomRight: Radius.circular(20.r),
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        floatingActionButton: !_recognized
            ? Padding(
                padding: EdgeInsets.only(bottom: 100.h),
                child: SizedBox(
                  height: 55.h,
                  width: 55.h,
                  child: FloatingActionButton(
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    foregroundColor: Theme.of(context).colorScheme.onSurface,
                    shape: const CircleBorder(),
                    elevation: 4,
                    onPressed: () async {
                      _orientationStreamController.close();
                      faceDetectionViewController?.stopCamera();
                      timer.cancel();
                      final navigator =
                          NavigationService.navigatorKey.currentState;
                      if (navigator != null && navigator.canPop()) {
                        navigator.pop();
                      }
                      CustomToast.showToast(LocaleKeys.face_control_leader_not_confirmed.tr());
                    },
                    child: Icon(
                      Icons.arrow_back_ios_new,
                      size: 22.h,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              )
            : null,
        body: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                      visible: widget.debug == false && !(invalidHolding),
                      child: FaceDetectionView(faceRecognitionViewState: this));
                }),
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                    visible: (invalidHolding),
                    child: Center(
                      child: RotatedBox(
                              quarterTurns: invalidNotifier.value ==
                                      NativeDeviceOrientation.landscapeLeft
                                  ? 1
                                  : invalidNotifier.value ==
                                          NativeDeviceOrientation.landscapeRight
                                      ? -1
                                      : 2,
                              child: Lottie.asset(Assets.iconsRotate,
                                  repeat: true, width: 200.h, height: 200.h))
                          .animate()
                          .fadeIn(duration: 500.ms),
                    ),
                  );
                }),
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                    visible: !(invalidHolding),
                    child: SizedBox(
                      width: double.infinity,
                      height: double.infinity,
                      child: CustomPaint(
                        painter: FacePainter(
                            similarity: _similarity,
                            faces: _faces,
                            livenessThreshold: _livenessThreshold),
                      ),
                    ),
                  );
                }),
            ValueListenableBuilder<NativeDeviceOrientation>(
                valueListenable: invalidNotifier,
                builder: (context, value, _) {
                  return Visibility(
                      visible: !(invalidHolding),
                      child: cameraMask(_recognized));
                }),
            Align(
              alignment: Alignment.bottomCenter,
              child: RoundedBottomContainer(
                matchScore: _similarity,
                livenessScore: _liveness,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget faceRecognizedWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Column(children: [
        const Spacer(flex: 2),
        Expanded(
          flex: 3,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: <Widget>[
              _enrolledFace != null
                  ? Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: Image.memory(
                            _enrolledFace,
                            height: 160.h,
                            width: 160.h,
                            frameBuilder: ((context, child, frame,
                                wasSynchronouslyLoaded) {
                              if (wasSynchronouslyLoaded) return child;
                              return AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: frame != null
                                    ? child
                                    : Center(
                                        child: SizedBox(
                                          height: 160.h,
                                          width: 160.h,
                                          child: CupertinoActivityIndicator(
                                            radius: 20.r,
                                          ),
                                        ),
                                      ),
                              );
                            }),
                          ),
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        Text(
                          LocaleKeys.face_control_uploaded.tr(),
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      ],
                    )
                  : SizedBox(
                      height: 1.h,
                    ),
              _identifiedFace != null
                  ? Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(10.r),
                          child: Image.memory(
                            _identifiedFace,
                            height: 160.h,
                            width: 160.h,
                            frameBuilder: ((context, child, frame,
                                wasSynchronouslyLoaded) {
                              if (wasSynchronouslyLoaded) return child;
                              return AnimatedSwitcher(
                                duration: const Duration(milliseconds: 200),
                                child: frame != null
                                    ? child
                                    : Center(
                                        child: SizedBox(
                                          height: 160.h,
                                          width: 160.h,
                                          child: CupertinoActivityIndicator(
                                            radius: 20.r,
                                            color:
                                                Theme.of(context).primaryColor,
                                          ),
                                        ),
                                      ),
                              );
                            }),
                          ),
                        ),
                        SizedBox(
                          height: 5.h,
                        ),
                        Text(
                          LocaleKeys.face_control_recognised.tr(),
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      ],
                    )
                  : SizedBox(
                      height: 1.h,
                    )
            ],
          ),
        ),
        Expanded(flex: 4, child: Lottie.asset(Assets.iconsDone, repeat: false)),
        SizedBox(height: 50.h)
      ]),
    );
  }

  Widget cameraMask(bool isRecognized) {
    double rectWidth = 250.w;
    double rectHeight = 350.h;
    double customPadding = 100.h;

    return Stack(
      alignment: Alignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: customPadding),
          child: SvgPicture.asset(
            height: 250.h,
            width: 250.h,
            key: ValueKey<bool>(isRecognized),
            Assets.iconsRecognationBorder,
          ),
        ),
        AnimatedSwitcher(
            duration: const Duration(milliseconds: 500),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return FadeTransition(child: child, opacity: animation);
            },
            child: Visibility(
              visible: isRecognized,
              child: Padding(
                padding: EdgeInsets.only(bottom: customPadding),
                child: SvgPicture.asset(
                  height: 150.h,
                  width: 150.h,
                  key: ValueKey<bool>(isRecognized),
                  Assets.iconsScanningDone,
                ).animate().scale(curve: Curves.bounceInOut),
              ),
            )),
      ],
    );
  }

  void initializeTimer() {
    // Don't initialize if already initialized
    if (_timerInitialized) return;
    _timerInitialized = true;

    timer = PausableTimer(const Duration(seconds: 2), () async {
      // Check for vibration capabilities before vibrating
      if (await Vibration.hasVibrator()) {
        Vibration.vibrate(preset: VibrationPreset.singleShortBuzz);
      }

      if (widget.server_approved) {
        _handleSuccessfulRecognition();
      } else {
        _handleServerApprovalRequired();
      }
    });
  }

  void _handleSuccessfulRecognition() {
    final navigator = NavigationService.navigatorKey.currentState;
    if (navigator != null) {
      if (widget.child != null) {
        navigator.pushReplacement(
            MaterialPageRoute(builder: (context) => widget.child!));
      } else if (widget.getBackAfterRecognized == true) {
        if (navigator.canPop()) {
          navigator.pop();
        }
      } else {
        final storage = GetStorage();
        storage.write(local_approved, true);
        faceDetectionViewController?.stopCamera();
        navigator.pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => LockProvider()),
          (route) => false,
        );
      }
    }
  }

  Future<void> _handleServerApprovalRequired() async {
    final isApproved = await sendApprovedFace(_enrolledFace);
    final navigator = NavigationService.navigatorKey.currentState;
    final storage = GetStorage();

    if (isApproved) {
      debugPrint('==== Photo uploaded!');
      CustomToast.showToast(LocaleKeys.face_control_picture_uploaded.tr());
      faceDetectionViewController?.stopCamera();
      storage.write(server_approved, true);
      storage.write(local_approved, true);
      if (navigator != null) {
        navigator.pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => LockProvider()),
          (route) => false,
        );
      }
    } else {
      debugPrint('Server approval failed');
      faceDetectionViewController?.stopCamera();
      if (navigator != null && navigator.canPop()) {
        navigator.pop();
      }
    }
  }
}

// ignore: must_be_immutable
class FaceDetectionView extends StatefulWidget
    implements FaceDetectionInterface {
  final FaceRecognitionViewState faceRecognitionViewState;

  const FaceDetectionView({super.key, required this.faceRecognitionViewState});

  @override
  Future<void> onFaceDetected(faces) async {
    await faceRecognitionViewState.onFaceDetected(faces);
  }

  @override
  State<StatefulWidget> createState() => _FaceDetectionViewState();
}

class _FaceDetectionViewState extends State<FaceDetectionView> {
  @override
  Widget build(BuildContext context) {
    if (defaultTargetPlatform == TargetPlatform.android) {
      return AndroidView(
        viewType: 'facedetectionview',
        onPlatformViewCreated: _onPlatformViewCreated,
      );
    } else {
      return UiKitView(
        viewType: 'facedetectionview',
        onPlatformViewCreated: _onPlatformViewCreated,
      );
    }
  }

  void _onPlatformViewCreated(int id) async {
    final storage = GetStorage();
    var cameraLens = storage.read("camera_lens") ?? 1;

    widget.faceRecognitionViewState.faceDetectionViewController =
        FaceDetectionViewController(id, widget);

    await widget.faceRecognitionViewState.faceDetectionViewController
        ?.initHandler();

    int? livenessLevel = storage.read("liveness_level") ?? 0;
    await widget.faceRecognitionViewState._faceSdkPlugin
        .setParam({'check_liveness_level': livenessLevel});

    await widget.faceRecognitionViewState.faceDetectionViewController
        ?.startCamera(cameraLens);
  }
}

class FacePainter extends CustomPainter {
  dynamic faces;
  double livenessThreshold;
  double similarity;

  FacePainter(
      {required this.faces,
      required this.livenessThreshold,
      required this.similarity});

  @override
  void paint(Canvas canvas, Size size) async {
    if (faces != null) {
      var paint = Paint();
      paint.color = const Color.fromARGB(0xff, 0xff, 0, 0);
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = 3;

      for (var face in faces) {
        double xScale = face['frameWidth'] / size.width;
        double yScale = face['frameHeight'] / size.height;

        Color color = face['liveness'] < livenessThreshold
            ? const Color.fromARGB(0xff, 0xff, 0, 0) // Red
            : const Color.fromARGB(0xff, 0, 0xff, 0); // Green

        paint.color = color;
        canvas.drawOval(
          Rect.fromPoints(
            Offset(face['x1'] / xScale, face['y1'] / yScale),
            Offset(face['x2'] / xScale, face['y2'] / yScale),
          ),
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

// ignore: must_be_immutable
class RoundedBottomContainer extends StatelessWidget {
  double matchScore = 0.0;
  double livenessScore = 0.0;
  int matchInt = 0;
  int livenesInt = 0;

  RoundedBottomContainer(
      {required this.matchScore, required this.livenessScore});

  @override
  Widget build(BuildContext context) {
    matchInt = (matchScore * 100).round();
    livenesInt = (livenessScore * 100).round();

    return Container(
        width: double.infinity,
        constraints: BoxConstraints(
          minHeight: 100.h,
          maxHeight: 140.h, // Flexible height to prevent overflow
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          top: false,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Similarity indicator
                _buildMetricRow(
                  context,
                  icon: _getSimilarityIcon(matchInt),
                  iconColor: _getSimilarityColor(context, matchInt),
                  label: LocaleKeys.face_control_face_match.tr(),
                  value: "$matchInt%",
                  progress: matchInt / 100,
                  isGood: matchInt >= 70,
                ),

                SizedBox(height: 8.h),

                // Liveness indicator
                _buildMetricRow(
                  context,
                  icon: _getLivenessIcon(livenesInt),
                  iconColor: _getLivenessColor(context, livenesInt),
                  label: LocaleKeys.face_control_liveness.tr(),
                  value: livenesInt == 0 ? LocaleKeys.face_control_analyzing.tr() : "$livenesInt%",
                  progress: livenesInt == 0 ? null : livenesInt / 100,
                  isGood: livenesInt >= 50,
                  isAnalyzing: livenesInt == 0,
                ),
              ],
            ),
          ),
        ));
  }

  Widget _buildMetricRow(
    BuildContext context, {
    required IconData icon,
    required Color iconColor,
    required String label,
    required String value,
    required bool isGood,
    double? progress,
    bool isAnalyzing = false,
  }) {
    return Row(
      children: [
        // Icon with background
        Container(
          width: 32.w,
          height: 32.w,
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.r),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 16.w,
          ),
        ),

        SizedBox(width: 10.w),

        // Label and progress
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Row(
                    children: [
                      if (isAnalyzing)
                        SizedBox(
                          width: 12.w,
                          height: 12.w,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      SizedBox(width: 6.w),
                      Text(
                        value,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (progress != null) ...[
                SizedBox(height: 2.h),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor:
                      Theme.of(context).colorScheme.outline.withOpacity(0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(iconColor),
                  minHeight: 2.h,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  IconData _getSimilarityIcon(int similarity) {
    if (similarity >= 80) return Icons.verified_user;
    if (similarity >= 60) return Icons.face;
    if (similarity >= 40) return Icons.face_retouching_natural;
    return Icons.warning;
  }

  Color _getSimilarityColor(BuildContext context, int similarity) {
    if (similarity >= 80) return Colors.green;
    if (similarity >= 60) return Colors.orange;
    if (similarity >= 40) return Colors.amber;
    return Colors.red;
  }

  IconData _getLivenessIcon(int liveness) {
    if (liveness == 0) return Icons.psychology;
    if (liveness >= 70) return Icons.visibility;
    if (liveness >= 50) return Icons.remove_red_eye;
    return Icons.visibility_off;
  }

  Color _getLivenessColor(BuildContext context, int liveness) {
    if (liveness == 0) return Theme.of(context).colorScheme.primary;
    if (liveness >= 70) return Colors.green;
    if (liveness >= 50) return Colors.orange;
    return Colors.red;
  }

  Widget _buildFaceCard(
    BuildContext context, {
    required dynamic imageData,
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return Container(
      width: 140.w,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with icon and title
          Row(
            children: [
              Icon(
                icon,
                color: Theme.of(context).colorScheme.primary,
                size: 16.w,
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: 8.h),

          // Face image
          ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Image.memory(
              imageData,
              height: 100.h,
              width: 100.w,
              fit: BoxFit.cover,
              frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
                if (wasSynchronouslyLoaded) return child;
                return AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: frame != null
                      ? child
                      : Container(
                          height: 100.h,
                          width: 100.w,
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .surfaceContainerHigh,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ),
                        ),
                );
              },
            ),
          ),

          SizedBox(height: 8.h),

          // Subtitle
          Text(
            subtitle,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: 10.sp,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
