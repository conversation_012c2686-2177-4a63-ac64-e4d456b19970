import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:get_storage/get_storage.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';
import '../../../../../core/utils/app_constants.dart';
import '../../../../../core/utils/jwt_decoder.dart';
import '../../../../../core/widgets/universal_loading.dart';
import '../../../../../di/dependency_injection.dart' as di;
import '../../../../auth/models/user_profile_model.dart';
import '../bloc/naz_profile_bloc.dart';
import '../bloc/naz_profile_event.dart';
import '../bloc/naz_profile_state.dart';
import '../../../../../translations/locale_keys.g.dart';

class NazPersonalInfoPage extends StatefulWidget {
  final UserProfile? userProfile;

  const NazPersonalInfoPage({
    super.key,
    this.userProfile,
  });

  @override
  State<NazPersonalInfoPage> createState() => _NazPersonalInfoPageState();
}

class _NazPersonalInfoPageState extends State<NazPersonalInfoPage> {
  late final NazProfileBloc _nazProfileBloc;
  late final GetStorage _storage;
  UserProfile? _currentProfile;

  @override
  void initState() {
    super.initState();
    _nazProfileBloc = di.di<NazProfileBloc>();
    _storage = di.di<GetStorage>();
    _currentProfile = widget.userProfile;

    // Load fresh data if no profile provided
    if (_currentProfile == null) {
      _loadUserProfile();
    }
  }

  @override
  void dispose() {
    _nazProfileBloc.close();
    super.dispose();
  }

  /// Load user profile from API
  void _loadUserProfile({bool forceRefresh = false}) {
    final token = _storage.read(TOKEN);
    final isDemoMode = _storage.read(is_demo) ?? false;

    String? userId;

    if (token != null) {
      // Regular authenticated user
      userId = JwtDecoder.getUserId(token);
    } else if (isDemoMode) {
      // Demo mode - extract user ID from guest token
      userId = JwtDecoder.getUserId(GUEST_TOKEN);
    }

    if (userId != null) {
      _nazProfileBloc.add(LoadNazProfileEvent(
        userId: userId,
        forceRefresh: forceRefresh,
      ));
    }
  }

  /// Handle refresh action
  Future<void> _onRefresh() async {
    _loadUserProfile(forceRefresh: true);

    // Wait for the refresh to complete
    await _nazProfileBloc.stream
        .firstWhere((state) => state is! NazProfileRefreshing);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _nazProfileBloc,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          elevation: 0,
          centerTitle: false,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            LocaleKeys.profile_personal_info.tr(),
            style: AppTextStyles.titleMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: BlocListener<NazProfileBloc, NazProfileState>(
          listener: (context, state) {
            if (state is NazProfileLoaded) {
              if (mounted) {
                setState(() {
                  _currentProfile = state.userProfile;
                });
              }
            }
            // Note: Error handling is now done in BlocBuilder to show error widget in center
          },
          child: RefreshIndicator(
            onRefresh: _onRefresh,
            color: AppColors.cFirstColor,
            backgroundColor: AppColors.cCardsColor,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  Gap(24.h),
                  _buildPersonalInfoCard(),
                  Gap(24.h),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPersonalInfoCard() {
    return BlocBuilder<NazProfileBloc, NazProfileState>(
      builder: (context, state) {
        if (state is NazProfileLoading) {
          return UniversalLoading.shimmer(
            child: Column(
              children: [
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(isStyled: true),
                _buildDivider(),
                UniversalLoading.personalInfoRow(isStyled: true),
              ],
            ),
          );
        }

        // Handle error state - show error widget in center
        if (state is NazProfileError) {
          return SizedBox(
            height: MediaQuery.of(context).size.height * 0.6,
            child: Center(
              child: UniversalLoading.error(
                message: state.message,
                onRetry: () => _loadUserProfile(forceRefresh: true),
                retryButtonText: LocaleKeys.common_retry.tr(),
              ),
            ),
          );
        }

        final profile =
            state is NazProfileLoaded ? state.userProfile : _currentProfile;

        if (profile == null) {
          return UniversalLoading.shimmer(
            child: Column(
              children: [
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(),
                _buildDivider(),
                UniversalLoading.personalInfoRow(isStyled: true),
                _buildDivider(),
                UniversalLoading.personalInfoRow(isStyled: true),
              ],
            ),
          );
        }

        return Column(
          children: [
            _buildInfoRow(label: LocaleKeys.profile_first_name.tr(), value: profile.firstName??'-'),
            _buildDivider(),
            _buildInfoRow(label: LocaleKeys.profile_last_name.tr(), value: profile.lastName??'-'),
            _buildDivider(),
            _buildInfoRow(label: LocaleKeys.profile_middle_name.tr(), value: profile.middleName??'-'),
            _buildDivider(),
            _buildInfoRow(
                label: LocaleKeys.profile_phone_number.tr(), value: profile.formattedPhone),
            _buildDivider(),
            _buildInfoRow(label: LocaleKeys.profile_gender.tr(), value: profile.genderText),
            _buildDivider(),
            _buildInfoRow(label: LocaleKeys.profile_address.tr(), value: profile.address??'-'),
            _buildDivider(),
            _buildInfoRow(
                label: LocaleKeys.profile_blocks.tr(), value: profile.blocksText, styled: true),
            _buildDivider(),
            _buildInfoRow(
                label: LocaleKeys.profile_places.tr(), value: profile.placesText, styled: true),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow(
      {required String label, required String value, bool styled = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120.w,
            child: Text(
              label,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.cTextGrayColor,
                fontSize: 14.sp,
              ),
            ),
          ),
          styled == true
              ? Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: AppColors.cGreenishColor.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(cRadius16.r),
                  ),
                  child: Text(
                    value,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.secondary,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              : Expanded(
                  child: Text(
                    value,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
        ],
      ),
    );
  }


  Widget _buildDivider() {
    return Container(
      height: 1,
      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
    );
  }
}
