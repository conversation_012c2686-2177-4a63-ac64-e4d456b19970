import 'package:equatable/equatable.dart';
import '../../../../auth/models/user_profile_model.dart';

/// Base class for naz profile states
abstract class NazProfileState extends Equatable {
  const NazProfileState();

  @override
  List<Object?> get props => [];
}

/// Initial state
class NazProfileInitial extends NazProfileState {
  const NazProfileInitial();
}

/// Loading state
class NazProfileLoading extends NazProfileState {
  const NazProfileLoading();
}

/// Success state with user profile data
class NazProfileLoaded extends NazProfileState {
  final UserProfile userProfile;
  final bool isFromCache;
  final String? message;

  const NazProfileLoaded({
    required this.userProfile,
    this.isFromCache = false,
    this.message,
  });

  @override
  List<Object?> get props => [userProfile, isFromCache, message];
}

/// Error state
class NazProfileError extends NazProfileState {
  final String message;
  final UserProfile? cachedProfile;

  const NazProfileError({
    required this.message,
    this.cachedProfile,
  });

  @override
  List<Object?> get props => [message, cachedProfile];
}

/// Refreshing state (when profile is already loaded but refreshing)
class NazProfileRefreshing extends NazProfileState {
  final UserProfile currentProfile;

  const NazProfileRefreshing({required this.currentProfile});

  @override
  List<Object?> get props => [currentProfile];
}

/// Updating profile image state
class NazProfileImageUpdating extends NazProfileState {
  final UserProfile currentProfile;

  const NazProfileImageUpdating({required this.currentProfile});

  @override
  List<Object?> get props => [currentProfile];
}

/// Profile image updated successfully state
class NazProfileImageUpdated extends NazProfileState {
  final UserProfile updatedProfile;
  final String message;

  const NazProfileImageUpdated({
    required this.updatedProfile,
    required this.message,
  });

  @override
  List<Object?> get props => [updatedProfile, message];
}
