import 'package:equatable/equatable.dart';

/// Base class for naz profile events
abstract class NazProfileEvent extends Equatable {
  const NazProfileEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load user profile
class LoadNazProfileEvent extends NazProfileEvent {
  final String userId;
  final bool forceRefresh;

  const LoadNazProfileEvent({
    required this.userId,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [userId, forceRefresh];
}

/// Event to refresh user profile
class RefreshNazProfileEvent extends NazProfileEvent {
  final String userId;

  const RefreshNazProfileEvent({required this.userId});

  @override
  List<Object?> get props => [userId];
}

/// Event to clear user profile cache
class ClearNazProfileEvent extends NazProfileEvent {
  const ClearNazProfileEvent();
}

/// Event to load cached user profile
class LoadCachedNazProfileEvent extends NazProfileEvent {
  const LoadCachedNazProfileEvent();
}

/// Event to update user profile image
class UpdateNazProfileImageEvent extends NazProfileEvent {
  final String userId;
  final String imagePath;

  const UpdateNazProfileImageEvent({
    required this.userId,
    required this.imagePath,
  });

  @override
  List<Object?> get props => [userId, imagePath];
}
