import 'package:click_bazaar/core/function/functions.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/utils/api_path.dart';
import '../../../../core/network/network_info.dart';
import '../../../../translations/locale_keys.g.dart';
import '../../../auth/models/user_profile_model.dart';

/// Abstract class for naz profile remote data source
abstract class NazProfileRemoteDatasource {
  Future<UserProfileResponse> getUserProfile(String userId);

  Future<UserProfileResponse> updateProfileImage(
      String userId, String imagePath);
}

/// Implementation of naz profile remote data source
class NazProfileRemoteDatasourceImpl implements NazProfileRemoteDatasource {
  final Dio _dio;
  final NetworkInfo _networkInfo;

  NazProfileRemoteDatasourceImpl({
    required Dio dio,
    required NetworkInfo networkInfo,
  })  : _dio = dio,
        _networkInfo = networkInfo;

  @override
  Future<UserProfileResponse> getUserProfile(String userId) async {
    if (!await _networkInfo.isConnected) {
      return UserProfileResponse.error(
        LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      final response = await _dio.get(
        '${ApiPath.supervisorProfilePath}/$userId',
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        return UserProfileResponse.fromJson(response.data);
      } else {
        return UserProfileResponse.error(
          LocaleKeys.profile_failed_to_load_profile.tr(),
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.supervisorProfilePath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  @override
  Future<UserProfileResponse> updateProfileImage(
      String userId, String imagePath) async {
    if (!await _networkInfo.isConnected) {
      return UserProfileResponse.error(
        LocaleKeys.common_no_internet_connection.tr(),
      );
    }

    try {
      // Create FormData for file upload
      final formData = FormData.fromMap({
        'file': [
          await MultipartFile.fromFile(imagePath,
              filename: imagePath.split('/').last,
              contentType: getMediaType(imagePath)),
        ],
      });
      print(imagePath);

      final response = await _dio.put(
        '${ApiPath.supervisorUpdatePath}/$userId',
        data: formData,
        options: Options(
          headers: {
            "Content-Type": "multipart/form-data",
            'Accept': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200 && response.data != null) {
        return UserProfileResponse.fromJson(response.data);
      } else {
        return UserProfileResponse.error(
          LocaleKeys.profile_profile_image_update_error.tr(),
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.supervisorUpdatePath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }
}
