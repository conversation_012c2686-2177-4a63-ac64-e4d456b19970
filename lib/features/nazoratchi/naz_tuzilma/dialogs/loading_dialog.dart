import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../translations/locale_keys.g.dart';

/// Reusable loading dialog with cancel functionality
class LoadingDialog extends StatelessWidget {
  final String message;
  final VoidCallback? onCancel;
  final bool cancellable;

  const LoadingDialog({
    super.key,
    this.message = '',
    this.onCancel,
    this.cancellable = true,
  });

  /// Show loading dialog
  static Future<T?> show<T>({
    required BuildContext context,
    String message = '',
    VoidCallback? onCancel,
    bool cancellable = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: cancellable,
      builder: (context) => LoadingDialog(
        message: message,
        onCancel: onCancel,
        cancellable: cancellable,
      ),
    );
  }

  /// Hide loading dialog
  static void hide(BuildContext context) {
    if (Navigator.canPop(context)) {
      debugPrint(LocaleKeys.dialogs_loading_hiding_dialog.tr());
      Navigator.of(context).pop();
    } else {
      debugPrint(LocaleKeys.dialogs_loading_cannot_pop.tr());
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: cancellable,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop && onCancel != null) {
          onCancel!();
        }
      },
      child: Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Loading indicator
              const SizedBox(
                width: 60,
                height: 60,
                child: CircularProgressIndicator(
                  color: AppColors.cFirstColor,
                  strokeWidth: 4,
                ),
              ),

              const SizedBox(height: 20),

              // Loading message
              Text(
                message.isEmpty ? LocaleKeys.dialogs_loading_default_message.tr() : message,
                textAlign: TextAlign.center,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),

              if (cancellable) ...[
                const SizedBox(height: 20),

                // Cancel button
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    onCancel?.call();
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.cTextGrayColor,
                  ),
                  child: Text(
                    LocaleKeys.dialogs_loading_cancel.tr(),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.cTextGrayColor,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
