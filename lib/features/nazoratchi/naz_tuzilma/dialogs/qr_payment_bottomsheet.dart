import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:screen_brightness/screen_brightness.dart';
import 'package:uzpay/uzpay.dart';
import 'package:uzpay/objects.dart';
import 'package:uzpay/enums.dart';
import 'package:easy_localization/easy_localization.dart';
import 'loading_dialog.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../di/dependency_injection.dart';
import '../../../../translations/locale_keys.g.dart';
import '../services/payment_service.dart';
import '../models/payment_models.dart';
import 'payment_result_dialog.dart';

/// QR Code payment bottomsheet widget
class QRPaymentBottomSheet extends StatefulWidget {
  final String amount;
  final int squareNumber;
  final String sellerId;
  final String day;
  final int price;
  final int quantity;
  final List<String> placeIds;
  final VoidCallback? onPaymentSuccess;
  final VoidCallback? onPaymentError;
  final VoidCallback? onClose;
  final String? placeNumbers; // Add place numbers string for display

  const QRPaymentBottomSheet({
    super.key,
    required this.amount,
    required this.squareNumber,
    required this.sellerId,
    required this.day,
    required this.price,
    required this.quantity,
    required this.placeIds,
    this.onPaymentSuccess,
    this.onPaymentError,
    this.onClose,
    this.placeNumbers,
  });

  @override
  State<QRPaymentBottomSheet> createState() => _QRPaymentBottomSheetState();
}

class _QRPaymentBottomSheetState extends State<QRPaymentBottomSheet> {
  final PaymentService _paymentService = di<PaymentService>();

  bool _showChequeDetails = true; // Start with cheque details
  bool _isLoading = false;
  bool _isCheckingPayment = false;
  bool _hasError = false;
  String _errorMessage = LocaleKeys.dialogs_qr_payment_error_message_empty.tr();
  String? _paymentId;
  Uint8List? _qrImageBytes;

  @override
  void initState() {
    super.initState();
    _setMaxBrightness();
    // Don't create payment immediately - wait for user confirmation
  }

  @override
  void dispose() {
    _restoreOriginalBrightness();
    super.dispose();
  }

  /// Set screen brightness to maximum when QR bottomsheet opens
  Future<void> _setMaxBrightness() async {
    try {
      // Set brightness to maximum (1.0)
      await ScreenBrightness().setApplicationScreenBrightness(1.0);
    } catch (e) {
      // Handle any errors silently
      debugPrint(LocaleKeys.dialogs_qr_payment_brightness_error.tr(namedArgs: {'error': e.toString()}));
    }
  }

  /// Restore original brightness when bottomsheet closes
  Future<void> _restoreOriginalBrightness() async {
    try {
      // Reset application brightness to follow system brightness
      await ScreenBrightness().resetApplicationScreenBrightness();
    } catch (e) {
      // Handle any errors silently
      debugPrint(LocaleKeys.dialogs_qr_payment_brightness_restore_error.tr(namedArgs: {'error': e.toString()}));
    }
  }

  /// Proceed to QR generation after user confirms payment details
  void _proceedToQRGeneration() {
    setState(() {
      _showChequeDetails = false;
    });
    _createPayment();
  }

  /// Create payment and generate QR code
  Future<void> _createPayment() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = LocaleKeys.dialogs_qr_payment_error_message_empty.tr();
    });

    try {
      // Create payment with paymentType: 3 (QR)
      final result = await _paymentService.createPayment(
        seller: widget.sellerId,
        days: widget.quantity,
        price: widget.price,
        paymentType: PaymentType.qr,
        places: widget.placeIds,
      );

      if (result.isSuccess && result.data != null) {
        _paymentId = result.data!.id;
        await _generateQRCode(_paymentId!);
      } else {
        setState(() {
          _hasError = true;
          _errorMessage = result.errorMessage ?? LocaleKeys.dialogs_qr_payment_creation_error.tr();
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = LocaleKeys.dialogs_qr_payment_creation_error_with_message.tr(namedArgs: {'error': e.toString()});
        _isLoading = false;
      });
    }
  }

  /// Generate QR code using UzPay package
  Future<void> _generateQRCode(String paymentId) async {
    try {
      // Load Click logo from assets
      final ByteData logoData = await rootBundle.load(Assets.iconsClick);
      final Uint8List logoBytes = logoData.buffer.asUint8List();

      // Configure Click parameters for QR generation
      final clickParams = ClickParams(
        serviceId: '31654', // Replace with your actual service ID
        merchantId: '16827', // Replace with your actual merchant ID
        merchantUserId: '31654', // Replace with your actual merchant user ID
        transactionParam: paymentId,
      );

      final params = Params(clickParams: clickParams);

      // Generate QR code with Click logo using UzPay
      final Uint8List qrBytes = await UzPay.generatePaymentQR(
        amount: widget.price.toDouble(),
        paymentSystem: PaymentSystem.Click,
        paymentParams: params,
        logoImage: logoBytes,
        logoSize: 80.0,
        logoBackgroundColor: Colors.white,
        logoPadding: 20.0,
        logoBackgroundRadius: 20.0, // 20px corner radius as specified
      );

      setState(() {
        _qrImageBytes = qrBytes;
        _isLoading = false;
        _hasError = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = LocaleKeys.dialogs_qr_payment_qr_generation_error.tr(namedArgs: {'error': e.toString()});
        _isLoading = false;
      });
    }
  }

  /// Check payment status
  Future<void> _checkPaymentStatus() async {
    if (_paymentId == null) return;

    setState(() {
      _isCheckingPayment = true;
    });

    try {
      final result = await _paymentService.checkPaymentStatus(_paymentId!);

      if (result.isSuccess && result.data != null) {
        // Payment found and successful
        await _restoreOriginalBrightness();
        if (mounted) {
          Navigator.of(context).pop();
          widget.onPaymentSuccess?.call();
        }
      } else {
        // Handle 404 or other errors - show error overlay but keep bottom sheet open
        if (mounted) {
          _showErrorOverlay(result.errorMessage ?? LocaleKeys.dialogs_qr_payment_payment_not_found.tr());
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorOverlay(LocaleKeys.dialogs_qr_payment_check_error.tr(namedArgs: {'error': e.toString()}));
      }
    } finally {
      setState(() {
        _isCheckingPayment = false;
      });
    }
  }

  /// Show error overlay dialog while keeping bottom sheet open
  void _showErrorOverlay(String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => PaymentResultDialog.error(
        message: message,
        onClose: () {
          Navigator.of(context).pop(); // Close only the error dialog
        },
      ),
    );
  }

  /// Retry payment creation
  void _retryPayment() {
    _createPayment();
  }

  /// Build cheque details widget
  Widget _buildChequeDetails() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      margin: EdgeInsets.symmetric(horizontal: 24.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: Theme.of(context).colorScheme.primary,
                size: 24.w,
              ),
              SizedBox(width: 8.w),
              Text(
                LocaleKeys.dialogs_qr_payment_payment_details.tr(),
                style: AppTextStyles.titleMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // Payment details
          _buildDetailRow(LocaleKeys.dialogs_qr_payment_square_number.tr(), _getPlaceNumbers()),
          SizedBox(height: 8.h),
          _buildDetailRow(LocaleKeys.dialogs_qr_payment_days_count.tr(), '${widget.quantity} kun'),
          SizedBox(height: 8.h),
          _buildDetailRow(LocaleKeys.dialogs_qr_payment_daily_price.tr(), '${widget.price ~/ widget.quantity} UZS'),
          SizedBox(height: 12.h),

          // Divider
          Container(
            height: 1,
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),

          SizedBox(height: 12.h),

          // Total
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                LocaleKeys.dialogs_qr_payment_total_payment.tr(),
                style: AppTextStyles.titleMedium.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                '${widget.price} UZS',
                style: AppTextStyles.titleLarge.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Get place display text from payment data or fallback to square number
  String _getPlaceDisplayText() {
    if (widget.placeNumbers != null && widget.placeNumbers!.isNotEmpty) {
      return LocaleKeys.dialogs_qr_payment_place_number.tr(namedArgs: {'number': widget.placeNumbers!});
    }
    return LocaleKeys.dialogs_qr_payment_place_number.tr(namedArgs: {'number': widget.squareNumber.toString()});
  }

  /// Get place numbers for detail row
  String _getPlaceNumbers() {
    if (widget.placeNumbers != null && widget.placeNumbers!.isNotEmpty) {
      return widget.placeNumbers!.split(', ').map((num) => '#$num').join(', ');
    }
    return '#${widget.squareNumber}';
  }

  /// Build detail row widget
  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(20),
        Flexible(
          child: Text(
            value,
            textAlign: TextAlign.end,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) async {
        if (didPop) {
          await _restoreOriginalBrightness();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.only(bottom: 32),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.only(top: 12),
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.cTextGrayColor.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 24),

              // Title
              Text(
                LocaleKeys.dialogs_qr_payment_title.tr(),
                style: AppTextStyles.titleLarge.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),

              const SizedBox(height: 8),

              // Square info
              Text(
                _getPlaceDisplayText(),
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.cTextGrayColor,
                ),
              ),

              const SizedBox(height: 32),

              // Content based on state
              if (_showChequeDetails) ...[
                // Show cheque details first
                Icon(
                  Icons.qr_code,
                  size: 60,
                  color: Theme.of(context).colorScheme.primary,
                ),

                const SizedBox(height: 24),

                // Cheque details
                _buildChequeDetails(),

                const SizedBox(height: 32),

                // Proceed to QR button
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _proceedToQRGeneration,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      icon: Icon(
                        Icons.qr_code,
                        size: 20,
                      ),
                      label: Text(
                        LocaleKeys.dialogs_qr_payment_show_qr_code.tr(),
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Cancel button
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () {
                        widget.onClose?.call();
                      },
                      style: TextButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        LocaleKeys.dialogs_qr_payment_cancel.tr(),
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ] else if (_isLoading) ...[
                // Loading state
                Container(
                  width: double.infinity,
                  height: 250.h,
                  alignment: Alignment.center,
                  child: const CircularProgressIndicator(
                    color: AppColors.cFirstColor,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  LocaleKeys.dialogs_qr_payment_generating_qr.tr(),
                  textAlign: TextAlign.center,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ] else if (_hasError) ...[
                // Error state
                const SizedBox(
                  width: 200,
                  height: 200,
                  child: Center(
                    child: Icon(
                      Icons.error_outline,
                      size: 80,
                      color: AppColors.cReddishColor,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    _errorMessage,
                    textAlign: TextAlign.center,
                    style: AppTextStyles.titleLarge.copyWith(
                      color: AppColors.cReddishColor,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                // Retry button
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _retryPayment,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.cFirstColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        LocaleKeys.dialogs_qr_payment_retry.tr(),
                        style: AppTextStyles.bodyLarge.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
              ] else ...[
                // Success state - show QR code
                Container(
                  width: 200,
                  height: 200,
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white, // QR code background should always be white for scanning
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 1,
                    ),
                  ),
                  child: _qrImageBytes != null
                      ? Image.memory(_qrImageBytes!)
                      : Image.asset(
                          Assets.imagesQrExample),
                ),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Text(
                    LocaleKeys.dialogs_qr_payment_scan_instruction.tr(),
                    textAlign: TextAlign.center,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                // Payment check button
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed:
                          _isCheckingPayment ? null : _checkPaymentStatus,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.cFirstColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isCheckingPayment
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: AppColors.white,
                                strokeWidth: 2,
                              ),
                            )
                          : Text(
                              LocaleKeys.dialogs_qr_payment_check_payment.tr(),
                              style: AppTextStyles.bodyLarge.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}
