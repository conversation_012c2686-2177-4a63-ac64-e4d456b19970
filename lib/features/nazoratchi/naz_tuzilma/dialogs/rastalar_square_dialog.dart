import 'dart:async';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../../../../core/widgets/universal_loading.dart';
import '../../../../core/widgets/custom_toast.dart';
import '../../../../di/dependency_injection.dart';
import '../../../../translations/locale_keys.g.dart';
import '../models/debt_model.dart';
import '../models/rastalar_model.dart';
import '../models/payment_models.dart';
import '../bloc/rastalar_api/rastalar_api_bloc.dart';
import '../services/debt_service.dart';
import 'payment_status_bottomsheet.dart';
import 'place_check_dialog.dart';
import '../page/empty_square_report_page.dart';

class RastalarSquareDialog extends StatefulWidget {
  final RastalarSquare square;
  final GroupedSquares? groupedSquares;
  final String? pavilionId;
  final String? blockId;
  final RastalarApiBloc rastalarBloc;

  const RastalarSquareDialog({
    super.key,
    required this.square,
    this.groupedSquares,
    this.pavilionId,
    this.blockId,
    required this.rastalarBloc,
  });

  /// Constructor for API-based grouped squares
  const RastalarSquareDialog.fromGroupedSquares({
    super.key,
    required GroupedSquares groupedSquares,
    this.pavilionId,
    this.blockId,
    required this.rastalarBloc,
  })  : square = const RastalarSquare(
            number: 0, status: SquareStatus.available, seller: null),
        groupedSquares = groupedSquares;

  @override
  State<RastalarSquareDialog> createState() => _RastalarSquareDialogState();
}

class _RastalarSquareDialogState extends State<RastalarSquareDialog> {
  int _quantity = 1;
  bool _showPaymentButtons = false; // Track which UI state to show
  late final RastalarApiBloc _rastalarBloc; // Store bloc reference for refresh

  // Debt loading state
  bool _isLoadingDebt = false;
  DebtResponse? _debtData;
  String? _debtError;
  DateFormat dateFormat = DateFormat('dd.MM.yyyy');

  @override
  void initState() {
    super.initState();
    // Use the passed bloc (now required)
    _rastalarBloc = widget.rastalarBloc;

    // Load debt data if seller has debt
    if (_hasDebt && _hasValidSeller) {
      _loadDebtData();
    }
  }

  /// Check if we're using API-based data
  bool get _isApiMode => widget.groupedSquares != null;

  /// Load debt data from API
  Future<void> _loadDebtData() async {
    if (!mounted) return;

    setState(() {
      _isLoadingDebt = true;
      _debtError = null;
    });

    try {
      final debtService = di<DebtService>();
      final sellerId = _getSellerId();

      final result = await debtService.getDebt(sellerId);

      if (!mounted) return;

      if (result.isSuccess) {
        setState(() {
          _debtData = result.data;
          _isLoadingDebt = false;
        });
      } else {
        final errorMessage = result.errorMessage ??
            LocaleKeys.nazoratchi_tuzilma_debt_loading_error.tr();
        setState(() {
          _debtError = errorMessage;
          _isLoadingDebt = false;
        });
        // Show error toast above dialog
        CustomToast.showErrorToast(errorMessage);
      }
    } catch (e) {
      if (!mounted) return;
      final errorMessage =
          LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr();
      setState(() {
        _debtError = errorMessage;
        _isLoadingDebt = false;
      });
      // Show error toast above dialog
      CustomToast.showErrorToast(errorMessage);
    }
  }

  /// Get the appropriate title
  String get _displayTitle {
    if (_isApiMode) {
      return widget.groupedSquares!.displayTitle;
    }
    return LocaleKeys.nazoratchi_tuzilma_dialog_place_number
        .tr(namedArgs: {'number': widget.square.number.toString()});
  }

  /// Get the appropriate tariff title
  String get _tariffTitle {
    if (_isApiMode) {
      return widget.groupedSquares!.tariffTitle;
    }
    return LocaleKeys.nazoratchi_tuzilma_default_category
        .tr(); // Default for legacy
  }

  /// Get the total price
  int get _totalPrice {
    if (_isApiMode) {
      return widget.groupedSquares!.totalPrice;
    }
    return 780000; // Default for legacy
  }

  /// Check if has debt (based on API status codes)
  /// Only for unpaid and unbinded squares, NOT for empty squares
  bool get _hasDebt {
    if (_isApiMode) {
      // Status 3 = qarzdor (debt), Status 1 = belgilanmagan (unbinded) - both show debt
      // Status 2 = bo'sh (empty) - should NOT show debt
      return widget.groupedSquares!.squares
          .any((square) => square.status == 3 || square.status == 1);
    }
    return widget.square.status == SquareStatus.unpaid ||
        widget.square.status == SquareStatus.unbinded;
  }

  /// Check if status is paid (based on API status codes)
  bool get _isPaid {
    if (_isApiMode) {
      // Payment: true = to'langan (paid)
      return widget.groupedSquares!.squares.every((square) => square.payment);
    }
    return widget.square.status == SquareStatus.paid;
  }

  /// Check if status is empty/available (based on API status codes)
  bool get _isEmpty {
    if (_isApiMode) {
      // Status 2 = bo'sh (empty)
      return widget.groupedSquares!.squares
          .every((square) => square.status == 2);
    }
    return widget.square.status == SquareStatus.available;
  }

  /// Get total price (combined price of all grouped places)
  int get _totalDailyPrice {
    if (_isApiMode) {
      // Sum all prices from grouped squares
      return widget.groupedSquares!.squares
          .fold(0, (sum, square) => sum + square.price);
    }
    return 12000; // Default for legacy
  }

  /// Get total square meters (for API mode)
  int _getTotalSquareMeters() {
    if (_isApiMode) {
      return widget.groupedSquares!.squares
          .fold(0, (sum, square) => sum + square.squareMeter);
    }
    return 3; // Default for legacy
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final maxDialogHeight = screenHeight * 0.85; // Use 85% of screen height
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width * 0.9,
            constraints: BoxConstraints(
              maxHeight: maxDialogHeight,
            ),
            margin: const EdgeInsets.symmetric(vertical: 60),
            // Add margin for close button space
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: _getStatusColor(),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            _getStatusText(),
                            style: AppTextStyles.bodySmall.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _displayTitle,
                          style: AppTextStyles.titleLarge.copyWith(
                            color: _getTitleColor(),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Content
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Owner name (seller)
                        Text(
                          _isApiMode
                              ? widget.groupedSquares!.sellerName
                              : LocaleKeys
                                  .nazoratchi_tuzilma_dialog_seller_placeholder
                                  .tr(),
                          style: AppTextStyles.titleMedium.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Tariff Info Widget
                        _buildTariffInfoWidget(),

                        const SizedBox(height: 16),

                        // Actual rasta details
                        _buildDetailRow(
                            LocaleKeys.nazoratchi_tuzilma_dialog_total_amount
                                .tr(),
                            '$_totalDailyPrice ${LocaleKeys.nazoratchi_tuzilma_dialog_currency_uzs.tr()}'),
                        const SizedBox(height: 8),
                        if (_isApiMode &&
                            widget.groupedSquares!.isMultiOccupant) ...[
                          // _buildDetailRow(
                          //     LocaleKeys.nazoratchi_tuzilma_dialog_total_square
                          //         .tr(),
                          //     '${_getTotalSquareMeters()} ${LocaleKeys.nazoratchi_tuzilma_dialog_square_meter.tr()}'),
                          // const SizedBox(height: 8),
                        ],

                        // Debt indicator - only show for unpaid and unbinded squares (not paid, not empty)
                        if (!_isPaid &&
                            !_isEmpty &&
                            _hasDebt &&
                            (_debtData?.debt ?? _totalPrice) > 0 &&
                            _hasValidSeller) ...[
                          const SizedBox(height: 24),
                          _buildDebtIndicator(),
                        ],

                        // Quantity selector - only show when in payment mode
                        if (_showPaymentButtons) ...[
                          const SizedBox(height: 24),
                          Text(
                            LocaleKeys
                                .nazoratchi_tuzilma_dialog_payment_days_question
                                .tr(),
                            style: AppTextStyles.bodyLarge.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w500,
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Quantity controls
                          Row(
                            children: [
                              _buildQuantityButton(
                                icon: Icons.remove,
                                onTap: () {
                                  if (_quantity > 1) {
                                    setState(() {
                                      _quantity--;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(width: 16),
                              Text(
                                _quantity.toString(),
                                style: AppTextStyles.headlineMedium.copyWith(
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 16),
                              _buildQuantityButton(
                                icon: Icons.add,
                                onTap: () {
                                  setState(() {
                                    _quantity++;
                                  });
                                },
                              ),
                            ],
                          ),

                          const SizedBox(height: 24),

                          // Total amount
                          Text(
                            '${_quantity * _totalDailyPrice} ${LocaleKeys.nazoratchi_tuzilma_dialog_currency_uzs.tr()}',
                            style: AppTextStyles.headlineLarge.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontWeight: FontWeight.w700,
                            ),
                          )
                        ],
                      ],
                    ),
                  ),

                  // Action buttons - conditional rendering based on state
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: _showPaymentButtons
                        ? _buildPaymentButtons()
                        : _buildInitialActionButtons(),
                  ),
                ],
              ),
            ),
          ),

          // Close button
          Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                width: 45,
                height: 45,
                decoration: BoxDecoration(
                  color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: AppColors.cTextGrayColor,
                  size: 25,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.cTextGrayColor,
            fontWeight: FontWeight.w400,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildTariffInfoWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Kategoriya
          _buildDetailRow(
              LocaleKeys.nazoratchi_tuzilma_dialog_category.tr(), _tariffTitle),
          const SizedBox(height: 8),

          // Tarif
          _buildDetailRow(
              LocaleKeys.nazoratchi_tuzilma_dialog_tariff.tr(),
              "${LocaleKeys.nazoratchi_tuzilma_dialog_daily_rate.tr()} (${_getDailyRateForDisplay().toString()} UZS)"),
          const SizedBox(height: 8),
          // Joy hajmi
          _buildDetailRow(
              LocaleKeys.nazoratchi_tuzilma_dialog_place_size.tr(),
              "${_getTotalSquareMeters().toString()} ${LocaleKeys.nazoratchi_tuzilma_dialog_place_size_meters
                  .tr()}"),
        ],
      ),
    );
  }

  /// Get daily rate for display in tariff info
  int _getDailyRateForDisplay() {
    if (_isApiMode) {
      // Always show the individual daily rate from the first square
      return widget.groupedSquares!.squares.first.tariff.price;
    }
    return 12000; // Default for legacy
  }

  Widget _buildQuantityButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.onSurface,
          size: 20,
        ),
      ),
    );
  }

  String _getStatusText() {
    if (_isApiMode) {
      final primaryStatus = widget.groupedSquares!.primaryStatus;
      switch (primaryStatus) {
        case SquareStatus.paid: // Status 4 = to'langan
          return LocaleKeys.nazoratchi_tuzilma_status_paid.tr();
        case SquareStatus.unpaid: // Status 3 = qarzdor
          return LocaleKeys.nazoratchi_tuzilma_status_unpaid.tr();
        case SquareStatus.unbinded: // Status 1 = belgilanmagan
          return LocaleKeys.nazoratchi_tuzilma_status_unassigned.tr();
        case SquareStatus.available: // Status 2 = bo'sh
          return LocaleKeys.nazoratchi_tuzilma_status_empty.tr();
      }
    }

    switch (widget.square.status) {
      case SquareStatus.paid:
        return LocaleKeys.nazoratchi_tuzilma_status_paid.tr();
      case SquareStatus.unpaid:
        return LocaleKeys.nazoratchi_tuzilma_status_unpaid.tr();
      case SquareStatus.unbinded:
        return LocaleKeys.nazoratchi_tuzilma_status_unassigned.tr();
      case SquareStatus.available:
        return LocaleKeys.nazoratchi_tuzilma_status_empty.tr();
    }
  }

  Color _getStatusColor() {
    if (_isApiMode) {
      final primaryStatus = widget.groupedSquares!.primaryStatus;
      switch (primaryStatus) {
        case SquareStatus.paid: // Status 4 = to'langan
          return AppColors.cGreenishColor;
        case SquareStatus.unpaid: // Status 3 = qarzdor
          return AppColors.cReddishColor;
        case SquareStatus.unbinded: // Status 1 = belgilanmagan
          return AppColors.cUnbindedColor;
        case SquareStatus.available: // Status 2 = bo'sh
          return AppColors.cYellowishColor;
      }
    }

    switch (widget.square.status) {
      case SquareStatus.paid:
        return AppColors.cGreenishColor;
      case SquareStatus.unpaid:
        return AppColors.cReddishColor;
      case SquareStatus.unbinded:
        return AppColors.cUnbindedColor;
      case SquareStatus.available:
        return AppColors.cYellowishColor;
    }
  }

  /// Get title color based on API status codes
  Color _getTitleColor() {
    if (_isApiMode) {
      final primaryStatus = widget.groupedSquares!.primaryStatus;
      switch (primaryStatus) {
        case SquareStatus.paid: // Status 4 = to'langan
          return AppColors.cGreenishColor;
        case SquareStatus.unpaid: // Status 3 = qarzdor
          return AppColors.cReddishColor;
        case SquareStatus.unbinded: // Status 1 = belgilanmagan
          return AppColors.cTextGrayColor; // Use visible gray
        case SquareStatus.available: // Status 2 = bo'sh
          return AppColors.cYellowishColor;
      }
    }

    switch (widget.square.status) {
      case SquareStatus.paid:
        return AppColors.cGreenishColor;
      case SquareStatus.unpaid:
        return AppColors.cReddishColor;
      case SquareStatus.unbinded:
        return AppColors.cTextGrayColor; // Use visible gray like empty dialog
      case SquareStatus.available:
        return AppColors.cYellowishColor;
    }
  }

  void _handleClickPayment(BuildContext context) {
    // Check if seller is valid before proceeding
    if (!_hasValidSeller) {
      debugPrint('Cannot proceed with payment: No valid seller');
      return;
    }

    // Get the parent context before closing the dialog
    final parentContext = Navigator.of(context).context;

    // Close current dialog first
    Navigator.of(context).pop();

    // Create payment first, then show QR payment dialog or old cheque confirmation
    _createPaymentAndShowQR(parentContext);
  }

  /// Create payment and show appropriate dialog (QR or old cheque confirmation)
  Future<void> _createPaymentAndShowQR(BuildContext context) async {
    // Show payment status bottomsheet that will handle old cheques (201) or new payments (200)
    showModalBottomSheet(
      context: context,
      // isDismissible: false,
      // enableDrag: false,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => PaymentStatusBottomSheet(
        paymentType: PaymentType.qr,
        quantity: _quantity,
        dailyRate: _totalDailyPrice,
        sellerId: _getSellerId(),
        day: _isApiMode
            ? widget.groupedSquares!.squares.first.date
            : DateTime.now().toIso8601String().split('T')[0],
        placeIds: _isApiMode
            ? widget.groupedSquares!.squares.map((s) => s.place).toList()
            : ['demo_place_id'],
        squareNumber: _isApiMode
            ? widget.groupedSquares!.displayTitle
            : widget.square.number.toString(),
        pavilionId: widget.pavilionId,
        onSuccess: () {
          Navigator.of(bottomSheetContext).pop(); // Close bottomsheet
          _handlePaymentSuccess(bottomSheetContext);
        },
        onCancel: () {
          Navigator.of(bottomSheetContext).pop(); // Close bottomsheet
        },
      ),
    );
  }

  void _handleCashPayment(BuildContext context) {
    // Check if seller is valid before proceeding
    if (!_hasValidSeller) {
      debugPrint('Cannot proceed with cash payment: No valid seller');
      return;
    }

    // Get the parent context before closing the dialog
    final parentContext = Navigator.of(context).context;

    // Close current dialog first
    Navigator.of(context).pop();

    // Create payment first, then show confirmation dialog
    _createPaymentAndShowDialog(parentContext, PaymentType.cash);
  }

  void _handleTerminalPayment(BuildContext context) {
    // Check if seller is valid before proceeding
    if (!_hasValidSeller) {
      debugPrint('Cannot proceed with terminal payment: No valid seller');
      return;
    }

    // Get the parent context before closing the dialog
    final parentContext = Navigator.of(context).context;

    // Close current dialog first
    Navigator.of(context).pop();

    // Create payment first, then show confirmation dialog
    _createPaymentAndShowDialog(parentContext, PaymentType.terminal);
  }

  /// Create payment and show appropriate confirmation dialog
  Future<void> _createPaymentAndShowDialog(
      BuildContext context, PaymentType paymentType) async {
    // Show payment status bottomsheet instead of separate dialogs
    showModalBottomSheet(
      context: context,
      // isDismissible: false,
      // enableDrag: false,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (bottomSheetContext) => PaymentStatusBottomSheet(
        paymentType: paymentType,
        quantity: _quantity,
        dailyRate: _totalDailyPrice,
        sellerId: _getSellerId(),
        day: _isApiMode
            ? widget.groupedSquares!.squares.first.date
            : DateTime.now().toIso8601String().split('T')[0],
        placeIds: _isApiMode
            ? widget.groupedSquares!.squares.map((s) => s.place).toList()
            : ['demo_place_id'],
        squareNumber: _isApiMode
            ? widget.groupedSquares!.displayTitle
            : widget.square.number.toString(),
        pavilionId: widget.pavilionId,
        onSuccess: () {
          Navigator.of(bottomSheetContext).pop(); // Close bottomsheet
          _handlePaymentSuccess(bottomSheetContext);
        },
        onCancel: () {
          Navigator.of(bottomSheetContext).pop(); // Close bottomsheet
        },
      ),
    );
  }

  /// Check if seller is available for payment
  bool get _hasValidSeller {
    if (_isApiMode && widget.groupedSquares!.squares.isNotEmpty) {
      // For grouped squares (API mode)
      final seller = widget.groupedSquares!.squares.first.seller;
      return seller != null && seller.id.isNotEmpty;
    } else if (widget.square.seller != null) {
      // For single square (legacy mode)
      return widget.square.seller!.id.isNotEmpty;
    }
    return false;
  }

  /// Check if Tekshirildi button should be shown
  /// Only show for Belgilanmagan status AND seller is truly null/empty
  bool _shouldShowTekshirildiButton() {
    debugPrint('🔍 Checking Tekshirildi button conditions:');

    if (_isApiMode) {
      // For grouped squares (API mode)
      // ALL squares must be status 1 (belgilanmagan) AND ALL must have truly null/empty seller
      final allUnbinded = widget.groupedSquares!.squares
          .every((square) => square.status == 1); // Status 1 = belgilanmagan

      // Check if ALL squares have truly no seller (null or empty ID)
      final allHaveNoSeller = widget.groupedSquares!.squares.every((square) =>
          square.seller == null ||
          square.seller!.id.isEmpty ||
          square.seller!.id.trim().isEmpty);

      debugPrint(
          '   - API Mode: allUnbinded=$allUnbinded, allHaveNoSeller=$allHaveNoSeller');
      debugPrint(
          '   - Squares: ${widget.groupedSquares!.squares.map((s) => 'status=${s.status}, seller=${s.seller?.id ?? "null"}').join(', ')}');
      debugPrint(
          '   - Seller details: ${widget.groupedSquares!.squares.map((s) => 'seller_full=${s.seller?.fullName ?? "null"}').join(', ')}');

      final result = allUnbinded && !allHaveNoSeller;
      debugPrint('   - Final result: $result');
      return result;
    } else {
      // For single square (legacy mode)
      final isUnbinded = widget.square.status == SquareStatus.unbinded;
      final hasNoSeller = widget.square.seller == null ||
          widget.square.seller!.id.isEmpty ||
          widget.square.seller!.id.trim().isEmpty;

      debugPrint(
          '   - Legacy Mode: isUnbinded=$isUnbinded, hasNoSeller=$hasNoSeller');
      debugPrint(
          '   - Square: status=${widget.square.status}, seller=${widget.square.seller?.id ?? "null"}');

      final result = isUnbinded && hasNoSeller;
      debugPrint('   - Final result: $result');
      return result;
    }
  }

  /// Get seller ID from rasta data
  String _getSellerId() {
    if (_isApiMode && widget.groupedSquares!.squares.isNotEmpty) {
      // For grouped squares (API mode) - this is the normal case
      final seller = widget.groupedSquares!.squares.first.seller;
      if (seller != null && seller.id.isNotEmpty) {
        debugPrint('Found seller ID from grouped squares: ${seller.id}');
        return seller.id;
      } else {
        debugPrint('Seller is null or empty in grouped squares');
      }
    } else if (widget.square.seller != null &&
        widget.square.seller!.id.isNotEmpty) {
      // For single square (legacy mode) - now has seller information
      debugPrint(
          'Found seller ID from single square: ${widget.square.seller!.id}');
      return widget.square.seller!.id;
    } else {
      debugPrint('No seller found in either grouped squares or single square');
    }

    // Fallback for demo mode or when no seller is found
    debugPrint('Warning: No seller found in rasta data, using demo seller ID');
    return 'demo_user_id';
  }

  /// Handle payment success - refresh grid immediately
  void _handlePaymentSuccess(BuildContext context) {
    // Immediately refresh the grid data using stored bloc reference
    _refreshGrid(context);

    // Note: We don't close dialogs here as they are managed by the payment flow
    // The payment status bottomsheet will handle its own success UI and closure
  }

  /// Handle payment error - just close QR bottomsheet, no additional dialogs needed
  void _showPaymentError(BuildContext context) {
    // The QR payment bottomsheet will handle its own error UI
    // We don't need to show additional error dialogs
  }

  void _showEmptySquareReportDialog(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) {
          if (widget.groupedSquares != null) {
            return EmptySquareReportPage.fromGroupedSquares(
              groupedSquares: widget.groupedSquares!,
              pavilionId: widget.pavilionId,
              onSuccess: () {
                // Refresh grid when returning from empty square report
                _refreshGrid(context);
              },
            );
          } else {
            return EmptySquareReportPage(
              square: widget.square,
              groupedSquares: null,
              pavilionId: widget.pavilionId,
              onSuccess: () {
                // Refresh grid when returning from empty square report
                _refreshGrid(context);
              },
            );
          }
        },
      ),
    );
  }

  /// Show place check dialog (Tekshirildi functionality)
  void _showPlaceCheckDialog(BuildContext context) {
    final sellerId = _getSellerId();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PlaceCheckDialog(
        sellerId: sellerId,
        onSuccess: () {
          // Refresh grid after successful verification
          _refreshGrid(context);
          Navigator.of(context).pop();
        },
        onCancel: () {
          // Just close the dialog, no additional action needed
        },
      ),
    );
  }

  /// Refresh the grid data
  void _refreshGrid(BuildContext context) {
    // Use the stored bloc reference instead of context
    _rastalarBloc.add(RefreshSquares(
      blockId: widget.blockId,
      pavilionId: widget.pavilionId,
    ));
  }

  /// Build debt indicator widget with API data and shimmer loading
  Widget _buildDebtIndicator() {
    if (_isLoadingDebt) {
      return _buildDebtShimmer();
    }

    if (_debtError != null) {
      return const SizedBox.shrink(); // Show nothing when error occurs
    }

    // Use API debt data if available, otherwise fallback to calculated price
    final debtAmount = _debtData?.debt ?? _totalPrice;
    final formattedAmount = _debtData?.formattedDebt ??
        debtAmount.toString().replaceAllMapped(
              RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
              (Match m) => '${m[1]},',
            );

    // Format price amount
    final priceAmount = _debtData?.price ?? 0;
    final formattedPrice = priceAmount.toString().replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );

    // Get debt days
    final debtDays = _debtData?.dabtDays ?? 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.cReddishColor.withAlpha(40),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.cReddishColor,
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // Main debt info row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Exclamation icon in pink circle
              // Qarzdorlik title and amount
              Expanded(
                child: Row(
                  children: [
                    Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: AppColors.cReddishColor,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.priority_high,
                        color: AppColors.white,
                        size: 10,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.nazoratchi_tuzilma_dialog_debt.tr(),
                          style: AppTextStyles.bodyMedium.copyWith(
                              color: AppColors.cReddishColor,
                              fontWeight: FontWeight.w500,
                              fontSize: 12),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '$formattedAmount ${LocaleKeys.nazoratchi_tuzilma_dialog_currency_uzs.tr()}',
                          style: AppTextStyles.titleLarge.copyWith(
                              color: AppColors.cReddishColor,
                              fontWeight: FontWeight.w700,
                              fontSize: 12),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // Days pill

                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.cReddishColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    "${debtDays} ${LocaleKeys.nazoratchi_tuzilma_dialog_debt_days.tr()}",
                    style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 10),
                  ),
                ),
            ],
          ),

          // Payment info section
          if (_debtData?.date != null) ...[
            const SizedBox(height: 16),
            // Last payment date
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  LocaleKeys.nazoratchi_tuzilma_dialog_last_payment_date.tr(),
                  style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w400,
                      fontSize: 10),
                ),
                Flexible(
                  child: Text(
                    _debtData!.formattedDate,
                    style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                        fontSize: 10),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // Last payment amount
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  LocaleKeys.nazoratchi_tuzilma_dialog_last_payment_amount.tr(),
                  style: AppTextStyles.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w400,
                      fontSize: 10),
                ),
                Flexible(
                  child: Text(
                    '$formattedPrice ${LocaleKeys.nazoratchi_tuzilma_dialog_currency_uzs.tr()}',
                    style: AppTextStyles.bodyMedium.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                        fontSize: 10),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Build shimmer loading for debt indicator
  Widget _buildDebtShimmer() {
    return UniversalLoading.shimmer(
      child: Container(
        padding: const EdgeInsets.all(26),
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.cGrayBorderColor,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            // Main debt info row shimmer
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 80,
                        height: 14,
                        decoration: BoxDecoration(
                          color:
                              AppColors.cTextGrayColor.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 120,
                        height: 16,
                        decoration: BoxDecoration(
                          color:
                              AppColors.cTextGrayColor.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ),
                // Refresh button shimmer
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.cTextGrayColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Payment date section shimmer
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.cTextGrayColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.cTextGrayColor.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: AppColors.cTextGrayColor.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 100,
                        height: 12,
                        decoration: BoxDecoration(
                          color:
                              AppColors.cTextGrayColor.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 140,
                        height: 14,
                        decoration: BoxDecoration(
                          color:
                              AppColors.cTextGrayColor.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build error state for debt indicator
  Widget _buildDebtError() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.error,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.error_outline_rounded,
              color: Theme.of(context).colorScheme.onError,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.nazoratchi_tuzilma_dialog_error_title.tr(),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _debtError ??
                      LocaleKeys.nazoratchi_tuzilma_dialog_data_not_loaded.tr(),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Theme.of(context).colorScheme.error,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          // Retry button
          InkWell(
            onTap: _loadDebtData,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Icon(
                Icons.refresh_rounded,
                color: AppColors.cFirstColor,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build initial action interface with conditional payment button
  Widget _buildInitialActionButtons() {
    return Column(
      children: [
        // To'lov qilish button - only show if seller is available
        if (_hasValidSeller) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _showPaymentButtons = true;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                foregroundColor: Theme.of(context).colorScheme.onSurface,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      LocaleKeys.nazoratchi_tuzilma_dialog_make_payment.tr(),
                      maxLines: 2,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Icon(Icons.arrow_forward_rounded,
                      color: Theme.of(context).colorScheme.onSurface, size: 24),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],

        // Show no seller message if payment is not available
        if (!_hasValidSeller) ...[
          _buildNoSellerMessage(),
          const SizedBox(height: 12),
        ],

        // Tekshirildi button - only show for Belgilanmagan status with null seller
        if (_shouldShowTekshirildiButton()) ...[
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                _showPlaceCheckDialog(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    Theme.of(context).colorScheme.surfaceContainerHighest,
                foregroundColor: Theme.of(context).colorScheme.onSurface,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Text(
                      LocaleKeys.nazoratchi_tuzilma_dialog_checked.tr(),
                      maxLines: 2,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_rounded,
                    color: Theme.of(context).colorScheme.onSurface,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
        ],

        // Bo'sh rasta deb belgilash button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showEmptySquareReportDialog(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor:
                  Theme.of(context).colorScheme.surfaceContainerHighest,
              foregroundColor: Theme.of(context).colorScheme.onSurface,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    LocaleKeys.nazoratchi_tuzilma_dialog_mark_empty_place.tr(),
                    maxLines: 2,
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_rounded,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 24,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build payment buttons interface (existing colorful buttons)
  Widget _buildPaymentButtons() {
    return Column(
      children: [
        // Back button to return to initial state
        MaterialButton(
          onPressed: () {
            setState(() {
              _showPaymentButtons = false;
            });
          },
          padding: EdgeInsets.zero,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Icon(
                Icons.arrow_back,
                color: Theme.of(context).colorScheme.onSurface,
                size: 24,
              ),
              Gap(12),
              Flexible(
                child: Text(
                  LocaleKeys.nazoratchi_tuzilma_dialog_select_payment_method
                      .tr(),
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Click orqali to'lash button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _handleClickPayment(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.cFirstColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  Assets.iconsClickLogoLight,
                  width: 20,
                  height: 20,
                  color: AppColors.white,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    LocaleKeys.nazoratchi_tuzilma_dialog_pay_with_click.tr(),
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Naqd pul bilan to'lash button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _handleCashPayment(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.cGreenishColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              LocaleKeys.nazoratchi_tuzilma_dialog_pay_with_cash.tr(),
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Terminal orqali to'lash button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _handleTerminalPayment(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.cCarrotColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              LocaleKeys.nazoratchi_tuzilma_dialog_pay_with_terminal.tr(),
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  /// Build message when no seller is available for payment
  Widget _buildNoSellerMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.info_outline,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            size: 32,
          ),
          const SizedBox(height: 12),
          Text(
            LocaleKeys.nazoratchi_tuzilma_payment_impossible.tr(),
            style: AppTextStyles.titleMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            LocaleKeys.nazoratchi_tuzilma_no_seller_info.tr(),
            textAlign: TextAlign.center,
            style: AppTextStyles.bodyMedium.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }
}
