import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'free_place_model.g.dart';

/// Request model for submitting free place report
class FreePlaceRequest extends Equatable {
  final String file; // Image file path
  final String place; // Place ID
  final String desc; // Description text

  const FreePlaceRequest({
    required this.file,
    required this.place,
    required this.desc,
  });

  /// Convert to Map for FormData
  Map<String, dynamic> toFormData() {
    return {
      'file': file,
      'place': place,
      'desc': desc,
    };
  }

  @override
  List<Object?> get props => [file, place, desc];
}

/// Response model for free place submission
@JsonSerializable()
class FreePlaceResponse extends Equatable {
  final bool success;
  final String? message;
  final Map<String, dynamic>? data;

  const FreePlaceResponse({
    required this.success,
    this.message,
    this.data,
  });

  factory FreePlaceResponse.fromJson(Map<String, dynamic> json) =>
      _$FreePlaceResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FreePlaceResponseToJson(this);

  /// Create success response
  factory FreePlaceResponse.success({String? message, Map<String, dynamic>? data}) {
    return FreePlaceResponse(
      success: true,
      message: message ?? 'Bo\'sh rasta muvaffaqiyatli belgilandi',
      data: data,
    );
  }

  /// Create error response
  factory FreePlaceResponse.error(String message) {
    return FreePlaceResponse(
      success: false,
      message: message,
      data: null,
    );
  }

  @override
  List<Object?> get props => [success, message, data];
}
