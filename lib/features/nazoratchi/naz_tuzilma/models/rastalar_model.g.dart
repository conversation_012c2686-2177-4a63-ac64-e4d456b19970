// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rastalar_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApiTariff _$ApiTariffFromJson(Map<String, dynamic> json) => ApiTariff(
      id: json['_id'] as String,
      title: json['title'] as String,
      price: (json['price'] as num).toInt(),
    );

Map<String, dynamic> _$ApiTariffToJson(ApiTariff instance) => <String, dynamic>{
      '_id': instance.id,
      'title': instance.title,
      'price': instance.price,
    };

ApiSeller _$ApiSellerFromJson(Map<String, dynamic> json) => ApiSeller(
      id: json['_id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      middleName: json['middleName'] as String?,
    );

Map<String, dynamic> _$ApiSellerToJson(ApiSeller instance) => <String, dynamic>{
      '_id': instance.id,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'middleName': instance.middleName,
    };

ApiSquare _$ApiSquareFromJson(Map<String, dynamic> json) => ApiSquare(
      id: json['_id'] as String,
      place: json['place'] as String,
      tariff: ApiTariff.fromJson(json['tariff'] as Map<String, dynamic>),
      squareMeter: (json['squareMeter'] as num).toInt(),
      price: (json['price'] as num).toInt(),
      status: (json['status'] as num).toInt(),
      title: (json['title'] as num).toInt(),
      payment: json['payment'] as bool? ?? false,
      supervisor: json['supervisor'] as String?,
      seller: json['seller'] == null
          ? null
          : ApiSeller.fromJson(json['seller'] as Map<String, dynamic>),
      date: json['date'] as String,
      photo: json['photo'] as String?,
      desc: json['desc'] as String?,
    );

Map<String, dynamic> _$ApiSquareToJson(ApiSquare instance) => <String, dynamic>{
      '_id': instance.id,
      'place': instance.place,
      'tariff': instance.tariff,
      'squareMeter': instance.squareMeter,
      'price': instance.price,
      'status': instance.status,
      'title': instance.title,
      'payment': instance.payment,
      'supervisor': instance.supervisor,
      'seller': instance.seller,
      'date': instance.date,
      'photo': instance.photo,
      'desc': instance.desc,
    };

ApiSquareResponse _$ApiSquareResponseFromJson(Map<String, dynamic> json) =>
    ApiSquareResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => ApiSquare.fromJson(e as Map<String, dynamic>))
          .toList(),
      success: json['success'] as bool? ?? true,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$ApiSquareResponseToJson(ApiSquareResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'success': instance.success,
      'message': instance.message,
    };
