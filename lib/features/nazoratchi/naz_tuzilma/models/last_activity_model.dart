import 'package:equatable/equatable.dart';

/// Model for Last Activity API response
class LastActivityResponse extends Equatable {
  final bool success;
  final String? message;
  final ActivityRecord? data;

  const LastActivityResponse({
    required this.success,
    this.message,
    this.data,
  });

  factory LastActivityResponse.fromJson(Map<String, dynamic> json) {
    return LastActivityResponse(
      success: json['success'] ?? true,
      message: json['message'],
      data: json['message'] != null ? ActivityRecord.fromJson(json) : null,
    );
  }

  factory LastActivityResponse.error(String message) {
    return LastActivityResponse(
      success: false,
      message: message,
      data: null,
    );
  }

  factory LastActivityResponse.empty() {
    return const LastActivityResponse(
      success: true,
      message: null,
      data: null,
    );
  }

  @override
  List<Object?> get props => [success, message, data];
}

/// Model for individual activity record
class ActivityRecord extends Equatable {
  final String id;
  final String? blockId;
  final String message;
  final DateTime timestamp;
  final String? userId;
  final String? userName;
  final String? userFullName;

  const ActivityRecord({
    required this.id,
    this.blockId,
    required this.message,
    required this.timestamp,
    this.userId,
    this.userName,
    this.userFullName,
  });

  factory ActivityRecord.fromJson(Map<String, dynamic> json) {
    // Extract user information
    final user = json['user'] as Map<String, dynamic>?;
    String? fullName;
    if (user != null) {
      final firstName = user['firstName'] ?? '';
      final lastName = user['lastName'] ?? '';
      final middleName = user['middleName'] ?? '';
      fullName = '$firstName $middleName $lastName'.trim();
      if (fullName.isEmpty) fullName = null;
    }

    return ActivityRecord(
      id: json['_id'] ?? json['id'] ?? '',
      blockId: json['block']?['_id'] ?? json['blockId'],
      message: json['message'] ?? '',
      timestamp: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : json['date'] != null
              ? DateTime.parse(json['date'])
              : DateTime.now(),
      userId: user?['_id'],
      userName: user?['phone'],
      userFullName: fullName,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'blockId': blockId,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'userId': userId,
      'userName': userName,
      'userFullName': userFullName,
    };
  }

  @override
  List<Object?> get props => [
        id,
        blockId,
        message,
        timestamp,
        userId,
        userName,
        userFullName,
      ];
}

/// Extension to get formatted time display
extension ActivityRecordExtension on ActivityRecord {
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Hozir';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} daqiqa oldin';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} soat oldin';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} kun oldin';
    } else {
      return '${timestamp.day}.${timestamp.month.toString().padLeft(2, '0')}.${timestamp.year}';
    }
  }
}
