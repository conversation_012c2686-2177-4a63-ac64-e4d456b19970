import 'package:equatable/equatable.dart';

/// Model for Last Activity API response
class LastActivityResponse extends Equatable {
  final bool success;
  final String? message;
  final List<ActivityRecord> data;

  const LastActivityResponse({
    required this.success,
    this.message,
    required this.data,
  });

  factory LastActivityResponse.fromJson(Map<String, dynamic> json) {
    return LastActivityResponse(
      success: json['success'] ?? true,
      message: json['message'],
      data: json['data'] != null
          ? (json['data'] as List)
              .map((item) => ActivityRecord.fromJson(item))
              .toList()
          : [],
    );
  }

  factory LastActivityResponse.error(String message) {
    return LastActivityResponse(
      success: false,
      message: message,
      data: [],
    );
  }

  @override
  List<Object?> get props => [success, message, data];
}

/// Model for individual activity record
class ActivityRecord extends Equatable {
  final String id;
  final String blockId;
  final String action;
  final String description;
  final int count;
  final DateTime timestamp;
  final String? userId;
  final String? userName;
  final Map<String, dynamic>? metadata;

  const ActivityRecord({
    required this.id,
    required this.blockId,
    required this.action,
    required this.description,
    required this.count,
    required this.timestamp,
    this.userId,
    this.userName,
    this.metadata,
  });

  factory ActivityRecord.fromJson(Map<String, dynamic> json) {
    return ActivityRecord(
      id: json['_id'] ?? json['id'] ?? '',
      blockId: json['blockId'] ?? json['block'] ?? '',
      action: json['action'] ?? '',
      description: json['description'] ?? json['desc'] ?? '',
      count: json['count'] ?? 0,
      timestamp: json['timestamp'] != null
          ? DateTime.parse(json['timestamp'])
          : json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      userId: json['userId'] ?? json['user']?['_id'],
      userName: json['userName'] ?? json['user']?['name'],
      metadata: json['metadata'] ?? json['meta'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'blockId': blockId,
      'action': action,
      'description': description,
      'count': count,
      'timestamp': timestamp.toIso8601String(),
      'userId': userId,
      'userName': userName,
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [
        id,
        blockId,
        action,
        description,
        count,
        timestamp,
        userId,
        userName,
        metadata,
      ];
}

/// Enum for different activity types
enum ActivityType {
  payment('payment'),
  placeAssignment('place_assignment'),
  statusUpdate('status_update'),
  report('report'),
  other('other');

  const ActivityType(this.value);
  final String value;

  static ActivityType fromString(String value) {
    return ActivityType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ActivityType.other,
    );
  }
}

/// Extension to get localized activity descriptions
extension ActivityRecordExtension on ActivityRecord {
  ActivityType get activityType => ActivityType.fromString(action);

  String get formattedDescription {
    // Format the description based on the activity type and count
    switch (activityType) {
      case ActivityType.payment:
        return '$count rastaga to\'lov qabul qilindi';
      case ActivityType.placeAssignment:
        return '$count rasta tayinlandi';
      case ActivityType.statusUpdate:
        return '$count rasta holati yangilandi';
      case ActivityType.report:
        return '$count hisobot yuborildi';
      case ActivityType.other:
        return description.isNotEmpty ? description : 'Faoliyat amalga oshirildi';
    }
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Hozir';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} daqiqa oldin';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} soat oldin';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} kun oldin';
    } else {
      return '${timestamp.day}.${timestamp.month.toString().padLeft(2, '0')}.${timestamp.year}';
    }
  }
}
