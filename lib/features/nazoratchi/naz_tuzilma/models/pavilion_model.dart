import 'package:easy_localization/easy_localization.dart';
import '../../../../translations/locale_keys.g.dart';

/// Model for Pavilion data from API
class Pavilion {
  final String id;
  final String title;

  const Pavilion({
    required this.id,
    required this.title,
  });

  factory Pavilion.fromJson(Map<String, dynamic> json) {
    return Pavilion(
      id: json['_id'] as String,
      title: json['title'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
    };
  }
}

/// Model for Pavilion response from API
class PavilionResponse {
  final Pavilion pavilion;
  final int blockCount;
  final int placeCount;

  const PavilionResponse({
    required this.pavilion,
    required this.blockCount,
    required this.placeCount,
  });

  factory PavilionResponse.fromJson(Map<String, dynamic> json) {
    return PavilionResponse(
      pavilion: Pavilion.fromJson(json['pavilion']),
      blockCount: json['blockCount'] as int,
      placeCount: json['placeCount'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pavilion': pavilion.toJson(),
      'blockCount': blockCount,
      'placeCount': placeCount,
    };
  }
}

/// Model for Block Status data from API
class BlockStatus {
  final int status;
  final int totalPrice;
  final int count;

  const BlockStatus({
    required this.status,
    required this.totalPrice,
    required this.count,
  });

  factory BlockStatus.fromJson(Map<String, dynamic> json) {
    return BlockStatus(
      status: json['status'] as int,
      totalPrice: json['totalPrice'] as int,
      count: json['count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'totalPrice': totalPrice,
      'count': count,
    };
  }
}

/// Model for Block data from API
class BlockData {
  final String id;
  final String title;
  final List<BlockStatus> statuses;

  const BlockData({
    required this.id,
    required this.title,
    required this.statuses,
  });

  factory BlockData.fromJson(Map<String, dynamic> json) {
    return BlockData(
      id: json['_id'] as String,
      title: json['title'] as String,
      statuses: (json['statuses'] as List)
          .map((status) => BlockStatus.fromJson(status))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'title': title,
      'statuses': statuses.map((status) => status.toJson()).toList(),
    };
  }

  /// Get status name based on status code
  /// 1 = belgilanmagan, 2 = bo'sh, 3 = qarzdor, 4 = to'langan
  String getStatusName(int statusCode) {
    switch (statusCode) {
      case 1:
        return LocaleKeys.nazoratchi_tuzilma_status_unassigned.tr();
      case 2:
        return LocaleKeys.nazoratchi_tuzilma_status_empty.tr();
      case 3:
        return LocaleKeys.nazoratchi_tuzilma_status_unpaid.tr();
      case 4:
        return LocaleKeys.nazoratchi_tuzilma_status_paid.tr();
      default:
        return LocaleKeys.nazoratchi_tuzilma_status_unknown.tr();
    }
  }

  /// Get only statuses that have data (count > 0)
  List<BlockStatus> get availableStatuses {
    return statuses.where((status) => status.count > 0).toList();
  }
}
