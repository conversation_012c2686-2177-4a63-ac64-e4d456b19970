# Rastalar Grid Shimmer - Usage Guide

## Overview

The Rastalar Grid Shimmer provides animated skeleton loading for the grid with flowing status colors from top to bottom. It creates an engaging loading experience that matches the actual grid layout and uses the same status colors as the real data.

## Features Implemented

✅ **Animated Status Colors** - Cycles through all 4 status colors (Green, <PERSON>, Yellow, Gray)
✅ **Top-to-Bottom Wave Effect** - Colors flow from top to bottom across the grid
✅ **Dimmed Color Palette** - Uses muted versions of status colors for subtle effect
✅ **Grid Layout Matching** - Matches the actual 4-column grid layout
✅ **Status Legend** - Includes the static status legend at the top
✅ **Dual Animation Controllers** - Wave animation + color cycling
✅ **Refresh Overlay** - Compact shimmer for refresh operations
✅ **Performance Optimized** - Efficient animations with proper disposal

## Status Colors Used

The shimmer uses dimmed versions of the actual status colors:

- **Green** (`#039855`) - To'langan (Paid)
- **Red** (`#FF0053`) - To<PERSON>lan<PERSON><PERSON> (Unpaid)  
- **Yellow** (`#FFB950`) - Bo'sh (Available)
- **Gray** (`#3A3B3C`) - Bel<PERSON>lanmagan (Unbinded)

## Components

### 1. RastalarGridShimmer (Main Component)

**Purpose**: Full-screen animated shimmer for initial loading
**Animation**: 2-second wave + 3-second color cycling
**Layout**: Status legend + 4x8 grid (32 squares)

```dart
// Usage in loading state
if (state.isLoading) {
  return const RastalarGridShimmer();
}
```

**Features**:
- Dual animation controllers for wave and color effects
- Row-based wave progression
- Color interpolation between status colors
- Shimmer content placeholders (number + status indicator)

### 2. RastalarGridShimmerCompact (Overlay Component)

**Purpose**: Subtle shimmer overlay during refresh operations
**Animation**: 1.5-second pulse animation
**Layout**: Configurable item count (default: 16)

```dart
// Usage during refresh
if (state.isRefreshing)
  Positioned.fill(
    child: Container(
      color: AppColors.cBackgroundColor.withValues(alpha: 0.7),
      child: const RastalarGridShimmerCompact(itemCount: 16),
    ),
  ),
```

**Features**:
- Simple opacity-based pulse animation
- Lightweight for overlay use
- Configurable grid size
- Minimal visual interference

## Animation Details

### Wave Animation
- **Duration**: 2000ms
- **Range**: -1.0 to 1.0
- **Curve**: `Curves.easeInOut`
- **Effect**: Creates flowing highlight effect from top to bottom
- **Calculation**: `(_waveAnimation.value + rowProgress) % 2.0 - 1.0`

### Color Animation
- **Duration**: 3000ms
- **Range**: 0.0 to 4.0 (number of colors)
- **Curve**: `Curves.linear`
- **Effect**: Cycles through all status colors continuously
- **Interpolation**: `Color.lerp()` for smooth transitions

### Highlight Logic
```dart
final isHighlighted = waveProgress > -0.3 && waveProgress < 0.3;
```
- Creates a "window" of highlighted squares
- Window moves from top to bottom
- Highlighted squares show the animated color
- Non-highlighted squares show base card color

## Integration Points

### 1. Rastalar Page Loading State
```dart
// Before: Simple CircularProgressIndicator
if (state.isLoading) {
  return const Center(
    child: CircularProgressIndicator(color: AppColors.cFirstColor),
  );
}

// After: Animated grid shimmer
if (state.isLoading) {
  return const RastalarGridShimmer();
}
```

### 2. Refresh State Overlay
```dart
child: Stack(
  children: [
    _buildSuccessWidget(context, state.groupedSquares),
    // Show subtle shimmer overlay during refresh
    if (state.isRefreshing)
      Positioned.fill(
        child: Container(
          color: AppColors.cBackgroundColor.withValues(alpha: 0.7),
          child: const RastalarGridShimmerCompact(itemCount: 16),
        ),
      ),
  ],
),
```

## Performance Considerations

### Animation Controllers
- **Proper Disposal**: Both controllers disposed in `dispose()`
- **Efficient Rebuilds**: `AnimatedBuilder` only rebuilds affected widgets
- **Optimized Calculations**: Row-based calculations minimize computation

### Memory Management
```dart
@override
void dispose() {
  _waveController.dispose();
  _colorController.dispose();
  super.dispose();
}
```

### Rendering Optimization
- Uses `Listenable.merge()` for efficient multi-animation listening
- Color interpolation cached per frame
- Minimal widget tree depth

## Customization Options

### Grid Size
```dart
// Main shimmer: Fixed 4x8 grid (32 items)
childCount: 32, // Show 8 rows of 4 squares

// Compact shimmer: Configurable
const RastalarGridShimmerCompact(itemCount: 20), // Custom size
```

### Animation Speed
```dart
// Wave speed
_waveController = AnimationController(
  duration: const Duration(milliseconds: 2000), // Adjustable
  vsync: this,
);

// Color cycling speed  
_colorController = AnimationController(
  duration: const Duration(milliseconds: 3000), // Adjustable
  vsync: this,
);
```

### Color Opacity
```dart
// Highlighted state opacity
final baseColor = isHighlighted 
    ? animatedColor.withValues(alpha: 0.4) // Adjustable
    : AppColors.cCardsColor;
    
final borderColor = isHighlighted
    ? animatedColor.withValues(alpha: 0.8) // Adjustable
    : AppColors.cGrayBorderColor.withValues(alpha: 0.3);
```

## File Structure

```
lib/features/nazoratchi/naz_tuzilma/
├── widgets/
│   ├── rastalar_grid_shimmer.dart       # Main shimmer implementation
│   ├── rastalar_status_legend.dart      # Status legend (reused)
│   └── RASTALAR_SHIMMER_USAGE.md        # This documentation
├── page/
│   └── rastalar_page.dart               # Integration point
└── bloc/rastalar_api/
    └── rastalar_api_state.dart          # State management
```

## Testing Scenarios

### 1. Initial Loading
- Navigate to rastalar page
- Verify animated shimmer appears
- Check wave effect flows top to bottom
- Verify color cycling through all 4 colors
- Confirm smooth transition to real data

### 2. Refresh Operation
- Pull to refresh on loaded grid
- Verify compact shimmer overlay appears
- Check refresh indicator in app bar
- Confirm overlay disappears when refresh completes

### 3. Performance Testing
- Monitor frame rate during animations
- Check memory usage with multiple controllers
- Verify proper cleanup on page disposal

## Future Enhancements

- [ ] Add staggered animation start for more organic feel
- [ ] Implement different wave patterns (diagonal, circular)
- [ ] Add shimmer intensity based on network speed
- [ ] Support for different grid layouts (3x, 5x columns)
- [ ] Add accessibility announcements for screen readers
- [ ] Implement shimmer for error states
- [ ] Add haptic feedback on shimmer completion

## Dependencies

- **Flutter**: Core animation framework
- **AppColors**: Status color definitions
- **RastalarStatusLegend**: Reused legend component
- **TickerProviderStateMixin**: Animation controller management

## Accessibility

- Shimmer animations are purely visual
- No impact on screen readers
- Maintains semantic structure of grid
- Respects system animation preferences (future enhancement)

The shimmer implementation provides a polished, engaging loading experience that maintains visual consistency with the actual grid data while providing clear feedback about the loading state.
