import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';

class NazStatusItem extends StatelessWidget {
  final String title;
  final String count;
  final String? price;
  final Color color;
  final String icon;
  final Color? textColor;
  final int status;

  const NazStatusItem({
    super.key,
    required this.title,
    required this.count,
    this.price,
    required this.color,
    required this.icon,
    this.textColor,
    required this.status,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        children: [
          Container(
            width: 6,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(cRadius12),
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(cRadius8),
                    bottomRight: Radius.circular(cRadius8)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: (status == 3) || (status == 4)
                          ? CrossAxisAlignment.start
                          : CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(icon),
                        Gap(8),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: (status == 3) || (status == 4)
                              ? MainAxisAlignment.start
                              : MainAxisAlignment.center,
                          children: [
                            Text(
                              title,
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: textColor ?? (status == 1
                                    ? Theme.of(context).colorScheme.onSurface
                                    : color),
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                              ),
                            ),
                            if ((status == 3) || (status == 4)) ...[
                              Gap(4),
                              Text(
                                price!,
                                style: AppTextStyles.bodySmall.copyWith(
                                  color: color,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 20,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Count
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        LocaleKeys.market_structure_places.tr(),
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.cTextGrayColor,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        count,
                        style: AppTextStyles.titleMedium.copyWith(
                          color: textColor ?? (status == 1
                              ? Theme.of(context).colorScheme.onSurface
                              : color),
                          fontSize: 24,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
