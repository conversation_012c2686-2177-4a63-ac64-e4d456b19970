import 'package:flutter/material.dart';
import '../../../../core/theme/app_colors.dart';
import 'rastalar_status_legend.dart';

/// Animated shimmer loading widget for rastalar grid
/// Features flowing status colors from top to bottom
class RastalarGridShimmer extends StatefulWidget {
  const RastalarGridShimmer({super.key});

  @override
  State<RastalarGridShimmer> createState() => _RastalarGridShimmerState();
}

class _RastalarGridShimmerState extends State<RastalarGridShimmer>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late AnimationController _colorController;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _colorAnimation;

  // Status colors (very subtle versions)
  static const List<Color> _statusColors = [
    Color(0x10039855), // Green (paid) - very subtle
    Color(0x10FF0053), // Red (unpaid) - very subtle
    Color(0x10FFB950), // Yellow (available) - very subtle
    Color(0x103A3B3C), // Gray (unbinded) - very subtle
  ];

  @override
  void initState() {
    super.initState();

    // Shimmer animation for the flowing effect
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Fast color cycling animation - 1 color per row
    _colorController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _shimmerAnimation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _colorAnimation = Tween<double>(
      begin: 0.0,
      end: _statusColors.length.toDouble(),
    ).animate(CurvedAnimation(
      parent: _colorController,
      curve: Curves.linear,
    ));

    _shimmerController.repeat();
    _colorController.repeat();
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _colorController.dispose();
    super.dispose();
  }

  /// Get gray shimmer base color for both themes
  Color _getShimmerBaseColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark
        ? const Color(0xFF3A3B3C) // Lighter gray for better visibility in dark theme
        : const Color(0xFFF0F0F0); // Light gray for light theme
  }

  @override
  Widget build(BuildContext context) {
    return CustomScrollView(
      slivers: [
        // Status legend (shimmer)
        SliverToBoxAdapter(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: const RastalarStatusLegend(
              rastalarCount: 0,
              isLoading: true,
            ),
          ),
        ),

        // Animated shimmer grid
        SliverPadding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                return _buildShimmerSquare(index);
              },
              childCount: 32, // Show 8 rows of 4 squares
            ),
          ),
        ),

        // Bottom padding
        const SliverToBoxAdapter(
          child: SizedBox(height: 100),
        ),
      ],
    );
  }

  Widget _buildShimmerSquare(int index) {
    return AnimatedBuilder(
      animation: Listenable.merge([_shimmerAnimation, _colorAnimation]),
      builder: (context, child) {
        // Calculate position in grid (row and column)
        final row = index ~/ 4;
        final col = index % 4;

        // Each row gets its own color based on row index
        final rowColorIndex = row % _statusColors.length;
        final rowColor = _statusColors[rowColorIndex];

        // Calculate shimmer progress for this square
        final shimmerProgress = _shimmerAnimation.value + (col * 0.1);
        final isShimmering = (shimmerProgress % 2.0 - 1.0).abs() < 0.5;

        // Create shimmer gradient effect
        final shimmerIntensity = isShimmering ? 1.0 : 0.3;

        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getShimmerBaseColor(context),
                  rowColor.withValues(alpha: shimmerIntensity * 0.15),
                  _getShimmerBaseColor(context),
                ],
                stops: [
                  (shimmerProgress - 0.5).clamp(0.0, 1.0),
                  shimmerProgress.clamp(0.0, 1.0),
                  (shimmerProgress + 0.5).clamp(0.0, 1.0),
                ],
              ).createShader(bounds);
            },
            child: Container(
              decoration: BoxDecoration(
                color: _getShimmerBaseColor(context),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildShimmerContent(isShimmering),
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerContent(bool isShimmering) {
    return Padding(
      padding: const EdgeInsets.all(4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Shimmer number placeholder
          Container(
            width: 20,
            height: 12,
            decoration: BoxDecoration(
              color: isShimmering
                  ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4)
                  : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          const SizedBox(height: 4),

          // Shimmer status indicator
          Container(
            width: 16,
            height: 8,
            decoration: BoxDecoration(
              color: isShimmering
                  ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3)
                  : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
        ],
      ),
    );
  }
}
