import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:intl/intl.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../bloc/last_activity/last_activity_bloc.dart';
import '../models/last_activity_model.dart';

class LastActivityWidget extends StatelessWidget {
  final String? blockId;

  const LastActivityWidget({
    super.key,
    required this.blockId,
  });

  @override
  Widget build(BuildContext context) {
    if (blockId == null || blockId!.isEmpty) {
      return const SizedBox.shrink();
    }

    return BlocProvider(
      create: (context) => GetIt.instance<LastActivityBloc>()
        ..add(LoadLastActivity(blockId: blockId!)),
      child: _LastActivityContent(blockId: blockId!),
    );
  }
}

class _LastActivityContent extends StatefulWidget {
  final String blockId;

  const _LastActivityContent({required this.blockId});

  @override
  State<_LastActivityContent> createState() => _LastActivityContentState();
}

class _LastActivityContentState extends State<_LastActivityContent>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LastActivityBloc, LastActivityState>(
      builder: (context, state) {
        if (state.isLoading) {
          _animationController.forward();
          return FadeTransition(
            opacity: _fadeAnimation,
            child: _buildLoadingWidget(context),
          );
        }

        if (state.hasError) {
          _animationController.forward();
          return FadeTransition(
            opacity: _fadeAnimation,
            child: _buildErrorWidget(
                context, widget.blockId, state.message ?? 'Error occurred'),
          );
        }

        if (state.shouldShow && state.activity != null) {
          _animationController.forward();
          return FadeTransition(
            opacity: _fadeAnimation,
            child: _buildActivityWidget(context, state.activity!),
          );
        }

        // Hide widget with fade out animation
        _animationController.reverse();
        return FadeTransition(
          opacity: _fadeAnimation,
          child: const SizedBox.shrink(),
        );
      },
    );
  }

  Widget _buildActivityWidget(BuildContext context, ActivityRecord activity) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cFirstColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.cFirstColor.withOpacity(0.8),
          width: 1,
        )
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: AppColors.white,
                size: 16.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Oxirgi harakat',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            activity.message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),

          ...[
          SizedBox(height: 4.h),
          Text(
            DateFormat('dd.MM.yyyy HH:mm').format(activity.timestamp.toLocal()),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.white.withOpacity(0.8),
              fontSize: 12.sp,
            ),
          ),
        ],

          ///Time ago message
          // if (activity.timeAgo.isNotEmpty) ...[
          //   SizedBox(height: 4.h),
          //   Text(
          //     activity.timeAgo,
          //     style: AppTextStyles.bodySmall.copyWith(
          //       color: AppColors.white.withOpacity(0.8),
          //       fontSize: 12.sp,
          //     ),
          //   ),
          // ],
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cFirstColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16.w,
            height: 16.w,
            child: CircularProgressIndicator(
              color: AppColors.white,
              strokeWidth: 2,
            ),
          ),
          SizedBox(width: 12.w),
          Text(
            'Yuklanmoqda...',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(
      BuildContext context, String blockId, String message) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cReddishColor.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.white,
            size: 16.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'Ma\'lumot yuklanmadi',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              context.read<LastActivityBloc>().add(
                    RefreshLastActivity(blockId: blockId),
                  );
            },
            child: Icon(
              Icons.refresh,
              color: AppColors.white,
              size: 18.sp,
            ),
          ),
        ],
      ),
    );
  }
}
