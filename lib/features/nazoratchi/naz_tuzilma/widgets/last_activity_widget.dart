import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/theme/app_text_styles.dart';
import '../bloc/last_activity/last_activity_bloc.dart';
import '../models/last_activity_model.dart';

class LastActivityWidget extends StatelessWidget {
  final String? blockId;

  const LastActivityWidget({
    super.key,
    required this.blockId,
  });

  @override
  Widget build(BuildContext context) {
    if (blockId == null || blockId!.isEmpty) {
      return const SizedBox.shrink();
    }

    return BlocProvider(
      create: (context) => GetIt.instance<LastActivityBloc>()
        ..add(LoadLastActivity(blockId: blockId!, limit: 1)),
      child: _LastActivityContent(blockId: blockId!),
    );
  }
}

class _LastActivityContent extends StatelessWidget {
  final String blockId;

  const _LastActivityContent({required this.blockId});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LastActivityBloc, LastActivityState>(
      builder: (context, state) {
        if (state.isLoading) {
          return _buildLoadingWidget(context);
        }

        if (state.hasError) {
          return _buildErrorWidget(context, state.message ?? 'Error occurred');
        }

        if (state.hasData && state.latestActivity != null) {
          return _buildActivityWidget(context, state.latestActivity!);
        }

        // Show mock data for demo purposes
        return _buildMockActivityWidget(context);
      },
    );
  }

  Widget _buildActivityWidget(BuildContext context, ActivityRecord activity) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cFirstColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cFirstColor.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: AppColors.white,
                size: 16.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Oxirgi harakat',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            activity.formattedDescription,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (activity.timeAgo.isNotEmpty) ...[
            SizedBox(height: 4.h),
            Text(
              activity.timeAgo,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.white.withOpacity(0.8),
                fontSize: 12.sp,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMockActivityWidget(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cFirstColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.cFirstColor.withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.access_time,
                color: AppColors.white,
                size: 16.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'Oxirgi harakat',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '21 rastaga to\'lov qabul qilindi',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            '15 daqiqa oldin',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.white.withOpacity(0.8),
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cFirstColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 16.w,
            height: 16.w,
            child: CircularProgressIndicator(
              color: AppColors.white,
              strokeWidth: 2,
            ),
          ),
          SizedBox(width: 12.w),
          Text(
            'Yuklanmoqda...',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(BuildContext context, String message) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.cReddishColor.withOpacity(0.8),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.white,
            size: 16.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              'Ma\'lumot yuklanmadi',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              context.read<LastActivityBloc>().add(
                RefreshLastActivity(blockId: blockId, limit: 1),
              );
            },
            child: Icon(
              Icons.refresh,
              color: AppColors.white,
              size: 18.sp,
            ),
          ),
        ],
      ),
    );
  }
}
