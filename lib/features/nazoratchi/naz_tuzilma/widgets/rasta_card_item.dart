import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/models/pavilion_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../translations/locale_keys.g.dart';

enum RastaStatus { active, pending, inactive }

class RastaCardItem extends StatelessWidget {

  final PavilionResponse pavilionResponse;
  final VoidCallback? onTap;

  const RastaCardItem({
    super.key,
    required this.pavilionResponse,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(cRadius12.r),
          child: Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(cRadius12.r),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context),
                Gap(20),
                _buildStatusRow(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // Large pavilion number
        // Container(
        //   width: 60.w,
        //   height: 60.w,
        //   decoration: BoxDecoration(
        //     color: AppColors.cBackgroundColor,
        //     borderRadius: BorderRadius.circular(cRadius8.r),
        //     border: Border.all(
        //       color: AppColors.cGrayBorderColor,
        //       width: 1,
        //     ),
        //   ),
        //   child: Center(
        //     child: Text(
        //       rastaNumber,
        //       style: AppTextStyles.titleLarge.copyWith(
        //         color: AppColors.white,
        //         fontWeight: FontWeight.w700,
        //         fontSize: 24.sp,
        //       ),
        //     ),
        //   ),
        // ),
        // Gap(12.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    pavilionResponse.pavilion.title,
                    style: AppTextStyles.titleMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                      fontSize: 20
                    ),
                  ),
                  Text(
                    LocaleKeys.market_structure_detailed.tr(),
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                      fontSize: 12.sp,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusRow(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildStatusItem(context,LocaleKeys.market_structure_blocks.tr(), pavilionResponse.blockCount.toString() ?? '')),
        Expanded(child: _buildStatusItem(context,LocaleKeys.market_structure_places.tr(), pavilionResponse.placeCount.toString() ?? '')),
      ],
    );
  }

  Widget _buildStatusItem(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodySmall.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 16,
          ),
        ),
        Gap(4),
        Text(
          value,
          style: AppTextStyles.titleMedium.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.w600,
            fontSize: 24,
          ),
        ),
      ],
    );
  }
}
