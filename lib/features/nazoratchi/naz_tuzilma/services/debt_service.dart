import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../../../../core/services/error_handler_service.dart';
import '../../../../core/services/dio_error_handler.dart';
import '../../../../core/utils/api_path.dart';
import '../models/debt_model.dart';

/// Service for handling debt-related API calls
class DebtService {
  final Dio _dio;

  DebtService({required Dio dio}) : _dio = dio;

  /// Get debt information for a seller
  ///
  /// [sellerId] - Seller ID to get debt for
  Future<ApiResult<DebtResponse>> getDebt(String sellerId) async {
    return await ErrorHandlerService.executeApiCall<DebtResponse>(
      () async {
        debugPrint('DebtService: Fetching debt for seller: $sellerId');

        final response = await _dio.get(
          ApiPath.debtPath,
          queryParameters: {
            'seller': sellerId,
          },
          options: Options(
            headers: {
              'Accept': 'application/json',
            },
          ),
        );

        debugPrint('DebtService: Response status: ${response.statusCode}');
        debugPrint('DebtService: Response data: ${response.data}');

        if (response.statusCode == 200) {
          return DebtResponse.fromJson(response.data);
        } else {
          throw DioException(
            requestOptions: response.requestOptions,
            response: response,
            type: DioExceptionType.badResponse,
            message: 'Failed to load debt information',
          );
        }
      },
      showToast: false, // We'll handle UI feedback in the calling widget
    );
  }
}
