import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import '../../models/last_activity_model.dart';
import '../../datasources/last_activity_remote_datasource.dart';

part 'last_activity_event.dart';
part 'last_activity_state.dart';

/// Base class for last activity events
abstract class LastActivityEvent extends Equatable {
  const LastActivityEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load last activity data
class LoadLastActivity extends LastActivityEvent {
  final String blockId;
  final int? limit;

  const LoadLastActivity({
    required this.blockId,
    this.limit,
  });

  @override
  List<Object?> get props => [blockId, limit];
}

/// Event to refresh last activity data
class RefreshLastActivity extends LastActivityEvent {
  final String blockId;
  final int? limit;

  const RefreshLastActivity({
    required this.blockId,
    this.limit,
  });

  @override
  List<Object?> get props => [blockId, limit];
}

/// Event to clear last activity data
class ClearLastActivity extends LastActivityEvent {
  const ClearLastActivity();
}

/// Enum for last activity status
enum LastActivityStatus { initial, loading, success, failure }

/// State class for last activity
class LastActivityState extends Equatable {
  final LastActivityStatus status;
  final List<ActivityRecord> activities;
  final String? message;
  final bool isRefreshing;

  const LastActivityState({
    this.status = LastActivityStatus.initial,
    this.activities = const [],
    this.message,
    this.isRefreshing = false,
  });

  LastActivityState copyWith({
    LastActivityStatus? status,
    List<ActivityRecord>? activities,
    String? message,
    bool? isRefreshing,
  }) {
    return LastActivityState(
      status: status ?? this.status,
      activities: activities ?? this.activities,
      message: message,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  bool get isLoading => status == LastActivityStatus.loading;
  bool get hasError => status == LastActivityStatus.failure;
  bool get hasData => activities.isNotEmpty;
  ActivityRecord? get latestActivity => activities.isNotEmpty ? activities.first : null;

  @override
  List<Object?> get props => [status, activities, message, isRefreshing];
}

class LastActivityBloc extends Bloc<LastActivityEvent, LastActivityState>
    with ErrorHandlerMixin<LastActivityEvent, LastActivityState> {
  final NetworkInfo networkInfo;
  final LastActivityRemoteDatasourceImpl remoteDatasource;

  LastActivityBloc({
    required this.networkInfo,
    required this.remoteDatasource,
  }) : super(const LastActivityState()) {
    on<LoadLastActivity>(_onLoadLastActivity);
    on<RefreshLastActivity>(_onRefreshLastActivity);
    on<ClearLastActivity>(_onClearLastActivity);
  }

  Future<void> _onLoadLastActivity(
    LoadLastActivity event,
    Emitter<LastActivityState> emit,
  ) async {
    emit(state.copyWith(status: LastActivityStatus.loading));

    try {
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          status: LastActivityStatus.failure,
          message: 'No internet connection',
        ));
        return;
      }

      final response = await remoteDatasource.fetchLastActivity(
        blockId: event.blockId,
        limit: event.limit,
      );

      if (response.success) {
        emit(state.copyWith(
          status: LastActivityStatus.success,
          activities: response.data,
          message: null,
        ));
      } else {
        emit(state.copyWith(
          status: LastActivityStatus.failure,
          message: response.message ?? 'Failed to load activity',
        ));
      }
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      emit(state.copyWith(
        status: LastActivityStatus.failure,
        message: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: LastActivityStatus.failure,
        message: 'Unexpected error: $e',
      ));
    }
  }

  Future<void> _onRefreshLastActivity(
    RefreshLastActivity event,
    Emitter<LastActivityState> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true));

    try {
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          isRefreshing: false,
          message: 'No internet connection',
        ));
        return;
      }

      final response = await remoteDatasource.fetchLastActivity(
        blockId: event.blockId,
        limit: event.limit,
      );

      if (response.success) {
        emit(state.copyWith(
          status: LastActivityStatus.success,
          activities: response.data,
          isRefreshing: false,
          message: null,
        ));
      } else {
        emit(state.copyWith(
          isRefreshing: false,
          message: response.message ?? 'Failed to refresh activity',
        ));
      }
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      emit(state.copyWith(
        isRefreshing: false,
        message: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        isRefreshing: false,
        message: 'Unexpected error: $e',
      ));
    }
  }

  void _onClearLastActivity(
    ClearLastActivity event,
    Emitter<LastActivityState> emit,
  ) {
    emit(const LastActivityState());
  }

  @override
  Future<bool> isNetworkConnected() async {
    return await networkInfo.isConnected;
  }
}
