part of 'last_activity_bloc.dart';

/// Base class for last activity events
abstract class LastActivityEvent extends Equatable {
  const LastActivityEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load last activity data
class LoadLastActivity extends LastActivityEvent {
  final String blockId;
  final int? limit;

  const LoadLastActivity({
    required this.blockId,
    this.limit,
  });

  @override
  List<Object?> get props => [blockId, limit];
}

/// Event to refresh last activity data
class RefreshLastActivity extends LastActivityEvent {
  final String blockId;
  final int? limit;

  const RefreshLastActivity({
    required this.blockId,
    this.limit,
  });

  @override
  List<Object?> get props => [blockId, limit];
}

/// Event to clear last activity data
class ClearLastActivity extends LastActivityEvent {
  const ClearLastActivity();
}
