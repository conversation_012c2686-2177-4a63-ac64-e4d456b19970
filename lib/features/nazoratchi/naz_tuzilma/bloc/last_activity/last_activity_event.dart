part of 'last_activity_bloc.dart';

/// Base class for last activity events
abstract class LastActivityEvent extends Equatable {
  const LastActivityEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load last activity data
class LoadLastActivity extends LastActivityEvent {
  final String blockId;

  const LoadLastActivity({
    required this.blockId,
  });

  @override
  List<Object?> get props => [blockId];
}

/// Event to refresh last activity data
class RefreshLastActivity extends LastActivityEvent {
  final String blockId;

  const RefreshLastActivity({
    required this.blockId,
  });

  @override
  List<Object?> get props => [blockId];
}

/// Event to clear last activity data
class ClearLastActivity extends LastActivityEvent {
  const ClearLastActivity();
}
