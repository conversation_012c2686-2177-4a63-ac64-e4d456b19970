part of 'last_activity_bloc.dart';

/// Enum for last activity status
enum LastActivityStatus { initial, loading, success, failure }

/// State class for last activity
class LastActivityState extends Equatable {
  final LastActivityStatus status;
  final ActivityRecord? activity;
  final String? message;
  final bool isRefreshing;

  const LastActivityState({
    this.status = LastActivityStatus.initial,
    this.activity,
    this.message,
    this.isRefreshing = false,
  });

  LastActivityState copyWith({
    LastActivityStatus? status,
    ActivityRecord? activity,
    String? message,
    bool? isRefreshing,
  }) {
    return LastActivityState(
      status: status ?? this.status,
      activity: activity,
      message: message,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  bool get isLoading => status == LastActivityStatus.loading;
  bool get hasError => status == LastActivityStatus.failure;
  bool get hasData => activity != null;
  bool get shouldShow => hasData && activity!.message.isNotEmpty;

  @override
  List<Object?> get props => [status, activity, message, isRefreshing];
}
