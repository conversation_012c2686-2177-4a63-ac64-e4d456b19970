part of 'last_activity_bloc.dart';

/// Enum for last activity status
enum LastActivityStatus { initial, loading, success, failure }

/// State class for last activity
class LastActivityState extends Equatable {
  final LastActivityStatus status;
  final List<ActivityRecord> activities;
  final String? message;
  final bool isRefreshing;

  const LastActivityState({
    this.status = LastActivityStatus.initial,
    this.activities = const [],
    this.message,
    this.isRefreshing = false,
  });

  LastActivityState copyWith({
    LastActivityStatus? status,
    List<ActivityRecord>? activities,
    String? message,
    bool? isRefreshing,
  }) {
    return LastActivityState(
      status: status ?? this.status,
      activities: activities ?? this.activities,
      message: message,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  bool get isLoading => status == LastActivityStatus.loading;
  bool get hasError => status == LastActivityStatus.failure;
  bool get hasData => activities.isNotEmpty;
  ActivityRecord? get latestActivity => activities.isNotEmpty ? activities.first : null;

  @override
  List<Object?> get props => [status, activities, message, isRefreshing];
}
