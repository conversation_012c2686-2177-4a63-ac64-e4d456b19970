part of 'naz_pavilion_bloc.dart';

sealed class NazPavilionEvent extends Equatable {
  const NazPavilionEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load blocks for a pavilion
class LoadBlocks extends NazPavilionEvent {
  final String pavilionId;

  const LoadBlocks(this.pavilionId);

  @override
  List<Object?> get props => [pavilionId];
}

/// Event to refresh blocks for a pavilion
class RefreshBlocks extends NazPavilionEvent {
  final String pavilionId;

  const RefreshBlocks(this.pavilionId);

  @override
  List<Object?> get props => [pavilionId];
}
