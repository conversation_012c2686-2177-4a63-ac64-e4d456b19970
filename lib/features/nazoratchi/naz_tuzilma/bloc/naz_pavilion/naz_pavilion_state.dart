part of 'naz_pavilion_bloc.dart';

enum NazPavilionStatus { initial, loading, success, failure }

/// Error type enum for better error handling
enum NazPavilionErrorType { network, api, unknown }

class NazPavilionState extends Equatable {
  final NazPavilionStatus status;
  final List<BlockData> blocks;
  final String? message;
  final NazPavilionErrorType? errorType;
  final bool isRefreshing;
  final bool hasRefreshError;

  const NazPavilionState({
    this.status = NazPavilionStatus.initial,
    this.blocks = const [],
    this.message,
    this.errorType,
    this.isRefreshing = false,
    this.hasRefreshError = false,
  });

  NazPavilionState copyWith({
    NazPavilionStatus? status,
    List<BlockData>? blocks,
    String? message,
    NazPavilionErrorType? errorType,
    bool? isRefreshing,
    bool? hasRefreshError,
  }) {
    return NazPavilionState(
      status: status ?? this.status,
      blocks: blocks ?? this.blocks,
      message: message ?? this.message,
      errorType: errorType ?? this.errorType,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasRefreshError: hasRefreshError ?? this.hasRefreshError,
    );
  }

  /// Check if there are any blocks loaded
  bool get hasAnyBlocks => blocks.isNotEmpty;

  /// Check if state is loading
  bool get isLoading => status == NazPavilionStatus.loading;

  /// Check if state is success
  bool get isSuccess => status == NazPavilionStatus.success;

  /// Check if state is failure
  bool get isFailure => status == NazPavilionStatus.failure;

  /// Check if state is initial
  bool get isInitial => status == NazPavilionStatus.initial;

  /// Check if error is network related
  bool get isNetworkError => isFailure && errorType == NazPavilionErrorType.network;

  /// Check if error is API related
  bool get isApiError => isFailure && errorType == NazPavilionErrorType.api;

  @override
  List<Object?> get props => [
        status,
        blocks,
        message,
        errorType,
        isRefreshing,
        hasRefreshError,
      ];

  @override
  String toString() {
    return 'NazPavilionState(status: $status, message: $message, isRefreshing: $isRefreshing, hasRefreshError: $hasRefreshError, blocksCount: ${blocks.length})';
  }
}
