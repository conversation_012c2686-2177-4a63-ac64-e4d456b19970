import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:equatable/equatable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import '../../datasources/naz_pavilion_remote_datasource.dart';
import '../../models/pavilion_model.dart';

part 'naz_pavilion_event.dart';
part 'naz_pavilion_state.dart';

/// BLoC for managing naz pavilion state
class NazPavilionBloc extends Bloc<NazPavilionEvent, NazPavilionState>
    with ErrorHandlerMixin<NazPavilionEvent, NazPavilionState> {
  final NazPavilionRemoteDatasource _remoteDatasource;
  final NetworkInfo _networkInfo;

  NazPavilionBloc({
    required NazPavilionRemoteDatasource remoteDatasource,
    required NetworkInfo networkInfo,
  })  : _remoteDatasource = remoteDatasource,
        _networkInfo = networkInfo,
        super(const NazPavilionState()) {
    on<LoadBlocks>(_onLoadBlocks);
    on<RefreshBlocks>(_onRefreshBlocks);
  }

  /// Handle load blocks event
  Future<void> _onLoadBlocks(
    LoadBlocks event,
    Emitter<NazPavilionState> emit,
  ) async {
    await executeApiCall<List<BlockData>>(
      apiCall: () => _remoteDatasource.getBlocks(event.pavilionId),
      onLoading: () => emit(state.copyWith(
        status: NazPavilionStatus.loading,
        errorType: null,
        isRefreshing: false,
        hasRefreshError: false,
      )),
      onSuccess: (blocks) {
        emit(state.copyWith(
          status: NazPavilionStatus.success,
          blocks: blocks,
          message: null,
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: NazPavilionStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        hasRefreshError: false,
      )),
    );
  }

  /// Handle refresh blocks event
  Future<void> _onRefreshBlocks(
    RefreshBlocks event,
    Emitter<NazPavilionState> emit,
  ) async {
    // Set refreshing flag and keep existing data
    emit(state.copyWith(
      isRefreshing: true,
      errorType: null,
      hasRefreshError: false,
    ));

    // Check network connectivity first for refresh operations
    if (!await isNetworkConnected()) {
      // If no network during refresh, emit network error but keep existing data
      emit(state.copyWith(
        status: state.hasAnyBlocks ? NazPavilionStatus.success : NazPavilionStatus.failure,
        message: LocaleKeys.nazoratchi_tuzilma_errors_network_connection.tr(),
        errorType: NazPavilionErrorType.network,
        isRefreshing: false,
        hasRefreshError: true,
      ));
      return;
    }

    // Attempt to load fresh data
    await executeApiCall<List<BlockData>>(
      apiCall: () => _remoteDatasource.getBlocks(event.pavilionId),
      onLoading: () {
        // Don't change status to loading during refresh to keep existing content visible
      },
      onSuccess: (blocks) {
        emit(state.copyWith(
          status: NazPavilionStatus.success,
          blocks: blocks,
          message: null,
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) {
        // Keep existing data and just set error info for toast
        emit(state.copyWith(
          status: state.hasAnyBlocks ? NazPavilionStatus.success : NazPavilionStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isRefreshing: false,
          hasRefreshError: true,
        ));
      },
    );
  }

  /// Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await _networkInfo.isConnected;
  }

  /// Determine error type based on error message
  NazPavilionErrorType _determineErrorType(String message) {
    final networkConnectionText = LocaleKeys.nazoratchi_tuzilma_errors_network_connection.tr();
    final networkUnavailableText = LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr();

    if (message.contains(networkConnectionText) ||
        message.contains(networkUnavailableText) ||
        message.contains('connection') ||
        message.contains('network')) {
      return NazPavilionErrorType.network;
    }
    return NazPavilionErrorType.api;
  }
}
