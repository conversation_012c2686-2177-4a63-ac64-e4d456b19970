import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import '../../models/rastalar_model.dart';
import '../../datasources/naz_rastalar_remote_datasource.dart';

part 'rastalar_api_event.dart';
part 'rastalar_api_state.dart';

class RastalarApiBloc extends Bloc<RastalarApiEvent, RastalarApiState>
    with ErrorHandlerMixin<RastalarApiEvent, RastalarApiState> {
  final NetworkInfo networkInfo;
  final NazRastalarRemoteDatasourceImpl remoteDatasource;

  RastalarApiBloc({
    required this.networkInfo,
    required this.remoteDatasource,
  }) : super(const RastalarApiState()) {
    on<LoadSquares>(_onLoadSquares);
    on<RefreshSquares>(_onRefreshSquares);
    on<UpdateSquareStatus>(_onUpdateSquareStatus);
    on<SubmitFreePlaceReport>(_onSubmitFreePlaceReport);
    on<SubmitEmptySquareReport>(_onSubmitEmptySquareReport);
  }

  Future<void> _onLoadSquares(
    LoadSquares event,
    Emitter<RastalarApiState> emit,
  ) async {
    emit(state.copyWith(status: RastalarApiStatus.loading));

    try {
      // Check network connectivity
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          status: RastalarApiStatus.failure,
          message: LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr(),
        ));
        return;
      }

      // Fetch squares from API
      final squares =
          await remoteDatasource.fetchSquares(blockId: event.blockId, pavilionId: event.pavilionId);

      // Group squares by seller
      final groupedSquares = remoteDatasource.groupSquaresBySeller(squares);

      emit(state.copyWith(
        status: RastalarApiStatus.success,
        groupedSquares: groupedSquares,
        message: null,
      ));
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      print('DioException in LoadSquares: $errorMessage');
      print('Response data: ${e.response?.data}');
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: errorMessage,
      ));
    } catch (e) {
      print('Unexpected error in LoadSquares: $e');
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: '${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e',
      ));
    }
  }

  Future<void> _onRefreshSquares(
    RefreshSquares event,
    Emitter<RastalarApiState> emit,
  ) async {
    emit(state.copyWith(isRefreshing: true));

    try {
      // Check network connectivity
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          isRefreshing: false,
          message: LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr(),
        ));
        return;
      }

      // Fetch squares from API
      final squares =
          await remoteDatasource.fetchSquares(blockId: event.blockId, pavilionId: event.pavilionId);

      // Group squares by seller
      final groupedSquares = remoteDatasource.groupSquaresBySeller(squares);

      emit(state.copyWith(
        status: RastalarApiStatus.success,
        groupedSquares: groupedSquares,
        isRefreshing: false,
        message: null,
      ));
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      print('DioException in RefreshSquares: $errorMessage');
      print('Response data: ${e.response?.data}');
      emit(state.copyWith(
        isRefreshing: false,
        message: errorMessage,
      ));
    } catch (e) {
      print('Unexpected error in RefreshSquares: $e');
      emit(state.copyWith(
        isRefreshing: false,
        message: '${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e',
      ));
    }
  }

  Future<void> _onUpdateSquareStatus(
    UpdateSquareStatus event,
    Emitter<RastalarApiState> emit,
  ) async {
    emit(state.copyWith(status: RastalarApiStatus.updating));

    try {
      // Check network connectivity
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          status: RastalarApiStatus.failure,
          message: LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr(),
        ));
        return;
      }

      // Update square status
      final success = await remoteDatasource.updateSquareStatus(
        squareId: event.squareId,
        paid: event.paid,
      );

      if (success) {
        // Refresh data after successful update
        add(const RefreshSquares());
      } else {
        emit(state.copyWith(
          status: RastalarApiStatus.failure,
          message: LocaleKeys.nazoratchi_tuzilma_errors_status_update_failed.tr(),
        ));
      }
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: '${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e',
      ));
    }
  }

  Future<void> _onSubmitFreePlaceReport(
    SubmitFreePlaceReport event,
    Emitter<RastalarApiState> emit,
  ) async {
    print('🎯 [BLOC] SubmitFreePlaceReport event received');
    print('📍 [BLOC] Place ID: ${event.placeId}');
    print('📝 [BLOC] Description: ${event.description}');
    print('🖼️ [BLOC] Image path: ${event.imagePath}');

    emit(state.copyWith(status: RastalarApiStatus.updating));
    print('🔄 [BLOC] State updated to updating');

    try {
      // Check network connectivity
      print('🌐 [BLOC] Checking network connectivity...');
      if (!await networkInfo.isConnected) {
        print('❌ [BLOC] No internet connection');
        emit(state.copyWith(
          status: RastalarApiStatus.failure,
          message: LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr(),
        ));
        return;
      }
      print('✅ [BLOC] Network connection available');

      // Submit free place report
      print('📤 [BLOC] Calling remoteDatasource.submitFreePlace...');
      final response = await remoteDatasource.submitFreePlace(
        placeId: event.placeId,
        description: event.description,
        imagePath: event.imagePath,
      );

      print('📥 [BLOC] Response received from datasource');
      print('✅ [BLOC] Response success: ${response.success}');
      print('📄 [BLOC] Response message: ${response.message}');

      if (response.success) {
        print('🎉 [BLOC] Success! Emitting success state');
        emit(state.copyWith(
          status: RastalarApiStatus.success,
          message: response.message ?? LocaleKeys.nazoratchi_tuzilma_success_empty_place_marked.tr(),
        ));

        // Note: Refresh will be handled by parent page via callback
        print('✅ [BLOC] Free place submission completed successfully');
      } else {
        print('❌ [BLOC] Failure! Emitting failure state');
        emit(state.copyWith(
          status: RastalarApiStatus.failure,
          message:
              response.message ?? LocaleKeys.nazoratchi_tuzilma_success_empty_place_mark_error.tr(),
        ));
      }
    } on DioException catch (e) {
      print('❌ [BLOC] DioException caught: ${e.message}');
      print('📊 [BLOC] DioException status: ${e.response?.statusCode}');
      print('📋 [BLOC] DioException data: ${e.response?.data}');
      final errorMessage = SimpleErrorHandler.handleError(e);
      print('🔧 [BLOC] Processed error message: $errorMessage');
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: errorMessage,
      ));
    } catch (e) {
      print('❌ [BLOC] Unexpected error caught: $e');
      print('🔍 [BLOC] Error type: ${e.runtimeType}');
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: '${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e',
      ));
    }
  }

  Future<void> _onSubmitEmptySquareReport(
    SubmitEmptySquareReport event,
    Emitter<RastalarApiState> emit,
  ) async {
    emit(state.copyWith(status: RastalarApiStatus.updating));

    try {
      // Check network connectivity
      if (!await networkInfo.isConnected) {
        emit(state.copyWith(
          status: RastalarApiStatus.failure,
          message: LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr(),
        ));
        return;
      }

      // Submit empty square report
      final success = await remoteDatasource.submitEmptySquareReport(
        squareId: event.squareId,
        description: event.description,
        imagePath: event.imagePath,
        selectedSubSquare: event.selectedSubSquare,
      );

      if (success) {
        // Refresh data after successful submission
        add(const RefreshSquares());
      } else {
        emit(state.copyWith(
          status: RastalarApiStatus.failure,
          message: LocaleKeys.nazoratchi_tuzilma_errors_report_not_sent.tr(),
        ));
      }
    } on DioException catch (e) {
      final errorMessage = SimpleErrorHandler.handleError(e);
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: RastalarApiStatus.failure,
        message: '${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e',
      ));
    }
  }

  // Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await networkInfo.isConnected;
  }
}
