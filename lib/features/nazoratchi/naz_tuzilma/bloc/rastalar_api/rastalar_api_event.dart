part of 'rastalar_api_bloc.dart';

sealed class RastalarApiEvent extends Equatable {
  const RastalarApiEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load squares from API
class LoadSquares extends RastalarApiEvent {
  final String? blockId;
  final String? pavilionId;

  const LoadSquares({this.blockId, this.pavilionId});

  @override
  List<Object?> get props => [blockId, pavilionId];
}

/// Event to refresh squares from API
class RefreshSquares extends RastalarApiEvent {
  final String? blockId;
  final String? pavilionId;

  const RefreshSquares({this.blockId, this.pavilionId});

  @override
  List<Object?> get props => [blockId, pavilionId];
}

/// Event to update square payment status
class UpdateSquareStatus extends RastalarApiEvent {
  final String squareId;
  final bool paid;

  const UpdateSquareStatus({
    required this.squareId,
    required this.paid,
  });

  @override
  List<Object?> get props => [squareId, paid];
}

/// Event to submit free place report
class SubmitFreePlaceReport extends RastalarA<PERSON>Event {
  final String placeId;
  final String description;
  final String imagePath;

  const SubmitFreePlaceReport({
    required this.placeId,
    required this.description,
    required this.imagePath,
  });

  @override
  List<Object?> get props => [placeId, description, imagePath];
}

/// Event to submit empty square report (legacy)
class SubmitEmptySquareReport extends RastalarApiEvent {
  final String squareId;
  final String description;
  final String? imagePath;
  final int? selectedSubSquare;

  const SubmitEmptySquareReport({
    required this.squareId,
    required this.description,
    this.imagePath,
    this.selectedSubSquare,
  });

  @override
  List<Object?> get props =>
      [squareId, description, imagePath, selectedSubSquare];
}
