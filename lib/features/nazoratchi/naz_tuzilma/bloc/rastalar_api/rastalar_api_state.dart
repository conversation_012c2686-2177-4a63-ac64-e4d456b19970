part of 'rastalar_api_bloc.dart';

enum RastalarApiStatus { initial, loading, success, failure, updating }

class RastalarApiState extends Equatable {
  final RastalarApiStatus status;
  final List<GroupedSquares> groupedSquares;
  final String? message;
  final bool isRefreshing;

  const RastalarApiState({
    this.status = RastalarApiStatus.initial,
    this.groupedSquares = const [],
    this.message,
    this.isRefreshing = false,
  });

  RastalarApiState copyWith({
    RastalarApiStatus? status,
    List<GroupedSquares>? groupedSquares,
    String? message,
    bool? isRefreshing,
  }) {
    return RastalarApiState(
      status: status ?? this.status,
      groupedSquares: groupedSquares ?? this.groupedSquares,
      message: message ?? this.message,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  /// Get total number of squares
  int get totalSquares {
    return groupedSquares.fold(0, (sum, group) => sum + group.squares.length);
  }

  /// Get number of paid squares
  int get paidSquares {
    return groupedSquares.where((group) => group.isPaid).fold(
      0, (sum, group) => sum + group.squares.length
    );
  }

  /// Get number of unpaid squares
  int get unpaidSquares {
    return groupedSquares.where((group) => !group.isPaid).fold(
      0, (sum, group) => sum + group.squares.length
    );
  }

  /// Check if data is loaded
  bool get hasData => groupedSquares.isNotEmpty;

  /// Check if loading
  bool get isLoading => status == RastalarApiStatus.loading;

  /// Check if error
  bool get hasError => status == RastalarApiStatus.failure;

  /// Check if updating
  bool get isUpdating => status == RastalarApiStatus.updating;

  @override
  List<Object?> get props => [status, groupedSquares, message, isRefreshing];
}
