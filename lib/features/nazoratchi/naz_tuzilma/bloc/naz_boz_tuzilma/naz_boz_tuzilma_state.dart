part of 'naz_boz_tuzilma_bloc.dart';

enum NazBozTuzilmaStatus { initial, loading, success, failure }

/// Error type enum for better error handling
enum NazBozTuzilmaErrorType { network, api, unknown }

class NazBozTuzilmaState extends Equatable {
  final NazBozTuzilmaStatus status;
  final List<PavilionResponse> pavilions;
  final String? message;
  final NazBozTuzilmaErrorType? errorType;
  final bool isRefreshing;
  final bool hasRefreshError;

  const NazBozTuzilmaState({
    this.status = NazBozTuzilmaStatus.initial,
    this.pavilions = const [],
    this.message,
    this.errorType,
    this.isRefreshing = false,
    this.hasRefreshError = false,
  });

  NazBozTuzilmaState copyWith({
    NazBozTuzilmaStatus? status,
    List<PavilionResponse>? pavilions,
    String? message,
    NazBozTuzilmaErrorType? errorType,
    bool? isRefreshing,
    bool? hasRefreshError,
  }) {
    return NazBozTuzilmaState(
      status: status ?? this.status,
      pavilions: pavilions ?? this.pavilions,
      message: message ?? this.message,
      errorType: errorType ?? this.errorType,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasRefreshError: hasRefreshError ?? this.hasRefreshError,
    );
  }

  /// Check if there are any pavilions loaded
  bool get hasAnyPavilions => pavilions.isNotEmpty;

  /// Check if state is loading
  bool get isLoading => status == NazBozTuzilmaStatus.loading;

  /// Check if state is success
  bool get isSuccess => status == NazBozTuzilmaStatus.success;

  /// Check if state is failure
  bool get isFailure => status == NazBozTuzilmaStatus.failure;

  /// Check if state is initial
  bool get isInitial => status == NazBozTuzilmaStatus.initial;

  /// Check if error is network related
  bool get isNetworkError => isFailure && errorType == NazBozTuzilmaErrorType.network;

  /// Check if error is API related
  bool get isApiError => isFailure && errorType == NazBozTuzilmaErrorType.api;

  @override
  List<Object?> get props => [
        status,
        pavilions,
        message,
        errorType,
        isRefreshing,
        hasRefreshError,
      ];

  @override
  String toString() {
    return 'NazBozTuzilmaState(status: $status, message: $message, isRefreshing: $isRefreshing, hasRefreshError: $hasRefreshError, pavilionsCount: ${pavilions.length})';
  }
}
