import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:equatable/equatable.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import '../../datasources/naz_boz_tuzilma_remote_datasource.dart';
import '../../models/pavilion_model.dart';

part 'naz_boz_tuzilma_event.dart';

part 'naz_boz_tuzilma_state.dart';

/// BLoC for managing naz boz tuzilma state
class NazBozTuzilmaBloc extends Bloc<NazBozTuzilmaEvent, NazBozTuzilmaState>
    with ErrorHandlerMixin<NazBozTuzilmaEvent, NazBozTuzilmaState> {
  final NazBozTuzilmaRemoteDatasource _remoteDatasource;
  final NetworkInfo _networkInfo;

  NazBozTuzilmaBloc({
    required NazBozTuzilmaRemoteDatasource remoteDatasource,
    required NetworkInfo networkInfo,
  })  : _remoteDatasource = remoteDatasource,
        _networkInfo = networkInfo,
        super(const NazBozTuzilmaState()) {
    on<LoadPavilions>(_onLoadPavilions);
    on<RefreshPavilions>(_onRefreshPavilions);
  }

  /// Handle load pavilions event
  Future<void> _onLoadPavilions(
    LoadPavilions event,
    Emitter<NazBozTuzilmaState> emit,
  ) async {
    await executeApiCall<List<PavilionResponse>>(
      apiCall: () => _remoteDatasource.getPavilions(),
      onLoading: () => emit(state.copyWith(
        status: NazBozTuzilmaStatus.loading,
        errorType: null,
        isRefreshing: false,
        hasRefreshError: false,
      )),
      onSuccess: (pavilions) {
        emit(state.copyWith(
          status: NazBozTuzilmaStatus.success,
          pavilions: pavilions,
          message: null,
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: NazBozTuzilmaStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        hasRefreshError: false,
      )),
    );
  }

  /// Handle refresh pavilions event
  Future<void> _onRefreshPavilions(
    RefreshPavilions event,
    Emitter<NazBozTuzilmaState> emit,
  ) async {
    // Set refreshing flag and keep existing data
    emit(state.copyWith(
      isRefreshing: true,
      errorType: null,
      hasRefreshError: false,
    ));

    // Check network connectivity first for refresh operations
    if (!await isNetworkConnected()) {
      // If no network during refresh, emit network error but keep existing data
      emit(state.copyWith(
        status: state.hasAnyPavilions ? NazBozTuzilmaStatus.success : NazBozTuzilmaStatus.failure,
        message: LocaleKeys.nazoratchi_tuzilma_errors_network_connection.tr(),
        errorType: NazBozTuzilmaErrorType.network,
        isRefreshing: false,
        hasRefreshError: true,
      ));
      return;
    }

    // Attempt to load fresh data
    await executeApiCall<List<PavilionResponse>>(
      apiCall: () => _remoteDatasource.getPavilions(),
      onLoading: () {
        // Don't change status to loading during refresh to keep existing content visible
      },
      onSuccess: (pavilions) {
        emit(state.copyWith(
          status: NazBozTuzilmaStatus.success,
          pavilions: pavilions,
          message: null,
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) {
        // Keep existing data and just set error info for toast
        emit(state.copyWith(
          status: state.hasAnyPavilions ? NazBozTuzilmaStatus.success : NazBozTuzilmaStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isRefreshing: false,
          hasRefreshError: true,
        ));
      },
    );
  }

  /// Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await _networkInfo.isConnected;
  }

  /// Determine error type based on error message
  NazBozTuzilmaErrorType _determineErrorType(String message) {
    final networkConnectionText = LocaleKeys.nazoratchi_tuzilma_errors_network_connection.tr();
    final networkUnavailableText = LocaleKeys.nazoratchi_tuzilma_errors_network_unavailable.tr();

    if (message.contains(networkConnectionText) ||
        message.contains(networkUnavailableText) ||
        message.contains('connection') ||
        message.contains('network')) {
      return NazBozTuzilmaErrorType.network;
    }
    return NazBozTuzilmaErrorType.api;
  }
}
