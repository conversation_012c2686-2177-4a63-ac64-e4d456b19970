import 'dart:async';
import 'package:click_bazaar/core/theme/app_colors.dart';
import 'package:flutter/services.dart';
import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/core/widgets/circle_button.dart';
import 'package:click_bazaar/features/nazoratchi/naz_tuzilma/widgets/rasta_card_item.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import 'package:easy_localization/easy_localization.dart';
import '../../../../core/widgets/universal_loading.dart';
import '../../../../di/dependency_injection.dart' as di;
import '../../../../translations/locale_keys.g.dart';
import '../bloc/naz_boz_tuzilma/naz_boz_tuzilma_bloc.dart';
import '../models/pavilion_model.dart';
import 'naz_pavilion_page.dart';

class NazBozTuzilma extends StatefulWidget {
  const NazBozTuzilma({super.key});

  @override
  State<NazBozTuzilma> createState() => _NazBozTuzilmaState();
}

class _NazBozTuzilmaState extends State<NazBozTuzilma> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          di.di<NazBozTuzilmaBloc>()..add(const LoadPavilions()),
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          elevation: 0,
          centerTitle: false,
          title: Text(
            LocaleKeys.market_structure_title.tr(),
            style: AppTextStyles.titleLarge.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 8),
              child: CircleButton(
                iconHeight: 24,
                iconWidth: 24,
                onPressed: () {},
                svgPath: Assets.iconsBell,
              ),
            )
          ],
        ),
        body: Column(
          children: [
            Expanded(
              child: BlocConsumer<NazBozTuzilmaBloc, NazBozTuzilmaState>(
                listener: (context, state) {
                  // Handle refresh errors with toast messages
                  // Show toast when there's a refresh error and existing data
                  if (state.hasRefreshError && state.hasAnyPavilions && state.message != null) {
                    // This is a refresh error - show toast while keeping existing content
                    final message = state.isNetworkError
                        ? LocaleKeys.common_no_internet_connection.tr()
                        : state.message!;

                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(message),
                        backgroundColor: Theme.of(context).colorScheme.error,
                        duration: const Duration(seconds: 3),
                        action: state.isNetworkError ? SnackBarAction(
                          label: LocaleKeys.common_retry.tr(),
                          textColor: Theme.of(context).colorScheme.onError,
                          onPressed: () {
                            context.read<NazBozTuzilmaBloc>().add(
                              const RefreshPavilions(),
                            );
                          },
                        ) : null,
                      ),
                    );
                  }
                },
                builder: (context, state) {
                  return RefreshIndicator(
                    onRefresh: () => _handleRefresh(context),
                    color: Theme.of(context).colorScheme.primary,
                    backgroundColor: Theme.of(context).colorScheme.surface,
                    strokeWidth: 2.0,
                    displacement: 40.0,
                    child: _buildContent(context, state),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Handle refresh action
  Future<void> _handleRefresh(BuildContext context) async {
    // Provide haptic feedback for better user experience
    HapticFeedback.lightImpact();

    // Add a small delay to show the refresh indicator
    await Future.delayed(const Duration(milliseconds: 300));
    context.read<NazBozTuzilmaBloc>().add(const RefreshPavilions());
  }

  Widget _buildContent(BuildContext context, NazBozTuzilmaState state) {
    if (state.isLoading || state.isInitial||state.isRefreshing) {
      return _buildShimmerLoading();
    }

    // Handle initial load errors - show centered error widget only if no existing data
    if (state.isFailure && !state.hasAnyPavilions) {
      return ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        children: [
          Center(
            child: SizedBox(
              height: MediaQuery.of(context).size.height * 0.9,
              child: Center(
                child: UniversalLoading.error(
                  message: state.isNetworkError
                      ? LocaleKeys.common_no_internet_connection.tr()
                      : (state.message ?? LocaleKeys.common_error_occurred.tr()),
                  onRetry: () {
                    context
                        .read<NazBozTuzilmaBloc>()
                        .add(const LoadPavilions());
                  },
                  retryButtonText: LocaleKeys.common_retry.tr(),
                  icon: state.isNetworkError ? Icons.wifi_off : null,
                ),
              ),
            )
          ),
          SizedBox(height: MediaQuery.of(context).size.height * 0.3),
        ],
      );
    }

    return _buildRastaList(context, state.pavilions);
  }

  Widget _buildRastaList(BuildContext context, List<PavilionResponse> pavilions) {
    if (pavilions.isEmpty) {
      return ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        children: [
          SizedBox(height: MediaQuery.of(context).size.height * 0.4),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.domain_outlined,
                  size: 64.w,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                Gap(16.h),
                Text(
                  LocaleKeys.market_structure_no_pavilions.tr(),
                  style: AppTextStyles.titleMedium.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                Gap(8.h),
                Text(
                  LocaleKeys.market_structure_no_pavilions_subtitle.tr(),
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16,vertical: 20),
      itemCount: pavilions.length,
      physics: const AlwaysScrollableScrollPhysics(
        parent: BouncingScrollPhysics(),
      ), // Enable refresh even with few items
      itemBuilder: (context, index) {
        final pavilionResponse = pavilions[index];
        final pavilion = pavilionResponse.pavilion;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: RastaCardItem(
            onTap: () {
              // Handle pavilion tap
              _onPavilionTap(context, pavilionResponse);
            },
            pavilionResponse: pavilionResponse,
          ),
        );
      },
    );
  }

  void _onPavilionTap(BuildContext context, PavilionResponse pavilionResponse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NazPavilionPage(
          pavilionId: pavilionResponse.pavilion.id,
          pavilionTitle: pavilionResponse.pavilion.title,
        ),
      ),
    );
  }

  /// Extract pavilion number from title (e.g., "1-pavillion" -> "1")
  String _extractPavilionNumber(String title) {
    final match = RegExp(r'(\d+)').firstMatch(title);
    return match?.group(1) ?? '0';
  }

  /// Build shimmer loading skeleton
  Widget _buildShimmerLoading() {
    return UniversalLoading.shimmer(
      child: ListView.builder(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: 6, // Show 6 skeleton cards
        itemBuilder: (context, index) {
          return _buildShimmerCard();
        },
      ),
    );
  }

  /// Build individual shimmer card skeleton
  Widget _buildShimmerCard() {
    // Add slight variation to make it look more realistic
    final random = DateTime.now().millisecondsSinceEpoch % 3;
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Pavilion title skeleton
                    Container(
                      height: 20.h,
                      width: (120 + random * 20).w, // Vary width: 120, 140, 160
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    Gap(8.h),
                    // Subtitle skeleton (if needed)
                    Container(
                      height: 14.h,
                      width: (80 + random * 15).w, // Vary width: 80, 95, 110
                      decoration: BoxDecoration(
                        color: AppColors.white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Gap(12.h),
          // Status row section
          Row(
            children: [
              // Bloklar section
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 12.h,
                      width: 50.w,
                      decoration: BoxDecoration(
                        color: AppColors.shimmerBase,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    Gap(4.h),
                    Container(
                      height: 16.h,
                      width: (30 + random * 10).w, // Vary width: 30, 40, 50
                      decoration: BoxDecoration(
                        color: AppColors.shimmerBase,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ),
              // Rastakar section
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 12.h,
                      width: 60.w,
                      decoration: BoxDecoration(
                        color: AppColors.shimmerBase,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                    Gap(4.h),
                    Container(
                      height: 16.h,
                      width: 40.w,
                      decoration: BoxDecoration(
                        color: AppColors.shimmerBase,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
