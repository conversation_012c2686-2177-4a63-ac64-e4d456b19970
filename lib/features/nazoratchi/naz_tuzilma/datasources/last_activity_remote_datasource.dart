import 'package:dio/dio.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import '../models/last_activity_model.dart';

/// Remote datasource for Last Activity management
class LastActivityRemoteDatasourceImpl {
  final Dio _dio;

  LastActivityRemoteDatasourceImpl({required Dio dio}) : _dio = dio;

  /// Fetch last activity records from API
  Future<LastActivityResponse> fetchLastActivity({
    required String blockId,
  }) async {
    try {
      print('🚀 [LAST_ACTIVITY_API] Starting fetchLastActivity request');
      print('📍 [LAST_ACTIVITY_API] Block ID: $blockId');

      // Build URL with query parameters
      final queryParams = <String, dynamic>{
        'block': blockId,
      };

      final url = '$API_BASE_URL/mobile/log/last-log';
      print('🌐 [LAST_ACTIVITY_API] API URL: $url');

      final response = await _dio.get(
        url,
        queryParameters: queryParams,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      print('📥 [LAST_ACTIVITY_API] Response received');
      print('📊 [LAST_ACTIVITY_API] Status Code: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (response.data == null) {
          print('⚠️ [LAST_ACTIVITY_API] Response data is null');
          return LastActivityResponse.empty();
        }

        try {
          // Handle single object response
          if (response.data is Map<String, dynamic>) {
            print('✅ [LAST_ACTIVITY_API] Processing object response');
            final data = response.data as Map<String, dynamic>;

            // Check if message exists and is not null
            if (data['message'] != null && data['message'].toString().isNotEmpty) {
              return LastActivityResponse.fromJson(data);
            } else {
              print('⚠️ [LAST_ACTIVITY_API] No message in response');
              return LastActivityResponse.empty();
            }
          } else {
            print('❌ [LAST_ACTIVITY_API] Unknown response format');
            return LastActivityResponse.empty();
          }
        } catch (parseError) {
          print('❌ [LAST_ACTIVITY_API] Error parsing response: $parseError');
          return LastActivityResponse.error('Parse error: $parseError');
        }
      } else if (response.statusCode == 204) {
        print('✅ [LAST_ACTIVITY_API] No content (204) - no activity to show');
        return LastActivityResponse.empty();
      } else {
        print('❌ [LAST_ACTIVITY_API] Non-success status code: ${response.statusCode}');
        return LastActivityResponse.error('API error (Status: ${response.statusCode})');
      }
    } on DioException catch (e) {
      print('❌ [LAST_ACTIVITY_API] DioException caught');
      final errorMessage = SimpleErrorHandler.handleError(e);
      return LastActivityResponse.error(errorMessage);
    } catch (e) {
      print('❌ [LAST_ACTIVITY_API] Unexpected error caught: $e');
      return LastActivityResponse.error('Unexpected error: $e');
    }
  }

  /// Get the most recent activity for display in the widget
  Future<ActivityRecord?> getLatestActivity({required String blockId}) async {
    try {
      final response = await fetchLastActivity(blockId: blockId);

      if (response.success && response.data != null) {
        return response.data;
      }

      return null;
    } catch (e) {
      print('❌ [LAST_ACTIVITY_API] Error getting latest activity: $e');
      return null;
    }
  }
}
