import 'package:dio/dio.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import '../models/last_activity_model.dart';

/// Remote datasource for Last Activity management
class LastActivityRemoteDatasourceImpl {
  final Dio _dio;

  LastActivityRemoteDatasourceImpl({required Dio dio}) : _dio = dio;

  /// Fetch last activity records from API
  Future<LastActivityResponse> fetchLastActivity({
    required String blockId,
    int? limit,
  }) async {
    try {
      print('🚀 [LAST_ACTIVITY_API] Starting fetchLastActivity request');
      print('📍 [LAST_ACTIVITY_API] Block ID: $blockId');

      // Build URL with query parameters
      final queryParams = <String, dynamic>{
        'block': blockId,
      };
      
      if (limit != null) {
        queryParams['limit'] = limit;
      }

      final url = '$API_BASE_URL/web/record-logs';
      print('🌐 [LAST_ACTIVITY_API] API URL: $url');

      final response = await _dio.get(
        url,
        queryParameters: queryParams,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
      );

      print('📥 [LAST_ACTIVITY_API] Response received');
      print('📊 [LAST_ACTIVITY_API] Status Code: ${response.statusCode}');

      if (response.statusCode == 200) {
        if (response.data == null) {
          print('⚠️ [LAST_ACTIVITY_API] Response data is null');
          return LastActivityResponse.error('No data received');
        }

        try {
          // Handle different response formats
          if (response.data is List) {
            // Direct array response
            print('✅ [LAST_ACTIVITY_API] Processing array response');
            final activities = (response.data as List)
                .map((item) => ActivityRecord.fromJson(item))
                .toList();
            
            return LastActivityResponse(
              success: true,
              data: activities,
            );
          } else if (response.data is Map<String, dynamic>) {
            // Object response with data field
            print('✅ [LAST_ACTIVITY_API] Processing object response');
            return LastActivityResponse.fromJson(response.data);
          } else {
            print('❌ [LAST_ACTIVITY_API] Unknown response format');
            return LastActivityResponse.error('Invalid response format');
          }
        } catch (parseError) {
          print('❌ [LAST_ACTIVITY_API] Error parsing response: $parseError');
          return LastActivityResponse.error('Parse error: $parseError');
        }
      } else {
        print('❌ [LAST_ACTIVITY_API] Non-success status code: ${response.statusCode}');
        return LastActivityResponse.error('API error (Status: ${response.statusCode})');
      }
    } on DioException catch (e) {
      print('❌ [LAST_ACTIVITY_API] DioException caught');
      final errorMessage = SimpleErrorHandler.handleError(e);
      return LastActivityResponse.error(errorMessage);
    } catch (e) {
      print('❌ [LAST_ACTIVITY_API] Unexpected error caught: $e');
      return LastActivityResponse.error('Unexpected error: $e');
    }
  }

  /// Get the most recent activity for display in the widget
  Future<ActivityRecord?> getLatestActivity({required String blockId}) async {
    try {
      final response = await fetchLastActivity(blockId: blockId, limit: 1);
      
      if (response.success && response.data.isNotEmpty) {
        return response.data.first;
      }
      
      return null;
    } catch (e) {
      print('❌ [LAST_ACTIVITY_API] Error getting latest activity: $e');
      return null;
    }
  }

  /// Create a mock activity record for demo purposes
  ActivityRecord createMockActivity({required String blockId}) {
    return ActivityRecord(
      id: 'mock_${DateTime.now().millisecondsSinceEpoch}',
      blockId: blockId,
      action: 'payment',
      description: 'To\'lov qabul qilindi',
      count: 21,
      timestamp: DateTime.now().subtract(const Duration(minutes: 15)),
      userId: 'mock_user_id',
      userName: 'Nazoratchi',
    );
  }
}
