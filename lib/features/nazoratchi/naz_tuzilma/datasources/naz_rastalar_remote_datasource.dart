import 'dart:io';
import 'package:dio/dio.dart';
import 'package:click_bazaar/core/utils/app_constants.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/core/services/simple_error_handler.dart';
import 'package:click_bazaar/core/function/functions.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import '../models/rastalar_model.dart';
import '../models/free_place_model.dart';

/// Remote datasource for Rastalar (Squares) management
class NazRastalarRemoteDatasourceImpl {
  final Dio _dio;

  NazRastalarRemoteDatasourceImpl({required Dio dio}) : _dio = dio;

  /// Fetch squares from API
  Future<List<ApiSquare>> fetchSquares({required String? blockId, required String? pavilionId}) async {
    try {
      // Build URL dynamically based on blockId
      String url;
      if (blockId != null && blockId.isNotEmpty) {
        // Use dynamic endpoint with the provided blockId
        url =
        '$API_BASE_URL/mobile/place?pavilion=$pavilionId&block=$blockId';
        print('Fetching squares for blockId: $blockId');
      } else {
        // Fallback to the hardcoded endpoint if no blockId provided
        url = '$API_BASE_URL$RASTALAR_HARD_ENDPOINT';
        print(
            'Fetching squares using hardcoded endpoint (no blockId specified)');
      }
      print('API URL: $url');

      final response = await _dio.get(
        url,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data;

        // Parse each square with error handling
        final List<ApiSquare> squares = [];
        for (int i = 0; i < data.length; i++) {
          try {
            final json = data[i] as Map<String, dynamic>;

            // Handle null payment field explicitly
            if (json['payment'] == null) {
              json['payment'] = false; // Default to false if null
              print(
                  'Warning: Square at index $i has null payment field, defaulting to false');
            }

            squares.add(ApiSquare.fromJson(json));
          } catch (e) {
            print('Error parsing square at index $i: $e');
            print('Square data: ${data[i]}');
            // Continue processing other squares instead of failing completely
            continue;
          }
        }

        return squares;
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: 'Failed to fetch squares: ${response.statusCode}',
        );
      }
    } on DioException catch (e) {
      print('DioException in fetchSquares: ${e.message}');
      throw DioException(
        requestOptions: e.requestOptions,
        response: e.response,
        message: SimpleErrorHandler.handleError(e),
      );
    } catch (e) {
      print('Unexpected error in fetchSquares: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  /// Group squares by seller ID
  List<GroupedSquares> groupSquaresBySeller(List<ApiSquare> squares) {
    final Map<String, List<ApiSquare>> groupedMap = {};
    int nullSellerCounter = 0; // Counter for null sellers to create unique keys

    // Group squares by seller ID, handling null sellers separately
    for (final square in squares) {
      String sellerId;

      if (square.seller == null) {
        // Create unique key for each null seller to prevent grouping
        sellerId = 'null_seller_${nullSellerCounter++}';
      } else {
        sellerId = square.seller!.id;
      }

      if (!groupedMap.containsKey(sellerId)) {
        groupedMap[sellerId] = [];
      }
      groupedMap[sellerId]!.add(square);
    }

    // Convert to GroupedSquares objects
    return groupedMap.entries.map((entry) {
      final squares = entry.value;
      final totalPrice = squares.fold(0, (sum, square) => sum + square.price);

      // Determine payment status based on API status codes
      // Status 4 = to'langan (paid), others are considered unpaid/debt
      final isPaid = squares.every((square) => square.status == 4);
      final tariffTitle = squares.first.tariff.title;

      // Use original seller ID (null if it was null, otherwise the actual seller ID)
      String? originalSellerId;
      if (entry.key.startsWith('null_seller_')) {
        originalSellerId = null; // Keep as null for display purposes
      } else {
        originalSellerId = entry.key;
      }

      return GroupedSquares(
        sellerId: originalSellerId,
        squares: squares,
        totalPrice: totalPrice,
        isPaid: isPaid,
        tariffTitle: tariffTitle,
      );
    }).toList();
  }

  /// Update square status (for future implementation)
  Future<bool> updateSquareStatus({
    required String squareId,
    required bool paid,
  }) async {
    try {
      // TODO: Implement when backend endpoint is available
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      return true;
    } catch (e) {
      throw Exception('Failed to update square status: $e');
    }
  }

  /// Submit free place report to API
  Future<FreePlaceResponse> submitFreePlace({
    required String placeId,
    required String description,
    required String imagePath,
  }) async {
    print('🚀 [FREE_PLACE_API] Starting submitFreePlace request');
    print('📍 [FREE_PLACE_API] Place ID: $placeId');
    print('📝 [FREE_PLACE_API] Description: $description');
    print('🖼️ [FREE_PLACE_API] Image path: $imagePath');

    try {
      // Validate image file exists
      final imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        print('❌ [FREE_PLACE_API] Image file does not exist: $imagePath');
        return FreePlaceResponse.error(LocaleKeys.nazoratchi_tuzilma_errors_image_file_not_found.tr());
      }

      final fileSize = await imageFile.length();
      print('📏 [FREE_PLACE_API] Image file size: ${fileSize} bytes');

      // Create FormData for multipart file upload
      print('🔧 [FREE_PLACE_API] Creating FormData...');
      final filename = imagePath.split('/').last;
      final contentType = getMediaType(imagePath);
      print('📄 [FREE_PLACE_API] Filename: $filename');
      print('🏷️ [FREE_PLACE_API] Content-Type: $contentType');

      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          imagePath,
          filename: filename,
          contentType: contentType,
        ),
        'place': placeId,
        'desc': description,
      });

      print('✅ [FREE_PLACE_API] FormData created successfully');
      print('🌐 [FREE_PLACE_API] API Endpoint: ${ApiPath.freePlacePath}');
      print('🔧 [FREE_PLACE_API] Dio base URL: ${_dio.options.baseUrl}');
      print('📤 [FREE_PLACE_API] Making POST request...');

      final response = await _dio.post(
        ApiPath.freePlacePath,
        data: formData,
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
            'Accept': 'application/json',
          },
        ),
      );

      print('📥 [FREE_PLACE_API] Response received');
      print('📊 [FREE_PLACE_API] Status Code: ${response.statusCode}');
      print('📋 [FREE_PLACE_API] Response Headers: ${response.headers}');
      print('📄 [FREE_PLACE_API] Response Data: ${response.data}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('✅ [FREE_PLACE_API] Success response received');

        if (response.data == null) {
          print('⚠️ [FREE_PLACE_API] Response data is null, assuming success');
          return FreePlaceResponse.success(
            message: LocaleKeys.nazoratchi_tuzilma_success_empty_place_marked.tr(),
          );
        }

        try {
          // Handle different response formats
          Map<String, dynamic> responseData;

          if (response.data is Map<String, dynamic>) {
            responseData = response.data as Map<String, dynamic>;
          } else if (response.data is String) {
            print('⚠️ [FREE_PLACE_API] Response is string, assuming success');
            return FreePlaceResponse.success(
              message: response.data as String,
            );
          } else {
            print(
                '⚠️ [FREE_PLACE_API] Unknown response format: ${response.data.runtimeType}');
            return FreePlaceResponse.success(
              message: LocaleKeys.nazoratchi_tuzilma_success_empty_place_marked.tr(),
            );
          }

          // Check if the response indicates success
          final message = responseData['message'] as String?;
          print('📄 [FREE_PLACE_API] Response message: $message');

          if (message != null && message.toLowerCase() == 'success') {
            print('✅ [FREE_PLACE_API] API returned success message');
            return FreePlaceResponse.success(
              message: LocaleKeys.nazoratchi_tuzilma_success_empty_place_marked.tr(),
              data: responseData,
            );
          } else if (responseData.containsKey('success')) {
            // Fallback: try to parse as our expected format
            final result = FreePlaceResponse.fromJson(responseData);
            print(
                '✅ [FREE_PLACE_API] Response parsed with success field: ${result.success}');
            return result;
          } else {
            // If we got 200/201 but unclear message, assume success
            print(
                '⚠️ [FREE_PLACE_API] Unclear response, but status code indicates success');
            return FreePlaceResponse.success(
              message: message ?? LocaleKeys.nazoratchi_tuzilma_success_empty_place_marked.tr(),
              data: responseData,
            );
          }
        } catch (parseError) {
          print('❌ [FREE_PLACE_API] Error parsing response: $parseError');
          print('🔍 [FREE_PLACE_API] Raw response data: ${response.data}');
          // Even if parsing fails, if we got 200/201, consider it success
          return FreePlaceResponse.success(
            message: LocaleKeys.nazoratchi_tuzilma_success_empty_place_marked.tr(),
          );
        }
      } else {
        print(
            '❌ [FREE_PLACE_API] Non-success status code: ${response.statusCode}');
        return FreePlaceResponse.error(
          '${LocaleKeys.nazoratchi_tuzilma_success_empty_place_mark_error.tr()} (Status: ${response.statusCode})',
        );
      }
    } on DioException catch (e) {
      print('❌ [FREE_PLACE_API] DioException caught');
      print('🔍 [FREE_PLACE_API] DioException type: ${e.type}');
      print('📄 [FREE_PLACE_API] DioException message: ${e.message}');
      print('📊 [FREE_PLACE_API] Response status: ${e.response?.statusCode}');
      print('📋 [FREE_PLACE_API] Response data: ${e.response?.data}');
      print('🌐 [FREE_PLACE_API] Request path: ${e.requestOptions.path}');
      print('📤 [FREE_PLACE_API] Request headers: ${e.requestOptions.headers}');

      final errorMessage = SimpleErrorHandler.handleError(e);
      print('🔧 [FREE_PLACE_API] Processed error message: $errorMessage');
      return FreePlaceResponse.error(errorMessage);
    } catch (e) {
      print('❌ [FREE_PLACE_API] Unexpected error caught: $e');
      print('🔍 [FREE_PLACE_API] Error type: ${e.runtimeType}');
      print('📚 [FREE_PLACE_API] Stack trace: ${StackTrace.current}');
      return FreePlaceResponse.error('${LocaleKeys.nazoratchi_tuzilma_errors_unexpected_error.tr()}: $e');
    }
  }

  /// Submit empty square report (legacy method - kept for backward compatibility)
  Future<bool> submitEmptySquareReport({
    required String squareId,
    required String description,
    String? imagePath,
    int? selectedSubSquare,
  }) async {
    try {
      if (imagePath != null) {
        // Use the new API for actual implementation
        final response = await submitFreePlace(
          placeId: squareId,
          description: description,
          imagePath: imagePath,
        );
        return response.success;
      } else {
        // If no image, return false as image is required
        return false;
      }
    } catch (e) {
      throw Exception('Failed to submit empty square report: $e');
    }
  }
}
