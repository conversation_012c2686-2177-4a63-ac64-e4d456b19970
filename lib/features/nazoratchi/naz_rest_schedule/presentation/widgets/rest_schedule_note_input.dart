import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gap/gap.dart';
import '../../../../../core/theme/app_colors.dart';
import '../../../../../core/theme/app_text_styles.dart';

class RestScheduleNoteInput extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final String hintText;

  const RestScheduleNoteInput({
    super.key,
    required this.controller,
    this.label = 'Izoh yozing',
    this.hintText = 'Izoh yozing...',
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.cTextGrayColor,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Container(
          width: double.infinity,
          height: 120.h,
          decoration: BoxDecoration(
            color: AppColors.cCardsColor,
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: AppColors.cGrayBorderColor,
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontSize: 14.sp,
            ),
            decoration: InputDecoration(
              hintText: hintText,
              hintStyle: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.cTextGrayColor,
                fontSize: 14.sp,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16.w),
            ),
          ),
        ),
      ],
    );
  }
}
