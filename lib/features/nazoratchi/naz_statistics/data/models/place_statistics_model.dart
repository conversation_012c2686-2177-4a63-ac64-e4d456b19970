import 'package:equatable/equatable.dart';

/// Model for place statistics API response
class PlaceStatisticsModel extends Equatable {
  final int belgilanmaganCount; // Maps from "belgilanmagan"
  final int bosh;               // Maps from "free"
  final int tolanmagan;         // Maps from "qarzdor"
  final int tolangan;           // Maps from "tolangan"
  final int totalCount;         // Calculated field

  const PlaceStatisticsModel({
    required this.belgilanmaganCount,
    required this.bosh,
    required this.tolanmagan,
    required this.tolangan,
    required this.totalCount,
  });

  /// Create from JSON response
  factory PlaceStatisticsModel.fromJson(Map<String, dynamic> json) {
    final belgilanmagan = json['belgilanmagan'] ?? 0;
    final free = json['free'] ?? 0;
    final qarzdor = json['qarzdor'] ?? 0;
    final tolangan = json['tolangan'] ?? 0;
    
    // Calculate total count
    final total = belgilanmagan + free + qarzdor + tolangan;
    
    return PlaceStatisticsModel(
      belgilanmaganCount: belgilanmagan,
      bosh: free,
      tolanmagan: qarzdor,
      tolangan: tolangan,
      totalCount: total,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'belgilanmagan': belgilanmaganCount,
      'free': bosh,
      'qarzdor': tolanmagan,
      'tolangan': tolangan,
    };
  }

  /// Create empty instance
  factory PlaceStatisticsModel.empty() {
    return const PlaceStatisticsModel(
      belgilanmaganCount: 0,
      bosh: 0,
      tolanmagan: 0,
      tolangan: 0,
      totalCount: 0,
    );
  }

  /// Copy with method for state updates
  PlaceStatisticsModel copyWith({
    int? belgilanmaganCount,
    int? bosh,
    int? tolanmagan,
    int? tolangan,
    int? totalCount,
  }) {
    return PlaceStatisticsModel(
      belgilanmaganCount: belgilanmaganCount ?? this.belgilanmaganCount,
      bosh: bosh ?? this.bosh,
      tolanmagan: tolanmagan ?? this.tolanmagan,
      tolangan: tolangan ?? this.tolangan,
      totalCount: totalCount ?? this.totalCount,
    );
  }

  @override
  List<Object?> get props => [
        belgilanmaganCount,
        bosh,
        tolanmagan,
        tolangan,
        totalCount,
      ];

  @override
  String toString() {
    return 'PlaceStatisticsModel(belgilanmaganCount: $belgilanmaganCount, bosh: $bosh, tolanmagan: $tolanmagan, tolangan: $tolangan, totalCount: $totalCount)';
  }
}
