import 'package:equatable/equatable.dart';

/// Model for individual payment history item
class PaymentHistoryItem extends Equatable {
  final String id;
  final int price;
  final int title; // Place number

  const PaymentHistoryItem({
    required this.id,
    required this.price,
    required this.title,
  });

  /// Create from JSON response
  factory PaymentHistoryItem.fromJson(Map<String, dynamic> json) {
    return PaymentHistoryItem(
      id: json['_id'] ?? '',
      price: json['price'] ?? 0,
      title: json['title'] ?? 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'price': price,
      'title': title,
    };
  }

  @override
  List<Object?> get props => [id, price, title];

  @override
  String toString() {
    return 'PaymentHistoryItem(id: $id, price: $price, title: $title)';
  }
}

/// Model for paginated payment history response
class PaymentHistoryResponse extends Equatable {
  final List<PaymentHistoryItem> docs;
  final int totalDocs;
  final int limit;
  final int totalPages;
  final int page;
  final int pagingCounter;
  final bool hasPrevPage;
  final bool hasNextPage;
  final int? prevPage;
  final int? nextPage;

  const PaymentHistoryResponse({
    required this.docs,
    required this.totalDocs,
    required this.limit,
    required this.totalPages,
    required this.page,
    required this.pagingCounter,
    required this.hasPrevPage,
    required this.hasNextPage,
    this.prevPage,
    this.nextPage,
  });

  /// Create from JSON response
  factory PaymentHistoryResponse.fromJson(Map<String, dynamic> json) {
    final docsList = (json['docs'] as List<dynamic>?)
            ?.map((e) => PaymentHistoryItem.fromJson(e as Map<String, dynamic>))
            .toList() ??
        [];

    return PaymentHistoryResponse(
      docs: docsList,
      totalDocs: json['totalDocs'] ?? 0,
      limit: json['limit'] ?? 10,
      totalPages: json['totalPages'] ?? 0,
      page: json['page'] ?? 1,
      pagingCounter: json['pagingCounter'] ?? 1,
      hasPrevPage: json['hasPrevPage'] ?? false,
      hasNextPage: json['hasNextPage'] ?? false,
      prevPage: json['prevPage'],
      nextPage: json['nextPage'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'docs': docs.map((item) => item.toJson()).toList(),
      'totalDocs': totalDocs,
      'limit': limit,
      'totalPages': totalPages,
      'page': page,
      'pagingCounter': pagingCounter,
      'hasPrevPage': hasPrevPage,
      'hasNextPage': hasNextPage,
      'prevPage': prevPage,
      'nextPage': nextPage,
    };
  }

  /// Create empty instance
  factory PaymentHistoryResponse.empty() {
    return const PaymentHistoryResponse(
      docs: [],
      totalDocs: 0,
      limit: 10,
      totalPages: 0,
      page: 1,
      pagingCounter: 1,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    );
  }

  /// Copy with method for state updates
  PaymentHistoryResponse copyWith({
    List<PaymentHistoryItem>? docs,
    int? totalDocs,
    int? limit,
    int? totalPages,
    int? page,
    int? pagingCounter,
    bool? hasPrevPage,
    bool? hasNextPage,
    int? prevPage,
    int? nextPage,
  }) {
    return PaymentHistoryResponse(
      docs: docs ?? this.docs,
      totalDocs: totalDocs ?? this.totalDocs,
      limit: limit ?? this.limit,
      totalPages: totalPages ?? this.totalPages,
      page: page ?? this.page,
      pagingCounter: pagingCounter ?? this.pagingCounter,
      hasPrevPage: hasPrevPage ?? this.hasPrevPage,
      hasNextPage: hasNextPage ?? this.hasNextPage,
      prevPage: prevPage ?? this.prevPage,
      nextPage: nextPage ?? this.nextPage,
    );
  }

  /// Check if this is the first page
  bool get isFirstPage => page == 1;

  /// Check if this is the last page
  bool get isLastPage => page == totalPages;

  /// Get total amount from all items
  int get totalAmount => docs.fold(0, (sum, item) => sum + item.price);

  @override
  List<Object?> get props => [
        docs,
        totalDocs,
        limit,
        totalPages,
        page,
        pagingCounter,
        hasPrevPage,
        hasNextPage,
        prevPage,
        nextPage,
      ];

  @override
  String toString() {
    return 'PaymentHistoryResponse(docs: ${docs.length}, totalDocs: $totalDocs, page: $page, totalPages: $totalPages)';
  }
}
