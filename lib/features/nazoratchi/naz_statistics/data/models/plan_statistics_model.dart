import 'package:equatable/equatable.dart';

/// Model for plan statistics API response
class PlanStatisticsModel extends Equatable {
  final int rejda;           // Maps from "totalPlan"
  final int tushgan;         // Maps from "tushum"
  final int clickOrqali;     // Maps from "click"
  final int naqdPul;         // Maps from "naqt"
  final int terminalOrqali;  // Maps from "terminal"
  final int qarzdalik;       // Maps from "qarzdorlik"

  const PlanStatisticsModel({
    required this.rejda,
    required this.tushgan,
    required this.clickOrqali,
    required this.naqdPul,
    required this.terminalOrqali,
    required this.qarzdalik,
  });

  /// Create from JSON response
  factory PlanStatisticsModel.fromJson(Map<String, dynamic> json) {
    return PlanStatisticsModel(
      rejda: json['totalPlan'] ?? 0,
      tushgan: json['tushum'] ?? 0,
      clickOrqali: json['click'] ?? 0,
      naqdPul: json['naqt'] ?? 0,
      terminalOrqali: json['terminal'] ?? 0,
      qarzdalik: json['qarzdorlik'] ?? 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'totalPlan': rejda,
      'tushum': tushgan,
      'click': clickOrqali,
      'naqt': naqdPul,
      'terminal': terminalOrqali,
      'qarzdorlik': qarzdalik,
    };
  }

  /// Create empty instance
  factory PlanStatisticsModel.empty() {
    return const PlanStatisticsModel(
      rejda: 0,
      tushgan: 0,
      clickOrqali: 0,
      naqdPul: 0,
      terminalOrqali: 0,
      qarzdalik: 0,
    );
  }

  /// Copy with method for state updates
  PlanStatisticsModel copyWith({
    int? rejda,
    int? tushgan,
    int? clickOrqali,
    int? naqdPul,
    int? terminalOrqali,
    int? qarzdalik,
  }) {
    return PlanStatisticsModel(
      rejda: rejda ?? this.rejda,
      tushgan: tushgan ?? this.tushgan,
      clickOrqali: clickOrqali ?? this.clickOrqali,
      naqdPul: naqdPul ?? this.naqdPul,
      terminalOrqali: terminalOrqali ?? this.terminalOrqali,
      qarzdalik: qarzdalik ?? this.qarzdalik,
    );
  }

  @override
  List<Object?> get props => [
        rejda,
        tushgan,
        clickOrqali,
        naqdPul,
        terminalOrqali,
        qarzdalik,
      ];

  @override
  String toString() {
    return 'PlanStatisticsModel(rejda: $rejda, tushgan: $tushgan, clickOrqali: $clickOrqali, naqdPul: $naqdPul, terminalOrqali: $terminalOrqali, qarzdalik: $qarzdalik)';
  }
}
