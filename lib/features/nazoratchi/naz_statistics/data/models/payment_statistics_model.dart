import 'package:equatable/equatable.dart';

/// Model for payment statistics API response
class PaymentStatisticsModel extends Equatable {
  final int topshirilgan;      // Maps from "topshirilgan"
  final int topshirilmagan;    // Maps from "topshirilmagan"

  const PaymentStatisticsModel({
    required this.topshirilgan,
    required this.topshirilmagan,
  });

  /// Create from JSON response
  factory PaymentStatisticsModel.fromJson(Map<String, dynamic> json) {
    return PaymentStatisticsModel(
      topshirilgan: json['topshirilgan'] ?? 0,
      topshirilmagan: json['topshirilmagan'] ?? 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'topshirilgan': topshirilgan,
      'topshirilmagan': topshirilmagan,
    };
  }

  /// Create empty instance
  factory PaymentStatisticsModel.empty() {
    return const PaymentStatisticsModel(
      topshirilgan: 0,
      topshirilmagan: 0,
    );
  }

  /// Copy with method for state updates
  PaymentStatisticsModel copyWith({
    int? topshirilgan,
    int? topshirilmagan,
  }) {
    return PaymentStatisticsModel(
      topshirilgan: topshirilgan ?? this.topshirilgan,
      topshirilmagan: topshirilmagan ?? this.topshirilmagan,
    );
  }

  /// Calculate total
  int get total => topshirilgan + topshirilmagan;

  @override
  List<Object?> get props => [
        topshirilgan,
        topshirilmagan,
      ];

  @override
  String toString() {
    return 'PaymentStatisticsModel(topshirilgan: $topshirilgan, topshirilmagan: $topshirilmagan)';
  }
}
