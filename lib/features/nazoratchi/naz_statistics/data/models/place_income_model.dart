import 'package:equatable/equatable.dart';

/// Model for place income data
class PlaceIncomeModel extends Equatable {
  final String placeNumber;
  final int amount;

  const PlaceIncomeModel({
    required this.placeNumber,
    required this.amount,
  });

  /// Create from JSON response
  factory PlaceIncomeModel.fromJson(Map<String, dynamic> json) {
    return PlaceIncomeModel(
      placeNumber: json['placeNumber']?.toString() ?? '',
      amount: json['amount'] ?? 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'placeNumber': placeNumber,
      'amount': amount,
    };
  }

  /// Create empty instance
  factory PlaceIncomeModel.empty() {
    return const PlaceIncomeModel(
      placeNumber: '',
      amount: 0,
    );
  }

  /// Copy with method for state updates
  PlaceIncomeModel copyWith({
    String? placeNumber,
    int? amount,
  }) {
    return PlaceIncomeModel(
      placeNumber: placeNumber ?? this.placeNumber,
      amount: amount ?? this.amount,
    );
  }

  @override
  List<Object?> get props => [placeNumber, amount];

  @override
  String toString() {
    return 'PlaceIncomeModel(placeNumber: $placeNumber, amount: $amount)';
  }
}

/// Response model for place income list
class PlaceIncomeResponse extends Equatable {
  final List<PlaceIncomeModel> places;
  final int totalAmount;

  const PlaceIncomeResponse({
    required this.places,
    required this.totalAmount,
  });

  /// Create from JSON response
  factory PlaceIncomeResponse.fromJson(Map<String, dynamic> json) {
    final placesList = (json['places'] as List<dynamic>?)
            ?.map((e) => PlaceIncomeModel.fromJson(e as Map<String, dynamic>))
            .toList() ??
        [];

    return PlaceIncomeResponse(
      places: placesList,
      totalAmount: json['totalAmount'] ?? 0,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'places': places.map((place) => place.toJson()).toList(),
      'totalAmount': totalAmount,
    };
  }

  /// Create empty instance
  factory PlaceIncomeResponse.empty() {
    return const PlaceIncomeResponse(
      places: [],
      totalAmount: 0,
    );
  }

  @override
  List<Object?> get props => [places, totalAmount];

  @override
  String toString() {
    return 'PlaceIncomeResponse(places: ${places.length}, totalAmount: $totalAmount)';
  }
}
