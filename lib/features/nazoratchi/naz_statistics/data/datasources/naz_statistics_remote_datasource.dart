import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/core/utils/api_path.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import '../models/plan_statistics_model.dart';
import '../models/place_statistics_model.dart';
import '../models/payment_statistics_model.dart';

/// Remote datasource for statistics API calls
class NazStatisticsRemoteDatasourceImpl {
  final NetworkInfo networkInfo;
  final Dio dio;

  NazStatisticsRemoteDatasourceImpl({
    required this.networkInfo,
    required this.dio,
  });

  /// Fetch plan statistics for a specific date
  Future<PlanStatisticsModel> getPlanStatistics(String date) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.planStatisticsPath),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.errors_network_connection_error.tr(),
      );
    }

    try {
      final response = await dio.get(
        ApiPath.planStatisticsPath,
        queryParameters: {'date': date},
      );

      if (response.statusCode == 200 && response.data != null) {
        return PlanStatisticsModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.planStatisticsPath),
          type: DioExceptionType.badResponse,
          message: LocaleKeys.statistics_history_statistics_load_error.tr(),
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.planStatisticsPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Fetch place statistics for a specific date
  Future<PlaceStatisticsModel> getPlaceStatistics(String date) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.placeStatisticsPath),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.errors_network_connection_error.tr(),
      );
    }

    try {
      final response = await dio.get(
        ApiPath.placeStatisticsPath,
        queryParameters: {'date': date},
      );

      if (response.statusCode == 200 && response.data != null) {
        return PlaceStatisticsModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.placeStatisticsPath),
          type: DioExceptionType.badResponse,
          message: LocaleKeys.statistics_history_statistics_load_error.tr(),
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.placeStatisticsPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Fetch payment statistics (no date parameter needed)
  Future<PaymentStatisticsModel> getPaymentStatistics(String date) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.paymentStatisticsPath),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.errors_network_connection_error.tr(),
      );
    }

    try {
      final response = await dio
          .get(ApiPath.paymentStatisticsPath, queryParameters: {'date': date});

      if (response.statusCode == 200 && response.data != null) {
        return PaymentStatisticsModel.fromJson(response.data);
      } else {
        throw DioException(
          requestOptions: RequestOptions(path: ApiPath.paymentStatisticsPath),
          type: DioExceptionType.badResponse,
          message: LocaleKeys.statistics_history_statistics_load_error.tr(),
        );
      }
    } on DioException catch (e) {
      // Re-throw DioException to be handled by the error handler
      rethrow;
    } catch (e) {
      // Convert any other exception to DioException for consistent handling
      throw DioException(
        requestOptions: RequestOptions(path: ApiPath.paymentStatisticsPath),
        type: DioExceptionType.unknown,
        error: e,
      );
    }
  }

  /// Fetch all statistics for a specific date
  Future<Map<String, dynamic>> getAllStatistics(String date) async {
    // Check network connectivity first
    if (!await networkInfo.isConnected) {
      throw DioException(
        requestOptions: RequestOptions(path: 'statistics'),
        type: DioExceptionType.connectionError,
        message: LocaleKeys.errors_network_connection_error.tr(),
      );
    }

    try {
      // Fetch all statistics in parallel
      final results = await Future.wait([
        getPlanStatistics(date),
        getPlaceStatistics(date),
        getPaymentStatistics(date),
      ]);

      return {
        'planStatistics': results[0] as PlanStatisticsModel,
        'placeStatistics': results[1] as PlaceStatisticsModel,
        'paymentStatistics': results[2] as PaymentStatisticsModel,
      };
    } catch (e) {
      rethrow;
    }
  }
}
