# Naz Statistics Feature

This feature implements a complete statistics system for the na<PERSON><PERSON><PERSON> (supervisor) role with three API endpoints and full BLoC architecture following the auth feature patterns.

## Architecture Overview

The feature follows the established patterns from the auth feature:
- **Data Layer**: Remote datasources and models
- **Presentation Layer**: BLoC for state management
- **Error Handling**: Uses ErrorHandlerMixin for consistent error handling
- **Dependency Injection**: Registered in the main DI container

## API Endpoints

### 1. Plan Statistics API
- **Endpoint**: `{{apiUrl}}/mobile/report/plan?date=2025-07-12`
- **Method**: GET
- **Parameters**: `date` (YYYY-MM-DD format)
- **Response Mapping**:
  ```json
  {
    "totalPlan": 0,     // Maps to "rejda"
    "tushum": 0,        // Maps to "tushgan" 
    "click": 0,         // Maps to "clickOrqali"
    "naqt": 0,          // Maps to "naqdPul"
    "terminal": 0,      // Maps to "terminalOrqali"
    "qarzdorlik": 0     // Maps to "qarzdalik"
  }
  ```

### 2. Place Statistics API
- **Endpoint**: `{{apiUrl}}/mobile/report/place?date=2025-07-12`
- **Method**: GET
- **Parameters**: `date` (YYYY-MM-DD format)
- **Response Mapping**:
  ```json
  {
    "belgilanmagan": 0, // Maps to "belgilanmaganCount"
    "free": 8,          // Maps to "bosh"
    "qarzdor": 0,       // Maps to "tolanmagan" 
    "tolangan": 0       // Maps to "tolangan"
  }
  ```
- **Calculated Field**: `totalCount = belgilanmaganCount + bosh + tolanmagan + tolangan`

### 3. Payment Statistics API
- **Endpoint**: `{{apiUrl}}/mobile/report/payment`
- **Method**: GET
- **Parameters**: None
- **Response Mapping**:
  ```json
  {
    "topshirilgan": 0,    // Maps to "topshirilgan"
    "topshirilmagan": 0   // Maps to "topshirilmagan"
  }
  ```

## File Structure

```
lib/features/nazoratchi/naz_statistics/
├── data/
│   ├── datasources/
│   │   └── naz_statistics_remote_datasource.dart
│   └── models/
│       ├── plan_statistics_model.dart
│       ├── place_statistics_model.dart
│       └── payment_statistics_model.dart
├── presentation/
│   ├── bloc/
│   │   ├── naz_statistics_bloc.dart
│   │   ├── naz_statistics_event.dart
│   │   └── naz_statistics_state.dart
│   ├── pages/
│   │   ├── naz_statistics_page.dart (existing)
│   │   └── naz_statistics_page_with_bloc.dart (new with BLoC)
│   └── widgets/
│       └── (existing widgets)
└── README.md
```

## BLoC Implementation

### Events
- `LoadPlanStatisticsEvent`: Load plan statistics for a specific date
- `LoadPlaceStatisticsEvent`: Load place statistics for a specific date
- `LoadPaymentStatisticsEvent`: Load payment statistics
- `LoadAllStatisticsEvent`: Load all statistics for a specific date
- `RefreshAllStatisticsEvent`: Refresh all statistics for a specific date

### State
- **Status**: `initial`, `loading`, `success`, `failure`
- **Data**: Contains all three statistics models
- **Helper Methods**: `hasAllStatistics`, `isLoading`, `isSuccess`, etc.

### Error Handling
- Uses `ErrorHandlerMixin` for consistent error handling
- Network connectivity checks
- Proper DioException handling
- User-friendly error messages in Uzbek

## Usage Example

```dart
// In your page
BlocProvider(
  create: (context) => di<NazStatisticsBloc>()
    ..add(LoadAllStatisticsEvent(date: '2025-07-12')),
  child: BlocConsumer<NazStatisticsBloc, NazStatisticsState>(
    listener: (context, state) {
      if (state.isFailure) {
        // Show error message
      }
    },
    builder: (context, state) {
      if (state.isLoading) {
        return CircularProgressIndicator();
      }
      
      if (state.hasAllStatistics) {
        // Display statistics
        return StatisticsWidget(
          planStats: state.planStatistics!,
          placeStats: state.placeStatistics!,
          paymentStats: state.paymentStatistics!,
        );
      }
      
      return EmptyStateWidget();
    },
  ),
)
```

## Testing

Comprehensive tests are provided in `test/features/nazoratchi/naz_statistics/naz_statistics_bloc_test.dart`:
- Unit tests for all BLoC events
- State transition testing
- Error handling verification
- Mock data testing

## Dependencies

The feature is registered in the dependency injection container:
- `NazStatisticsRemoteDatasourceImpl`: Singleton datasource
- `NazStatisticsBloc`: Factory BLoC instance

## Integration

To integrate this feature:
1. The BLoC and datasource are already registered in DI
2. Use `NazStatisticsPageWithBloc` for the complete implementation
3. Or integrate the BLoC into existing pages as needed

## Error Messages

All error messages are in Uzbek following the app's localization:
- Network errors: "Internet aloqasi yo'q. Iltimos, ulanishni tekshiring."
- API errors: Specific error messages for each endpoint
- Generic errors: "Kutilmagan xatolik yuz berdi"

## Future Enhancements

- Add caching for statistics data
- Implement offline support
- Add data export functionality
- Include more detailed analytics
