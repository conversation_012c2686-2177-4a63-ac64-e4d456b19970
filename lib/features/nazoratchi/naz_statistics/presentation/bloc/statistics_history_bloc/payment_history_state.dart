part of 'payment_history_bloc.dart';

/// Status enum for payment history state
enum PaymentHistoryStatus { initial, loading, success, failure }

/// Error type enum for better error handling
enum PaymentHistoryErrorType { network, api, unknown }

/// State class for payment history
class PaymentHistoryState extends Equatable {
  final PaymentHistoryStatus status;
  final String? message;
  final PaymentHistoryErrorType? errorType;
  final bool isRefreshing;
  final bool hasRefreshError;
  final bool isLoadingMore;
  final bool hasLoadMoreError;
  final PaymentHistoryResponse? paymentHistory;
  final PaymentHistoryFilters? currentFilters;

  const PaymentHistoryState({
    this.status = PaymentHistoryStatus.initial,
    this.message = '',
    this.errorType,
    this.isRefreshing = false,
    this.hasRefreshError = false,
    this.isLoadingMore = false,
    this.hasLoadMoreError = false,
    this.paymentHistory,
    this.currentFilters,
  });

  /// Copy with method for state updates
  PaymentHistoryState copyWith({
    PaymentHistoryStatus? status,
    String? message,
    PaymentHistoryErrorType? errorType,
    bool? isRefreshing,
    bool? hasRefreshError,
    bool? isLoadingMore,
    bool? hasLoadMoreError,
    PaymentHistoryResponse? paymentHistory,
    PaymentHistoryFilters? currentFilters,
  }) {
    return PaymentHistoryState(
      status: status ?? this.status,
      message: message ?? this.message,
      errorType: errorType ?? this.errorType,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasRefreshError: hasRefreshError ?? this.hasRefreshError,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasLoadMoreError: hasLoadMoreError ?? this.hasLoadMoreError,
      paymentHistory: paymentHistory ?? this.paymentHistory,
      currentFilters: currentFilters ?? this.currentFilters,
    );
  }

  /// Check if data is loaded
  bool get hasData => paymentHistory != null && paymentHistory!.docs.isNotEmpty;

  /// Check if loading
  bool get isLoading => status == PaymentHistoryStatus.loading;

  /// Check if success
  bool get isSuccess => status == PaymentHistoryStatus.success;

  /// Check if failure
  bool get isFailure => status == PaymentHistoryStatus.failure;

  /// Check if initial
  bool get isInitial => status == PaymentHistoryStatus.initial;

  /// Check if has next page
  bool get hasNextPage => paymentHistory?.hasNextPage ?? false;

  /// Get next page number
  int get nextPage => paymentHistory?.nextPage ?? 1;

  /// Check if has previous page
  bool get hasPrevPage => paymentHistory?.hasPrevPage ?? false;

  /// Get current page number
  int get currentPage => paymentHistory?.page ?? 1;

  /// Get total pages
  int get totalPages => paymentHistory?.totalPages ?? 0;

  /// Get total items count
  int get totalItems => paymentHistory?.totalDocs ?? 0;

  /// Get current items count
  int get currentItemsCount => paymentHistory?.docs.length ?? 0;

  /// Get total amount
  int get totalAmount => paymentHistory?.totalAmount ?? 0;

  @override
  List<Object?> get props => [
        status,
        message,
        errorType,
        isRefreshing,
        hasRefreshError,
        isLoadingMore,
        hasLoadMoreError,
        paymentHistory,
        currentFilters,
      ];

  @override
  String toString() {
    return 'PaymentHistoryState(status: $status, message: $message, isRefreshing: $isRefreshing, isLoadingMore: $isLoadingMore, hasData: $hasData, currentPage: $currentPage, totalPages: $totalPages)';
  }
}
