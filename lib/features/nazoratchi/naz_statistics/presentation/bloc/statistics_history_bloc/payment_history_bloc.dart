import 'package:bloc/bloc.dart';
import 'package:click_bazaar/core/mixins/error_handler_mixin.dart';
import 'package:click_bazaar/core/network/network_info.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import '../../../data/datasources/payment_history_remote_datasource.dart';
import '../../../data/models/payment_history_model.dart';

part 'payment_history_event.dart';
part 'payment_history_state.dart';

/// Filters for payment history
class PaymentHistoryFilters extends Equatable {
  final int? paymentType;
  final bool? payment;
  final String? date;
  final int limit;

  const PaymentHistoryFilters({
    this.paymentType,
    this.payment,
    this.date,
    this.limit = 10,
  });

  @override
  List<Object?> get props => [paymentType, payment, date, limit];
}

/// BLoC for managing naz payment history state
class NazPaymentHistoryBloc extends Bloc<PaymentHistoryEvent, PaymentHistoryState>
    with ErrorHandlerMixin<PaymentHistoryEvent, PaymentHistoryState> {
  final NetworkInfo networkInfo;
  final NazPaymentHistoryRemoteDatasourceImpl paymentHistoryRemoteDatasource;

  NazPaymentHistoryBloc({
    required this.networkInfo,
    required this.paymentHistoryRemoteDatasource,
  }) : super(const PaymentHistoryState()) {
    on<LoadPaymentHistoryEvent>(_handleLoadPaymentHistory);
    on<LoadMorePaymentHistoryEvent>(_handleLoadMorePaymentHistory);
    on<RefreshPaymentHistoryEvent>(_handleRefreshPaymentHistory);
  }

  /// Handle load payment history event
  Future<void> _handleLoadPaymentHistory(
    LoadPaymentHistoryEvent event,
    Emitter<PaymentHistoryState> emit,
  ) async {
    await executeApiCall<PaymentHistoryResponse>(
      apiCall: () => paymentHistoryRemoteDatasource.getPaymentHistory(
        page: 1,
        limit: event.limit,
        paymentType: event.paymentType,
        payment: event.payment,
        date: event.date,
      ),
      onLoading: () => emit(state.copyWith(
        status: PaymentHistoryStatus.loading,
        errorType: null,
        isRefreshing: false,
        hasRefreshError: false,
        isLoadingMore: false,
        hasLoadMoreError: false,
      )),
      onSuccess: (response) {
        print('Initial load: ${response.docs.length} items, hasNextPage: ${response.hasNextPage}, totalDocs: ${response.totalDocs}');
        emit(state.copyWith(
          status: PaymentHistoryStatus.success,
          paymentHistory: response,
          message: '',
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
          isLoadingMore: false,
          hasLoadMoreError: false,
          currentFilters: PaymentHistoryFilters(
            paymentType: event.paymentType,
            payment: event.payment,
            date: event.date,
            limit: event.limit,
          ),
        ));
      },
      onFailure: (message) => emit(state.copyWith(
        status: PaymentHistoryStatus.failure,
        message: message,
        errorType: _determineErrorType(message),
        isRefreshing: false,
        hasRefreshError: false,
        isLoadingMore: false,
        hasLoadMoreError: false,
      )),
    );
  }

  /// Handle load more payment history event
  Future<void> _handleLoadMorePaymentHistory(
    LoadMorePaymentHistoryEvent event,
    Emitter<PaymentHistoryState> emit,
  ) async {
    // Don't load more if already loading or no more pages
    if (state.isLoadingMore || !state.hasNextPage) return;

    // Set loading more flag
    emit(state.copyWith(
      isLoadingMore: true,
      hasLoadMoreError: false,
    ));

    await executeApiCall<PaymentHistoryResponse>(
      apiCall: () => paymentHistoryRemoteDatasource.getPaymentHistory(
        page: state.nextPage,
        limit: state.currentFilters?.limit ?? 10,
        paymentType: state.currentFilters?.paymentType,
        payment: state.currentFilters?.payment,
        date: state.currentFilters?.date,
      ),
      onLoading: () {
        // Don't change status to loading during load more
      },
      onSuccess: (response) {
        // Combine existing items with new items
        final combinedDocs = <PaymentHistoryItem>[
          ...state.paymentHistory?.docs ?? [],
          ...response.docs,
        ];

        print('Load more: ${response.docs.length} new items, total combined: ${combinedDocs.length}, hasNextPage: ${response.hasNextPage}');

        // Create combined response with updated pagination metadata from the new response
        final combinedResponse = PaymentHistoryResponse(
          docs: combinedDocs,
          totalDocs: response.totalDocs,
          limit: response.limit,
          totalPages: response.totalPages,
          page: response.page,
          pagingCounter: response.pagingCounter,
          hasPrevPage: response.hasPrevPage,
          hasNextPage: response.hasNextPage,
          prevPage: response.prevPage,
          nextPage: response.nextPage,
        );

        emit(state.copyWith(
          status: PaymentHistoryStatus.success,
          paymentHistory: combinedResponse,
          message: '',
          errorType: null,
          isLoadingMore: false,
          hasLoadMoreError: false,
        ));
      },
      onFailure: (message) {
        emit(state.copyWith(
          status: state.hasData ? PaymentHistoryStatus.success : PaymentHistoryStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isLoadingMore: false,
          hasLoadMoreError: true,
        ));
      },
    );
  }

  /// Handle refresh payment history event
  Future<void> _handleRefreshPaymentHistory(
    RefreshPaymentHistoryEvent event,
    Emitter<PaymentHistoryState> emit,
  ) async {
    // Set refreshing flag and keep existing data
    emit(state.copyWith(
      isRefreshing: true,
      errorType: null,
      hasRefreshError: false,
      isLoadingMore: false,
      hasLoadMoreError: false,
    ));

    // Check network connectivity first for refresh operations
    if (!await isNetworkConnected()) {
      emit(state.copyWith(
        status: state.hasData ? PaymentHistoryStatus.success : PaymentHistoryStatus.failure,
        message: LocaleKeys.common_no_internet_connection.tr(),
        errorType: PaymentHistoryErrorType.network,
        isRefreshing: false,
        hasRefreshError: true,
      ));
      return;
    }

    await executeApiCall<PaymentHistoryResponse>(
      apiCall: () => paymentHistoryRemoteDatasource.getPaymentHistory(
        page: 1,
        limit: state.currentFilters?.limit ?? 10,
        paymentType: state.currentFilters?.paymentType,
        payment: state.currentFilters?.payment,
        date: state.currentFilters?.date,
      ),
      onLoading: () {
        // Don't change status to loading during refresh to keep existing content visible
      },
      onSuccess: (response) {
        emit(state.copyWith(
          status: PaymentHistoryStatus.success,
          paymentHistory: response,
          message: '',
          errorType: null,
          isRefreshing: false,
          hasRefreshError: false,
        ));
      },
      onFailure: (message) {
        emit(state.copyWith(
          status: state.hasData ? PaymentHistoryStatus.success : PaymentHistoryStatus.failure,
          message: message,
          errorType: _determineErrorType(message),
          isRefreshing: false,
          hasRefreshError: true,
        ));
      },
    );
  }

  /// Override network connectivity check
  @override
  Future<bool> isNetworkConnected() async {
    return await networkInfo.isConnected;
  }

  /// Determine error type from message
  PaymentHistoryErrorType _determineErrorType(String message) {
    if (message.contains('Internet') || message.contains('aloqa')) {
      return PaymentHistoryErrorType.network;
    } else if (message.contains('401') || message.contains('403')) {
      return PaymentHistoryErrorType.api;
    } else {
      return PaymentHistoryErrorType.unknown;
    }
  }
}
