part of 'payment_history_bloc.dart';

/// Base class for payment history events
abstract class PaymentHistoryEvent extends Equatable {
  const PaymentHistoryEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load payment history with filters
class LoadPaymentHistoryEvent extends PaymentHistoryEvent {
  final int? paymentType;
  final bool? payment;
  final String? date;
  final int limit;

  const LoadPaymentHistoryEvent({
    this.paymentType,
    this.payment,
    this.date,
    this.limit = 10,
  });

  @override
  List<Object?> get props => [paymentType, payment, date, limit];
}

/// Event to load more payment history items (pagination)
class LoadMorePaymentHistoryEvent extends PaymentHistoryEvent {
  const LoadMorePaymentHistoryEvent();
}

/// Event to refresh payment history
class RefreshPaymentHistoryEvent extends PaymentHistoryEvent {
  const RefreshPaymentHistoryEvent();
}
