part of 'naz_statistics_bloc.dart';

/// Base class for naz statistics events
abstract class NazStatisticsEvent extends Equatable {
  const NazStatisticsEvent();

  @override
  List<Object?> get props => [];
}

/// Event to load plan statistics for a specific date
class LoadPlanStatisticsEvent extends NazStatisticsEvent {
  final String date;

  const LoadPlanStatisticsEvent({required this.date});

  @override
  List<Object?> get props => [date];
}

/// Event to load place statistics for a specific date
class LoadPlaceStatisticsEvent extends NazStatisticsEvent {
  final String date;

  const LoadPlaceStatisticsEvent({required this.date});

  @override
  List<Object?> get props => [date];
}

/// Event to load payment statistics
class LoadPaymentStatisticsEvent extends NazStatisticsEvent {
  final String date;

  const LoadPaymentStatisticsEvent({required this.date});

  @override
  List<Object> get props => [date];
}

/// Event to load all statistics for a specific date
class LoadAllStatisticsEvent extends NazStatisticsEvent {
  final String date;

  const LoadAllStatisticsEvent({required this.date});

  @override
  List<Object?> get props => [date];
}

/// Event to refresh all statistics for a specific date
class RefreshAllStatisticsEvent extends NazStatisticsEvent {
  final String date;

  const RefreshAllStatisticsEvent({required this.date});

  @override
  List<Object?> get props => [date];
}
