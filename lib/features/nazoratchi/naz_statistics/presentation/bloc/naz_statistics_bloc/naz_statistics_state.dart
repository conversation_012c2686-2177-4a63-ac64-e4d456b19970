part of 'naz_statistics_bloc.dart';

/// Status enum for naz statistics state
enum NazStatisticsStatus { initial, loading, success, failure }

/// Error type enum for better error handling
enum NazStatisticsErrorType { network, api, unknown }

/// State class for naz statistics
class NazStatisticsState extends Equatable {
  final NazStatisticsStatus status;
  final String? message;
  final NazStatisticsErrorType? errorType;
  final bool isRefreshing;
  final bool hasRefreshError;
  final PlanStatisticsModel? planStatistics;
  final PlaceStatisticsModel? placeStatistics;
  final PaymentStatisticsModel? paymentStatistics;
  final String? currentDate;

  const NazStatisticsState({
    this.status = NazStatisticsStatus.initial,
    this.message = '',
    this.errorType,
    this.isRefreshing = false,
    this.hasRefreshError = false,
    this.planStatistics,
    this.placeStatistics,
    this.paymentStatistics,
    this.currentDate,
  });

  /// Copy with method for state updates
  NazStatisticsState copyWith({
    NazStatisticsStatus? status,
    String? message,
    NazStatisticsErrorType? errorType,
    bool? isRefreshing,
    bool? hasRefreshError,
    PlanStatisticsModel? planStatistics,
    PlaceStatisticsModel? placeStatistics,
    PaymentStatisticsModel? paymentStatistics,
    String? currentDate,
  }) {
    return NazStatisticsState(
      status: status ?? this.status,
      message: message ?? this.message,
      errorType: errorType ?? this.errorType,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      hasRefreshError: hasRefreshError ?? this.hasRefreshError,
      planStatistics: planStatistics ?? this.planStatistics,
      placeStatistics: placeStatistics ?? this.placeStatistics,
      paymentStatistics: paymentStatistics ?? this.paymentStatistics,
      currentDate: currentDate ?? this.currentDate,
    );
  }

  /// Check if all statistics are loaded
  bool get hasAllStatistics =>
      planStatistics != null &&
      placeStatistics != null &&
      paymentStatistics != null;

  /// Check if any statistics are loaded
  bool get hasAnyStatistics =>
      planStatistics != null ||
      placeStatistics != null ||
      paymentStatistics != null;

  /// Check if state is loading
  bool get isLoading => status == NazStatisticsStatus.loading;

  /// Check if state is success
  bool get isSuccess => status == NazStatisticsStatus.success;

  /// Check if state is failure
  bool get isFailure => status == NazStatisticsStatus.failure;

  /// Check if state is initial
  bool get isInitial => status == NazStatisticsStatus.initial;

  /// Check if error is network related
  bool get isNetworkError => isFailure && errorType == NazStatisticsErrorType.network;

  /// Check if error is API related
  bool get isApiError => isFailure && errorType == NazStatisticsErrorType.api;

  @override
  List<Object?> get props => [
        status,
        message,
        errorType,
        isRefreshing,
        hasRefreshError,
        planStatistics,
        placeStatistics,
        paymentStatistics,
        currentDate,
      ];

  @override
  String toString() {
    return 'NazStatisticsState(status: $status, message: $message, currentDate: $currentDate, isRefreshing: $isRefreshing, hasRefreshError: $hasRefreshError, hasAllStatistics: $hasAllStatistics)';
  }
}
