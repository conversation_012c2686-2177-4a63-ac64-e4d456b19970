import 'package:click_bazaar/core/theme/app_text_styles.dart';
import 'package:click_bazaar/translations/locale_keys.g.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../../../../core/theme/app_colors.dart';

class StatisticsCircleChart extends StatelessWidget {
  final int totalCount;
  final int tolangan;
  final int tolanmagan;
  final int bosh;
  final int belgilanmaganCount;

  const StatisticsCircleChart({
    super.key,
    required this.totalCount,
    required this.tolangan,
    required this.tolanmagan,
    required this.bosh,
    required this.belgilanmaganCount,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 250,
      height: 250,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Custom circular progress indicator
          CustomPaint(
            size: const Size(250, 250),
            painter: CircularChartPainter(
              successCount: tolangan,
              failureCount: tolanmagan,
              bothCount: bosh,
              belgilanmaganCount: belgilanmaganCount,
              totalCount: totalCount,
              context: context,
            ),
          ),
          // Center content with primary color background
          Container(
            width: 135,
            height: 135,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              shape: BoxShape.circle,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  LocaleKeys.nazoratchi_statistics_all.tr(),
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  totalCount.toString(),
                  style: AppTextStyles.headlineLarge.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.primary,
                    fontSize: 30,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CircularChartPainter extends CustomPainter {
  final int successCount;
  final int failureCount;
  final int bothCount;
  final int belgilanmaganCount;
  final int totalCount;
  final BuildContext context;

  CircularChartPainter({
    required this.successCount,
    required this.failureCount,
    required this.bothCount,
    required this.belgilanmaganCount,
    required this.totalCount,
    required this.context,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final outerRadius = size.width / 2 - 15;
    const strokeWidth = 40.0;

    // Draw background circle first (theme-aware gray for the entire ring)
    final backgroundPaint = Paint()
      ..color = AppColors.cUnbindedColor // Keep this neutral gray as it's part of the chart design
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.butt;

    canvas.drawCircle(center, outerRadius, backgroundPaint);

    if (totalCount > 0) {
      // Calculate angles - start from top and go clockwise
      final successAngle = (successCount / totalCount) * 2 * math.pi;
      final failureAngle = (failureCount / totalCount) * 2 * math.pi;
      final belgilanmaganAngle =
          (belgilanmaganCount / totalCount) * 2 * math.pi;
      final bothAngle = (bothCount / totalCount) * 2 * math.pi;

      // Define starting angle for 12 o'clock position (top)
      const startAngle = -math.pi/2; // -90 degrees from 0 (3 o'clock), which puts it at 12 o'clock (top)

      // Success arc (green) - starts from top (12 o'clock), goes clockwise
      if (successCount > 0) {
        final successPaint = Paint()
          ..color = AppColors.cGreenishColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.butt;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: outerRadius),
          startAngle, // Start from top (12 o'clock)
          successAngle,
          false,
          successPaint,
        );
      }

      // Failure arc (red) - starts after green section
      if (failureCount > 0) {
        final failurePaint = Paint()
          ..color = AppColors.cReddishColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.butt;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: outerRadius),
          startAngle + successAngle, // Start after success arc
          failureAngle,
          false,
          failurePaint,
        );
      }

      // Belgilanmagan arc (orange) - starts after failure section
      if (bothCount > 0) {

        final bothPaint = Paint()
          ..color = AppColors.cYellowishColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.butt;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: outerRadius),
          startAngle + successAngle + failureAngle + belgilanmaganAngle,
          // Start after belgilanmagan arc
          bothAngle,
          false,
          bothPaint,
        );

      }

      // Bo'sh arc (gray) - starts after belgilanmagan section
      if (belgilanmaganCount > 0) {
        final belgilanmaganPaint = Paint()
          ..color = AppColors.cUnbindedColor
          ..style = PaintingStyle.stroke
          ..strokeWidth = strokeWidth
          ..strokeCap = StrokeCap.butt;

        canvas.drawArc(
          Rect.fromCircle(center: center, radius: outerRadius),
          startAngle + successAngle + failureAngle, // Start after failure arc
          belgilanmaganAngle,
          false,
          belgilanmaganPaint,
        );

      }
    }

    // Draw dotted border inside the colored border
    _drawDottedBorder(canvas, center, outerRadius - 27,
        const Color(0xFF9E9E9E).withValues(alpha: 0.5));

    // Draw inner circle - white in light theme, primary in dark theme
    final isDarkTheme = Theme.of(context).brightness == Brightness.dark;
    final innerCirclePaint = Paint()
      ..color = Theme.of(context).colorScheme.primary.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, outerRadius - 34, innerCirclePaint);

    // Draw gray border around inner circle (like before)
    final borderPaint = Paint()
      ..color = const Color(0xFF9E9E9E).withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawCircle(center, outerRadius - 34, borderPaint);
  }

  void _drawDottedBorder(
      Canvas canvas, Offset center, double radius, Color color) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Calculate dot count for border with closer spacing
    final circumference = 2 * math.pi * radius;
    final dotSpacing = 8.0; // Closer spacing for border
    final dotCount = (circumference / dotSpacing).round();
    const dotSize = 1.5;

    for (int i = 0; i < dotCount; i++) {
      final angle = (i / dotCount) * 2 * math.pi;
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);

      canvas.drawCircle(Offset(x, y), dotSize, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate != this;
  }
}
