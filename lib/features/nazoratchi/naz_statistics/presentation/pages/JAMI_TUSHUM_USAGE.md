# Jami Tushum Page - Usage Guide

## Overview

The `JamiTushumPage` displays a list of places with their rental income amounts. It follows the existing UI patterns and architecture used throughout the nazoratchi features.

## Features

- **Clean List Display**: Shows place numbers and rental amounts in a clean, organized format
- **Responsive Design**: Uses ScreenUtil for responsive sizing
- **Loading States**: Includes shimmer loading animations
- **Error Handling**: Displays error states with retry functionality
- **Swipe-to-Refresh**: Allows users to refresh the data
- **Consistent Theming**: Follows app color scheme and text styles

## Navigation

### From Statistics Page
```dart
// Navigate from statistics page or any other page
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => const JamiTushumPage(),
  ),
);
```

### From Menu Item
```dart
// Example: Add to profile menu or statistics menu
NazProfileMenuItem(
  title: '<PERSON>i tushum',
  iconPath: Assets.iconsIncome, // Add appropriate icon
  onTap: () {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const <PERSON>iTushumPage(),
      ),
    );
  },
  subTitle: '<PERSON>cha joy<PERSON>an tushgan daromad',
),
```

## Data Structure

The page uses the following models:

### PlaceIncomeModel
```dart
class PlaceIncomeModel {
  final String placeNumber;  // Place number (e.g., "35", "24")
  final int amount;          // Rental amount in UZS
}
```

### PlaceIncomeResponse
```dart
class PlaceIncomeResponse {
  final List<PlaceIncomeModel> places;  // List of places with income
  final int totalAmount;                // Total income amount
}
```

## API Integration

To integrate with real API data, modify the `_loadData()` method in `JamiTushumPage`:

```dart
Future<void> _loadData() async {
  setState(() {
    _isLoading = true;
    _hasError = false;
  });

  try {
    // Replace with actual API call
    final response = await apiService.getPlaceIncomeData();
    final placeIncomeData = PlaceIncomeResponse.fromJson(response.data);

    setState(() {
      _placeIncomeData = placeIncomeData;
      _isLoading = false;
    });
  } catch (e) {
    setState(() {
      _hasError = true;
      _errorMessage = 'Ma\'lumotlarni yuklashda xatolik yuz berdi';
      _isLoading = false;
    });
  }
}
```

## BLoC Integration (Future Enhancement)

For full BLoC integration, you can:

1. **Add Events** to `naz_statistics_event.dart`:
```dart
class LoadPlaceIncomeEvent extends NazStatisticsEvent {
  final String? date;
  const LoadPlaceIncomeEvent({this.date});
}
```

2. **Add State Properties** to `naz_statistics_state.dart`:
```dart
final PlaceIncomeResponse? placeIncomeData;
```

3. **Add Handler** to `naz_statistics_bloc.dart`:
```dart
Future<void> _handleLoadPlaceIncome(
  LoadPlaceIncomeEvent event,
  Emitter<NazStatisticsState> emit,
) async {
  // Implementation
}
```

## UI Components

### Header
- Displays column titles: "Rastalar" and "Tushgan summa (UZS)"
- Uses card styling with border

### List Items
- Each item shows place number (e.g., "#35") and formatted amount
- Uses comma-separated number formatting
- Consistent spacing and styling

### Loading States
- Shimmer animation for skeleton loading
- Separate shimmer for header and list items
- Maintains layout structure during loading

### Error States
- Centered error message with retry button
- Uses UniversalLoading.error component
- Consistent error handling patterns

## Styling

The page follows the app's design system:

- **Background**: `AppColors.cBackgroundColor`
- **Cards**: `AppColors.cCardsColor` with `AppColors.cGrayBorderColor` border
- **Text**: `AppColors.white` for primary text, `AppColors.cTextGrayColor` for secondary
- **Typography**: Uses `AppTextStyles` with appropriate font sizes

## Testing

Run the model tests:
```bash
flutter test test/features/nazoratchi/naz_statistics/place_income_model_test.dart
```

The tests cover:
- JSON serialization/deserialization
- Null value handling
- Empty instance creation
- Copy with functionality

## File Structure

```
lib/features/nazoratchi/naz_statistics/
├── data/models/
│   └── place_income_model.dart          # Data models
├── presentation/pages/
│   ├── jami_tushum_page.dart           # Main page implementation
│   └── JAMI_TUSHUM_USAGE.md            # This documentation
└── test/
    └── place_income_model_test.dart     # Unit tests
```

## Future Enhancements

- [ ] Add date filtering functionality
- [ ] Implement search/filter by place number
- [ ] Add export functionality (PDF/Excel)
- [ ] Include pagination for large datasets
- [ ] Add sorting options (by amount, place number)
- [ ] Implement pull-to-refresh with BLoC
- [ ] Add total amount display in header
- [ ] Include charts/graphs for visual representation
