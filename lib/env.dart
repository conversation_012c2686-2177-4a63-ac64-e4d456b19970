import 'package:click_bazaar/core/utils/api_path.dart';

enum Environment { dev, prod }

abstract class AppEnvironment {
  static late Environment _environment;
  static late String baseApiUrl;
  static late String baseImageUrl;

  static Environment get environment => _environment;

  static setupEnv(Environment env) {
    _environment = env;
    switch (env) {
      case Environment.dev:
        {
          baseApiUrl = ApiPath.baseUrl;
          baseImageUrl = ApiPath.baseUrlFile;
          break;
        }
      case Environment.prod:
        {
          baseApiUrl = ApiPath.baseUrl;
          baseImageUrl = ApiPath.baseUrlFile;
          break;
        }
    }
  }
}
