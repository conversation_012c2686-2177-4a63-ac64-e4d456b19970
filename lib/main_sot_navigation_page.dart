import 'package:click_bazaar/core/widgets/custom_bottom_navigation_bar.dart';
import 'package:click_bazaar/core/widgets/role_switcher.dart';
import 'package:click_bazaar/features/sotuvchi/sot_home/presentation/page/sot_home_page.dart';
import 'package:click_bazaar/features/sotuvchi/sot_payment_history/presentation/page/sot_payment_history_page.dart';
import 'package:click_bazaar/features/sotuvchi/sot_empty_places/presentation/pages/sot_empty_places_page.dart';
import 'package:click_bazaar/features/sotuvchi/sot_profile/presentation/pages/sot_profile_page.dart';
import 'package:click_bazaar/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'translations/locale_keys.g.dart';
import 'package:flutter/services.dart';


class MainSotNavigationPage extends StatefulWidget {
  final Function(UserRole)? onRoleChanged;

  const MainSotNavigationPage({
    super.key,
    this.onRoleChanged,
  });

  @override
  State<MainSotNavigationPage> createState() => _MainSotNavigationPageState();
}

class _MainSotNavigationPageState extends State<MainSotNavigationPage> {
  int _currentIndex = 0;
  DateTime? _lastBackPressTime;
  bool _isFirstBackPress = true;

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      const SotHomePage(),
      const SotPaymentHistoryPage(),
      const SotEmptyPlacesPage(),
      SotProfilePage(onRoleChanged: widget.onRoleChanged),
    ];
  }

  List<CustomBottomNavItem> get _bottomNavItems => [
        CustomBottomNavItem(
          iconPath: Assets.iconsHome,
          label: LocaleKeys.navigation_home.tr(),
          activeIconPath: Assets.iconsHome,
        ),
        CustomBottomNavItem(
          iconPath: Assets.iconsPaymentHistory,
          activeIconPath: Assets.iconsPaymentHistory,
          label: LocaleKeys.navigation_payment_history.tr(),
        ),
        CustomBottomNavItem(
          label: LocaleKeys.navigation_empty_places.tr(),
          iconPath: Assets.iconsFreeSpace,
          activeIconPath: Assets.iconsFreeSpace,
        ),
        CustomBottomNavItem(
          label: LocaleKeys.navigation_profile.tr(),
          iconPath: Assets.iconsProfile,
          activeIconPath: Assets.iconsProfile,
        ),
      ];

  void _handleBackPress() {
    final DateTime now = DateTime.now();

    // If not on the first tab (home), go to first tab
    if (_currentIndex != 0) {
      setState(() {
        _currentIndex = 0;
      });
      _lastBackPressTime = null; // Reset timer when navigating to home
      _isFirstBackPress = true; // Reset first press flag
      return;
    }

    // More aggressive reset: if more than 5 minutes have passed, reset
    if (_lastBackPressTime != null &&
        now.difference(_lastBackPressTime!) > const Duration(minutes: 5)) {
      _lastBackPressTime = null;
      _isFirstBackPress = true;
    }

    // If this is the first back press on home tab OR more than 2 seconds have passed
    if (_isFirstBackPress || _lastBackPressTime == null ||
        now.difference(_lastBackPressTime!) > const Duration(seconds: 2)) {
      _lastBackPressTime = now;
      _isFirstBackPress = false;

      // Show snackbar message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(LocaleKeys.back_again_exit.tr()),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else {
      // Exit the app
      _lastBackPressTime = null;
      _isFirstBackPress = true;
      SystemNavigator.pop();
    }
  }


  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) {
        if (didPop) return;
        _handleBackPress();
      },
      child: Scaffold(
        body: _pages[_currentIndex],
        bottomNavigationBar: CustomBottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
            // Reset back press timer and flag when user manually switches tabs
            _lastBackPressTime = null;
            _isFirstBackPress = true;
          },
          items: _bottomNavItems,
        ),
      ),
    );
  }
}
