#!/bin/bash

# Flutter APK Build & Telegram Upload Service
# Cross-platform portable service for building and uploading Flutter APKs
# Compatible with macOS, Windows (Git Bash/WSL), and Linux

set -e  # Exit on any error

# =============================================================================
# SCRIPT CONFIGURATION
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
SERVICES_DIR="$SCRIPT_DIR"

# Source utility scripts
source "$SERVICES_DIR/scripts/platform_utils.sh"
source "$SERVICES_DIR/scripts/build_utils.sh"
source "$SERVICES_DIR/scripts/telegram_utils.sh"
source "$SERVICES_DIR/scripts/file_utils.sh"
source "$SERVICES_DIR/scripts/external_hosting.sh"
source "$SERVICES_DIR/config/build_config.sh"

# =============================================================================
# GLOBAL VARIABLES
# =============================================================================

BUILD_TYPE="release"
BUILD_FLAVOR=""
DRY_RUN=false
CLEAN_BUILD=false
RUN_TESTS=false
SHOW_HELP=false
DEBUG_MODE=false
LOG_FILE="${LOG_FILE:-$SERVICES_DIR/build_and_upload.log}"
ERRORS=()
WARNINGS=()

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

print_banner() {
    echo ""
    echo "🚀 Flutter APK Build & Telegram Upload Service"
    echo "================================================"
    echo ""
}

print_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

OPTIONS:
    --clean                 Clean build folder before building
    --dry-run              Simulate the process without actual building/uploading
    --test                 Run tests if test folder contains test files
    --debug                Build debug APK
    --release              Build release APK (default)
    --flavor <name>        Specify build flavor
    --help                 Show this help message

EXAMPLES:
    $0                                    # Basic release build and upload
    $0 --clean --flavor production        # Clean build with production flavor
    $0 --dry-run                         # Simulate without building/uploading
    $0 --test --debug                    # Run tests and build debug APK

ENVIRONMENT:
    Configure settings in .env file (copy from config/.env.example)

EOF
}

log_timestamp() {
    date +"%Y-%m-%d %H:%M:%S"
}

log_to_file() {
    local msg="$1"
    echo -e "$msg" >> "$LOG_FILE"
}

log_info() {
    local msg="[$(log_timestamp)] ℹ️  $1"
    echo -e "$msg"
    log_to_file "$msg"
}

log_success() {
    local msg="[$(log_timestamp)] ✅ $1"
    echo -e "$msg"
    log_to_file "$msg"
}

log_warning() {
    local msg="[$(log_timestamp)] ⚠️  $1"
    echo -e "$msg"
    log_to_file "$msg"
    WARNINGS+=("$1")
}

log_error() {
    local msg="[$(log_timestamp)] ❌ $1"
    echo -e "$msg" >&2
    log_to_file "$msg"
    ERRORS+=("$1")
}

log_fatal() {
    log_error "$1"
    print_error_summary
    exit 1
}

log_debug() {
    if [[ "$DEBUG_MODE" == "true" || "$DEBUG" == "1" ]]; then
        local msg="[$(log_timestamp)] 🐛 DEBUG: $1"
        echo -e "$msg"
        log_to_file "$msg"
    fi
}

print_error_summary() {
    echo -e "\n===== BUILD & UPLOAD SUMMARY =====" | tee -a "$LOG_FILE"
    if [[ ${#ERRORS[@]} -gt 0 ]]; then
        echo -e "❌ Errors:" | tee -a "$LOG_FILE"
        for err in "${ERRORS[@]}"; do
            echo -e "  - $err" | tee -a "$LOG_FILE"
        done
    else
        echo -e "✅ No errors." | tee -a "$LOG_FILE"
    fi
    if [[ ${#WARNINGS[@]} -gt 0 ]]; then
        echo -e "⚠️  Warnings:" | tee -a "$LOG_FILE"
        for warn in "${WARNINGS[@]}"; do
            echo -e "  - $warn" | tee -a "$LOG_FILE"
        done
    else
        echo -e "✅ No warnings." | tee -a "$LOG_FILE"
    fi
    echo -e "Log file: $LOG_FILE\n" | tee -a "$LOG_FILE"
}

print_config_summary() {
    echo ""
    echo "📋 Configuration Summary"
    echo "========================"
    echo "App Name: $(get_app_name)"
    echo "Version: $(get_app_version)"
    echo "Build Type: $BUILD_TYPE"
    echo "Build Flavor: ${BUILD_FLAVOR:-"none"}"
    echo "Git Branch: $(get_git_branch)"
    echo "Platform: $PLATFORM"
    echo "Upload Method: $UPLOAD_METHOD"
    if [[ "$UPLOAD_METHOD" == "external" ]]; then
        echo "Hosting Service: $EXTERNAL_HOSTING_SERVICE"
    else
        # Human-friendly Telegram chat/channel info
        local chat_type=""
        if [[ "$TELEGRAM_CHAT_ID" =~ ^-100 ]]; then
            chat_type="(channel or supergroup)"
        elif [[ "$TELEGRAM_CHAT_ID" =~ ^- ]]; then
            chat_type="(group)"
        elif [[ -n "$TELEGRAM_CHAT_ID" ]]; then
            chat_type="(private chat)"
        else
            chat_type="(not set)"
        fi
        echo "Telegram Target: $chat_type $TELEGRAM_CHAT_ID${TELEGRAM_TOPIC_ID:+, topic: $TELEGRAM_TOPIC_ID}"
    fi
    echo "Upload Timeout: ${UPLOAD_TIMEOUT}s"
    echo "Max File Size: $(format_file_size "$MAX_FILE_SIZE")"
    echo ""
}

# =============================================================================
# ARGUMENT PARSING
# =============================================================================

parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --clean)
                CLEAN_BUILD=true
                shift
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --test)
                RUN_TESTS=true
                shift
                ;;
            --debug)
                BUILD_TYPE="debug"
                shift
                ;;
            --release)
                BUILD_TYPE="release"
                shift
                ;;
            --flavor)
                BUILD_FLAVOR="$2"
                shift 2
                ;;
            --help)
                SHOW_HELP=true
                shift
                ;;
            *)
                log_error "Unknown option: $1"
                print_usage
                exit 1
                ;;
        esac
    done
}

# =============================================================================
# MAIN EXECUTION FUNCTIONS
# =============================================================================

validate_environment() {
    log_info "Validating environment..."
    
    # Check if we're in a Flutter project
    if [[ ! -f "$PROJECT_ROOT/pubspec.yaml" ]]; then
        log_error "Not a Flutter project! pubspec.yaml not found."
        exit 1
    fi
    
    # Check Flutter installation
    if ! command -v flutter &> /dev/null; then
        if [[ -n "$FLUTTER_ROOT" ]]; then
            export PATH="$FLUTTER_ROOT/bin:$PATH"
            if ! command -v flutter &> /dev/null; then
                log_error "Flutter not found in PATH or FLUTTER_ROOT"
                exit 1
            fi
        else
            log_error "Flutter not found in PATH. Install Flutter or set FLUTTER_ROOT in .env"
            exit 1
        fi
    fi
    
    log_success "Environment validation passed"
}

main() {
    print_banner
    
    # Parse command line arguments
    parse_arguments "$@"
    
    if [[ "$SHOW_HELP" == "true" ]]; then
        print_usage
        exit 0
    fi
    
    # Load configuration
    load_config
    
    # Apply environment overrides
    if [[ -n "$BUILD_TYPE_ENV" ]]; then
        BUILD_TYPE="$BUILD_TYPE_ENV"
    fi
    if [[ -n "$BUILD_FLAVOR_ENV" ]]; then
        BUILD_FLAVOR="$BUILD_FLAVOR_ENV"
    fi
    if [[ "$ALWAYS_CLEAN" == "1" ]]; then
        CLEAN_BUILD=true
    fi
    if [[ "$ALWAYS_TEST" == "1" ]]; then
        RUN_TESTS=true
    fi
    if [[ "$DEBUG" == "1" ]]; then
        DEBUG_MODE=true
    fi
    
    log_info "Configuration loaded"
    log_debug "Build type: $BUILD_TYPE"
    log_debug "Build flavor: ${BUILD_FLAVOR:-"none"}"
    log_debug "Dry run: $DRY_RUN"
    log_debug "Clean build: $CLEAN_BUILD"
    log_debug "Run tests: $RUN_TESTS"
    
    # Validate environment
    validate_environment
    
    # Initialize platform-specific settings
    init_platform
    
    # --- NEW: Check Telegram connection and resolve chat/topic names before build ---
    if command -v get_telegram_chat_info &> /dev/null; then
        get_telegram_chat_info
        test_telegram_connection
    else
        source "$SERVICES_DIR/scripts/telegram_utils.sh"
        get_telegram_chat_info
        test_telegram_connection
    fi
    # --- END NEW ---
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "🧪 DRY RUN MODE - Simulating process..."
        simulate_build_process
        print_error_summary
        exit 0
    fi
    
    # Execute build process
    execute_build_process
    
    print_error_summary
}

# =============================================================================
# SCRIPT ENTRY POINT
# =============================================================================

# Trap errors and cleanup
trap 'log_error "Script failed at line $LINENO"; print_error_summary; exit 1' ERR

# Run main function with all arguments
main "$@"
