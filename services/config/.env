# Flutter APK Build & Telegram Upload Service Configuration

# =============================================================================
# TELEGRAM CONFIGURATION
# =============================================================================

# Telegram Bot Token (Required)
TELEGRAM_BOT_TOKEN=**********************************************

# Telegram Chat ID (Required)
TELEGRAM_CHAT_ID=-1002063224194

# Telegram Topic ID (Optional)
TELEGRAM_TOPIC_ID=918

# =============================================================================
# BUILD CONFIGURATION
# =============================================================================

# Build Type (debug or release)
BUILD_TYPE=release

# Build Flavor (Optional)
BUILD_FLAVOR=

# App Name Override (Optional)
APP_NAME_OVERRIDE=

# =============================================================================
# GIT CONFIGURATION
# =============================================================================

# Git Branch Override (Optional)
GIT_BRANCH_OVERRIDE=

# =============================================================================
# UPLOAD CONFIGURATION
# =============================================================================

# Upload Timeout (seconds)
UPLOAD_TIMEOUT=300

# Maximum File Size (bytes)
# Increased to 150MB to accommodate larger APKs
MAX_FILE_SIZE=150000000

# =============================================================================
# FLUTTER CONFIGURATION
# =============================================================================

# Flutter Root Path (Optional)
FLUTTER_ROOT=

# Flutter Channel (Optional)
FLUTTER_CHANNEL=

# =============================================================================
# ADVANCED CONFIGURATION
# =============================================================================

# Debug Mode (Optional)
DEBUG=1

# Clean Build (Optional)
ALWAYS_CLEAN=0

# Run Tests (Optional)
ALWAYS_TEST=0

# Custom Build Arguments (Optional)
CUSTOM_BUILD_ARGS=

# =============================================================================
# CHANGELOG CONFIGURATION
# =============================================================================

# Changelog File Path (Optional)
CHANGELOG_FILE=CHANGELOG.md

# Changelog Lines (Optional)
CHANGELOG_LINES=5
