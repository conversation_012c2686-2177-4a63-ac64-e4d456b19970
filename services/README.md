# Flutter APK Build & Telegram Upload Service

A portable, cross-platform service for automatically building Flutter APKs and uploading them to Telegram channels/groups.

## 🚀 Quick Start

1. Copy the entire `services` folder to your Flutter project root
2. Copy `.env.example` to `.env` and configure your settings
3. Run the build script:
   ```bash
   ./services/build_and_upload.sh
   ```

## 📁 Service Structure

```
services/
├── build_and_upload.sh      # Main build and upload script
├── config/
│   ├── .env.example         # Environment configuration template
│   └── build_config.sh      # Build configuration utilities
├── scripts/
│   ├── build_utils.sh       # Build helper functions
│   ├── telegram_utils.sh    # Telegram API utilities
│   ├── platform_utils.sh    # Cross-platform compatibility
│   └── file_utils.sh        # File handling utilities
├── templates/
│   └── changelog.md         # Changelog template
└── README.md               # This file
```

## ⚙️ Configuration

### Environment Variables (.env)

```bash
# Telegram Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
TELEGRAM_TOPIC_ID=your_topic_id_here  # Optional, for groups with topics

# Build Configuration
BUILD_TYPE=release                     # debug or release
BUILD_FLAVOR=                         # Optional flavor name
APP_NAME_OVERRIDE=                    # Optional app name override

# Git Configuration
GIT_BRANCH_OVERRIDE=                  # Optional branch name override

# Upload Configuration
UPLOAD_TIMEOUT=300                    # Upload timeout in seconds
MAX_FILE_SIZE=50000000               # Max file size in bytes (50MB default)
```

### Build Flavors Support

The service automatically detects and supports Flutter flavors. If your project has flavors configured in `android/app/build.gradle`, you can specify them using the `BUILD_FLAVOR` environment variable or the `--flavor` command line argument.

## 🎯 Usage

### Basic Usage
```bash
./services/build_and_upload.sh
```

### Advanced Usage
```bash
# Clean build with specific flavor
./services/build_and_upload.sh --clean --flavor production

# Dry run (simulate without building/uploading)
./services/build_and_upload.sh --dry-run

# Run tests before building
./services/build_and_upload.sh --test

# Debug build
./services/build_and_upload.sh --debug

# Custom build type and flavor
./services/build_and_upload.sh --release --flavor staging
```

### Command Line Options

| Option | Description |
|--------|-------------|
| `--clean` | Clean build folder before building |
| `--dry-run` | Simulate the process without actual building/uploading |
| `--test` | Run tests if test folder contains test files |
| `--debug` | Build debug APK (default: release) |
| `--release` | Build release APK |
| `--flavor <name>` | Specify build flavor |
| `--help` | Show help message |

## 📱 APK Naming Convention

Generated APKs follow this naming pattern:
```
{app_name}_{version}_{build_type}_{flavor}.apk
```

Examples:
- `click_bazaar_1.0.1_release.apk`
- `click_bazaar_1.0.1_debug_staging.apk`

## 📤 Telegram Upload Format

The service uploads APKs with a formatted message including:

```
📱 **Click Bazaar v1.0.1**
📦 **Size:** 25.3 MB
🌿 **Branch:** main
📝 **Changelog:**
- New feature implementation
- Bug fixes and improvements

🔧 **Build:** release
⏰ **Built:** 2024-01-15 14:30:25 UTC
```

## 🔧 Setup Instructions

### 1. Telegram Bot Setup

1. Create a new bot with [@BotFather](https://t.me/botfather)
2. Get your bot token
3. Add the bot to your channel/group
4. Get the chat ID using [@userinfobot](https://t.me/userinfobot)

### 2. Environment Configuration

1. Copy `.env.example` to `.env`
2. Fill in your Telegram credentials
3. Configure build settings as needed

### 3. Permissions

Make the script executable:
```bash
chmod +x services/build_and_upload.sh
```

## 🌍 Cross-Platform Compatibility

This service works on:
- ✅ macOS
- ✅ Windows (Git Bash, WSL, or Cygwin)
- ✅ Linux

## 🔍 Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x services/build_and_upload.sh
   ```

2. **Flutter Not Found**
   - Ensure Flutter is in your PATH
   - Or set FLUTTER_ROOT in your .env file

3. **Large File Upload Fails**
   - Telegram has a 50MB limit for bot uploads
   - Consider using `--debug` for smaller APK size

4. **Build Fails**
   - Run `flutter doctor` to check your setup
   - Use `--clean` flag to clean build cache

### Debug Mode

Run with debug output:
```bash
DEBUG=1 ./services/build_and_upload.sh
```

## 📄 License

This service is designed to be portable and reusable across Flutter projects.
