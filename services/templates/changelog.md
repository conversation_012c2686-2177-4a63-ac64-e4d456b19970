# Changelog Template

This file serves as a template for generating changelog entries in your Telegram upload messages.

## Format

The service will automatically read your project's CHANGELOG.md file and include the latest entries in the upload message. If no CHANGELOG.md exists, you can create one using this template.

## Example CHANGELOG.md

```markdown
# Changelog

All notable changes to this project will be documented in this file.

## [1.0.1] - 2024-01-15

### Added
- New user authentication system
- Dark mode support
- Push notifications

### Changed
- Improved app performance
- Updated UI design
- Enhanced error handling

### Fixed
- Fixed crash on startup
- Resolved memory leak issues
- Fixed navigation bugs

## [1.0.0] - 2024-01-01

### Added
- Initial release
- Basic functionality
- User interface
```

## Automatic Changelog Detection

The service will:
1. Look for CHANGELOG.md in your project root
2. Extract the latest version entries
3. Include them in the Telegram upload message
4. Limit to the configured number of lines (default: 5)

## Custom Changelog Configuration

You can customize changelog behavior in your .env file:

```bash
# Custom changelog file path
CHANGELOG_FILE=docs/CHANGELOG.md

# Number of lines to include
CHANGELOG_LINES=10
```

## Git Commit Messages

If no CHANGELOG.md is found, the service can optionally use recent git commit messages as changelog entries.
