#!/bin/bash

# File Utilities for Build Service
# Handles file operations, validation, and management

# =============================================================================
# FILE VALIDATION FUNCTIONS
# =============================================================================

# Check if file exists and is readable
validate_file_exists() {
    local file_path="$1"
    local description="${2:-"File"}"
    
    if [[ ! -f "$file_path" ]]; then
        log_error "$description not found: $file_path"
        return 1
    fi
    
    if [[ ! -r "$file_path" ]]; then
        log_error "$description is not readable: $file_path"
        return 1
    fi
    
    return 0
}

# Check if directory exists and is accessible
validate_directory_exists() {
    local dir_path="$1"
    local description="${2:-"Directory"}"
    
    if [[ ! -d "$dir_path" ]]; then
        log_error "$description not found: $dir_path"
        return 1
    fi
    
    if [[ ! -r "$dir_path" ]]; then
        log_error "$description is not accessible: $dir_path"
        return 1
    fi
    
    return 0
}

# Validate file size
validate_file_size() {
    local file_path="$1"
    local max_size="$2"
    local description="${3:-"File"}"
    
    if ! validate_file_exists "$file_path" "$description"; then
        return 1
    fi
    
    local file_size
    file_size=$(get_file_size "$file_path")
    
    if [[ "$file_size" -gt "$max_size" ]]; then
        log_error "$description size ($(format_file_size "$file_size")) exceeds maximum ($(format_file_size "$max_size"))"
        return 1
    fi
    
    return 0
}

# =============================================================================
# FILE OPERATION FUNCTIONS
# =============================================================================

# Create directory if it doesn't exist
ensure_directory() {
    local dir_path="$1"
    
    if [[ ! -d "$dir_path" ]]; then
        if mkdir -p "$dir_path"; then
            log_debug "Created directory: $dir_path"
        else
            log_error "Failed to create directory: $dir_path"
            return 1
        fi
    fi
    
    return 0
}

# Copy file with validation
copy_file() {
    local source="$1"
    local destination="$2"
    local description="${3:-"File"}"
    
    if ! validate_file_exists "$source" "$description"; then
        return 1
    fi
    
    # Ensure destination directory exists
    local dest_dir
    dest_dir=$(dirname "$destination")
    if ! ensure_directory "$dest_dir"; then
        return 1
    fi
    
    if cp "$source" "$destination"; then
        log_debug "Copied $description: $source -> $destination"
        return 0
    else
        log_error "Failed to copy $description: $source -> $destination"
        return 1
    fi
}

# Move file with validation
move_file() {
    local source="$1"
    local destination="$2"
    local description="${3:-"File"}"
    
    if ! validate_file_exists "$source" "$description"; then
        return 1
    fi
    
    # Ensure destination directory exists
    local dest_dir
    dest_dir=$(dirname "$destination")
    if ! ensure_directory "$dest_dir"; then
        return 1
    fi
    
    if mv "$source" "$destination"; then
        log_debug "Moved $description: $source -> $destination"
        return 0
    else
        log_error "Failed to move $description: $source -> $destination"
        return 1
    fi
}

# Remove file safely
remove_file() {
    local file_path="$1"
    local description="${2:-"File"}"
    
    if [[ -f "$file_path" ]]; then
        if rm "$file_path"; then
            log_debug "Removed $description: $file_path"
            return 0
        else
            log_error "Failed to remove $description: $file_path"
            return 1
        fi
    else
        log_debug "$description does not exist: $file_path"
        return 0
    fi
}

# =============================================================================
# FILE SEARCH FUNCTIONS
# =============================================================================

# Find files by pattern
find_files_by_pattern() {
    local search_dir="$1"
    local pattern="$2"
    local max_depth="${3:-3}"
    
    if ! validate_directory_exists "$search_dir"; then
        return 1
    fi
    
    find "$search_dir" -maxdepth "$max_depth" -name "$pattern" -type f 2>/dev/null
}

# Find the most recent file matching pattern
find_most_recent_file() {
    local search_dir="$1"
    local pattern="$2"
    
    if ! validate_directory_exists "$search_dir"; then
        return 1
    fi
    
    find "$search_dir" -name "$pattern" -type f -printf '%T@ %p\n' 2>/dev/null | \
        sort -n | tail -1 | cut -d' ' -f2-
}

# =============================================================================
# FILE CONTENT FUNCTIONS
# =============================================================================

# Read file content safely
read_file_content() {
    local file_path="$1"
    local max_lines="${2:-1000}"
    
    if ! validate_file_exists "$file_path"; then
        return 1
    fi
    
    head -n "$max_lines" "$file_path" 2>/dev/null
}

# Write content to file safely
write_file_content() {
    local file_path="$1"
    local content="$2"
    local description="${3:-"File"}"
    
    # Ensure directory exists
    local dir_path
    dir_path=$(dirname "$file_path")
    if ! ensure_directory "$dir_path"; then
        return 1
    fi
    
    if echo "$content" > "$file_path"; then
        log_debug "Written $description: $file_path"
        return 0
    else
        log_error "Failed to write $description: $file_path"
        return 1
    fi
}

# Append content to file safely
append_file_content() {
    local file_path="$1"
    local content="$2"
    local description="${3:-"File"}"
    
    # Ensure directory exists
    local dir_path
    dir_path=$(dirname "$file_path")
    if ! ensure_directory "$dir_path"; then
        return 1
    fi
    
    if echo "$content" >> "$file_path"; then
        log_debug "Appended to $description: $file_path"
        return 0
    else
        log_error "Failed to append to $description: $file_path"
        return 1
    fi
}

# =============================================================================
# FILE INFORMATION FUNCTIONS
# =============================================================================

# Get file modification time
get_file_mtime() {
    local file_path="$1"
    
    if ! validate_file_exists "$file_path"; then
        return 1
    fi
    
    if [[ "$IS_MACOS" == "true" ]]; then
        stat -f%m "$file_path" 2>/dev/null
    else
        stat -c%Y "$file_path" 2>/dev/null
    fi
}

# Get file permissions
get_file_permissions() {
    local file_path="$1"
    
    if ! validate_file_exists "$file_path"; then
        return 1
    fi
    
    if [[ "$IS_MACOS" == "true" ]]; then
        stat -f%Mp%Lp "$file_path" 2>/dev/null
    else
        stat -c%a "$file_path" 2>/dev/null
    fi
}

# Check if file is executable
is_file_executable() {
    local file_path="$1"
    
    [[ -x "$file_path" ]]
}

# Make file executable
make_file_executable() {
    local file_path="$1"
    local description="${2:-"File"}"
    
    if ! validate_file_exists "$file_path" "$description"; then
        return 1
    fi
    
    if chmod +x "$file_path"; then
        log_debug "Made $description executable: $file_path"
        return 0
    else
        log_error "Failed to make $description executable: $file_path"
        return 1
    fi
}

# =============================================================================
# CLEANUP FUNCTIONS
# =============================================================================

# Clean up temporary files
cleanup_temp_files() {
    local temp_dir="${1:-/tmp}"
    local pattern="${2:-build_service_*}"
    local max_age_days="${3:-7}"
    
    log_info "🧹 Cleaning up temporary files..."
    
    # Find and remove old temporary files
    if command_exists find; then
        local count
        count=$(find "$temp_dir" -name "$pattern" -type f -mtime +"$max_age_days" 2>/dev/null | wc -l)
        
        if [[ "$count" -gt 0 ]]; then
            find "$temp_dir" -name "$pattern" -type f -mtime +"$max_age_days" -delete 2>/dev/null
            log_info "Cleaned up $count temporary files"
        else
            log_debug "No temporary files to clean up"
        fi
    fi
}

# Clean up build artifacts
cleanup_build_artifacts() {
    local project_root="$1"
    
    log_info "🧹 Cleaning up build artifacts..."
    
    local cleanup_paths=(
        "$project_root/build"
        "$project_root/.dart_tool/build"
        "$project_root/android/.gradle"
        "$project_root/android/app/build"
    )
    
    for path in "${cleanup_paths[@]}"; do
        if [[ -d "$path" ]]; then
            rm -rf "$path" 2>/dev/null || true
            log_debug "Cleaned up: $path"
        fi
    done
    
    log_success "Build artifacts cleaned up"
}

# =============================================================================
# BACKUP FUNCTIONS
# =============================================================================

# Create backup of file
backup_file() {
    local file_path="$1"
    local backup_suffix="${2:-".backup"}"
    local description="${3:-"File"}"
    
    if ! validate_file_exists "$file_path" "$description"; then
        return 1
    fi
    
    local backup_path="${file_path}${backup_suffix}"
    
    if copy_file "$file_path" "$backup_path" "$description backup"; then
        log_info "Created backup: $backup_path"
        return 0
    else
        return 1
    fi
}

# Restore file from backup
restore_file_from_backup() {
    local file_path="$1"
    local backup_suffix="${2:-".backup"}"
    local description="${3:-"File"}"
    
    local backup_path="${file_path}${backup_suffix}"
    
    if ! validate_file_exists "$backup_path" "$description backup"; then
        return 1
    fi
    
    if copy_file "$backup_path" "$file_path" "$description"; then
        log_info "Restored from backup: $file_path"
        return 0
    else
        return 1
    fi
}
