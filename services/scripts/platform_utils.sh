#!/bin/bash

# Platform Utilities for Cross-Platform Compatibility
# Handles differences between macOS, Windows, and Linux

# =============================================================================
# PLATFORM DETECTION
# =============================================================================

PLATFORM=""
IS_WINDOWS=false
IS_MACOS=false
IS_LINUX=false

detect_platform() {
    case "$(uname -s)" in
        Darwin*)
            PLATFORM="macOS"
            IS_MACOS=true
            ;;
        Linux*)
            PLATFORM="Linux"
            IS_LINUX=true
            ;;
        CYGWIN*|MINGW*|MSYS*)
            PLATFORM="Windows"
            IS_WINDOWS=true
            ;;
        *)
            PLATFORM="Unknown"
            # Use echo if log_warning is not available
            if declare -f log_warning > /dev/null; then
                log_warning "Unknown platform detected: $(uname -s)"
            else
                echo "⚠️  Unknown platform detected: $(uname -s)" >&2
            fi
            ;;
    esac
    
    # Use echo if log_debug is not available
    if declare -f log_debug > /dev/null; then
        log_debug "Platform detected: $PLATFORM"
    elif [[ "${DEBUG:-0}" == "1" ]]; then
        echo "🐛 DEBUG: Platform detected: $PLATFORM"
    fi
}

init_platform() {
    detect_platform
    
    # Set platform-specific configurations
    if [[ "$IS_WINDOWS" == "true" ]]; then
        # Windows-specific settings
        export MSYS_NO_PATHCONV=1  # Prevent path conversion in Git Bash
    fi
}

# =============================================================================
# FILE PATH UTILITIES
# =============================================================================

# Convert path to platform-specific format
normalize_path() {
    local path="$1"
    
    if [[ "$IS_WINDOWS" == "true" ]]; then
        # Convert forward slashes to backslashes for Windows
        echo "$path" | sed 's|/|\\|g'
    else
        echo "$path"
    fi
}

# Get absolute path in a cross-platform way
get_absolute_path() {
    local path="$1"
    
    if [[ "$IS_WINDOWS" == "true" ]]; then
        # Use realpath if available, otherwise use pwd
        if command -v realpath &> /dev/null; then
            realpath "$path" 2>/dev/null || (cd "$path" && pwd)
        else
            (cd "$path" && pwd)
        fi
    else
        realpath "$path" 2>/dev/null || (cd "$path" && pwd)
    fi
}

# =============================================================================
# COMMAND UTILITIES
# =============================================================================

# Check if a command exists
command_exists() {
    command -v "$1" &> /dev/null
}

# Get file size in bytes (cross-platform)
get_file_size() {
    local file="$1"
    
    if [[ "$IS_MACOS" == "true" ]]; then
        stat -f%z "$file" 2>/dev/null || echo "0"
    else
        stat -c%s "$file" 2>/dev/null || echo "0"
    fi
}

# Format file size for human reading
format_file_size() {
    local bytes="$1"
    
    if [[ "$bytes" -lt 1024 ]]; then
        echo "${bytes} B"
    elif [[ "$bytes" -lt 1048576 ]]; then
        echo "$(( bytes / 1024 )) KB"
    elif [[ "$bytes" -lt 1073741824 ]]; then
        echo "$(( bytes / 1048576 )) MB"
    else
        echo "$(( bytes / 1073741824 )) GB"
    fi
}

# Get current timestamp in ISO format
get_timestamp() {
    if [[ "$IS_MACOS" == "true" ]]; then
        date -u +"%Y-%m-%d %H:%M:%S UTC"
    else
        date -u +"%Y-%m-%d %H:%M:%S UTC"
    fi
}

# Get current timestamp for filenames (no spaces or special chars)
get_timestamp_filename() {
    if [[ "$IS_MACOS" == "true" ]]; then
        date -u +"%Y%m%d_%H%M%S"
    else
        date -u +"%Y%m%d_%H%M%S"
    fi
}

# =============================================================================
# PROCESS UTILITIES
# =============================================================================

# Kill process by name (cross-platform)
kill_process_by_name() {
    local process_name="$1"
    
    if [[ "$IS_WINDOWS" == "true" ]]; then
        taskkill //F //IM "$process_name" 2>/dev/null || true
    else
        pkill -f "$process_name" 2>/dev/null || true
    fi
}

# Check if process is running
is_process_running() {
    local process_name="$1"
    
    if [[ "$IS_WINDOWS" == "true" ]]; then
        tasklist //FI "IMAGENAME eq $process_name" 2>/dev/null | grep -q "$process_name"
    else
        pgrep -f "$process_name" > /dev/null 2>&1
    fi
}

# =============================================================================
# NETWORK UTILITIES
# =============================================================================

# Check internet connectivity
check_internet_connection() {
    local test_urls=("8.8.8.8" "1.1.1.1" "google.com")
    
    for url in "${test_urls[@]}"; do
        if ping -c 1 -W 3 "$url" &> /dev/null; then
            return 0
        fi
    done
    
    return 1
}

# Download file with progress (cross-platform)
download_file() {
    local url="$1"
    local output="$2"
    
    if command_exists curl; then
        curl -L --progress-bar -o "$output" "$url"
    elif command_exists wget; then
        wget --progress=bar -O "$output" "$url"
    else
        # Use echo if log_error is not available
        if declare -f log_error > /dev/null; then
            log_error "Neither curl nor wget found for downloading"
        else
            echo "❌ Neither curl nor wget found for downloading" >&2
        fi
        return 1
    fi
}

# =============================================================================
# TERMINAL UTILITIES
# =============================================================================

# Get terminal width
get_terminal_width() {
    if command_exists tput; then
        tput cols 2>/dev/null || echo "80"
    else
        echo "80"
    fi
}

# Print progress bar
print_progress_bar() {
    local current="$1"
    local total="$2"
    local width="${3:-50}"
    
    local percentage=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    printf "\r["
    printf "%*s" "$filled" | tr ' ' '='
    printf "%*s" "$empty" | tr ' ' '-'
    printf "] %d%%" "$percentage"
}

# Clear current line
clear_line() {
    printf "\r\033[K"
}

# =============================================================================
# ENVIRONMENT UTILITIES
# =============================================================================

# Set environment variable (cross-platform)
set_env_var() {
    local name="$1"
    local value="$2"
    
    export "$name"="$value"
}

# Get environment variable with default
get_env_var() {
    local name="$1"
    local default="$2"
    
    echo "${!name:-$default}"
}

# Check if running in CI environment
is_ci_environment() {
    [[ -n "$CI" || -n "$CONTINUOUS_INTEGRATION" || -n "$GITHUB_ACTIONS" || -n "$GITLAB_CI" || -n "$JENKINS_URL" ]]
}

# =============================================================================
# INITIALIZATION
# =============================================================================

# Initialize platform detection when script is sourced
detect_platform
