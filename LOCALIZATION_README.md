# Comprehensive Localization Implementation Guide

This document describes the complete localization implementation for the Click Bazaar Flutter application using the `easy_localization` package, including comprehensive error handling localization.

## Overview

The application now supports multiple languages with complete localization coverage:
- **Uzbek (uz)** - Default language
- **Russian (ru)** - Secondary language

## ✅ Comprehensive Localization Coverage

### 1. Error Handling Components (Fully Localized)
- **DioErrorHandler** - All HTTP error messages and network errors
- **ImageErrorHandler** - All image loading error messages
- **ErrorHandlerService** - Generic error handling
- **ErrorHandlerMixin** - BLoC error handling mixin

### 2. Dialog Components (Fully Localized)
- **Empty Square Dialog** - Status indicators and descriptions
- **Contact Dialogs** - Call buttons and contact messages
- **Payment Dialogs** - All payment-related text

### 3. UI Components (Fully Localized)
- **Form validation messages**
- **Button texts**
- **Toast and snackbar messages**
- **Loading and error states**
- **Navigation labels**
- **Profile pages**
- **Statistics pages**

## Implementation Details

### 1. Dependencies

The following dependency was already added to `pubspec.yaml`:
```yaml
dependencies:
  easy_localization: ^3.0.7+1
```

### 2. Translation Files

Translation files are located in `assets/translations/`:
- `uz.json` - Uzbek translations
- `ru.json` - Russian translations

### 3. Generated Files

The following files are auto-generated:
- `lib/translations/locale_keys.g.dart` - Contains all translation keys as constants
- `lib/translations/codegen_loader.g.dart` - Contains the translation data

### 4. Key Components

#### Language Service (`lib/core/services/language_service.dart`)
- Manages language selection and persistence using GetStorage
- Provides methods to get/set current language
- Handles app locale updates

#### Language Selection Dialog (`lib/core/widgets/language_selection_dialog.dart`)
- Universal dialog component for language selection
- Supports both Uzbek and Russian languages
- Persists language choice automatically

### 5. Configuration

#### Main App Configuration (`lib/main.dart`)
```dart
EasyLocalization(
  supportedLocales: const [Locale('uz'), Locale('ru')],
  path: 'assets/translations',
  fallbackLocale: const Locale('uz'),
  startLocale: Locale(savedLanguage),
  child: const ClickBazaarApp(),
)
```

#### MaterialApp Configuration
```dart
MaterialApp(
  locale: context.locale,
  supportedLocales: context.supportedLocales,
  localizationsDelegates: context.localizationsDelegates,
  // ... other properties
)
```

## Usage

### 1. Using Translation Keys

Replace hardcoded strings with translation keys:

```dart
// Before
Text('Ilovaga kirish')

// After
Text(LocaleKeys.auth_login_title.tr())
```

### 2. Language Selection

Use the universal language selection dialog:

```dart
showDialog(
  context: context,
  builder: (context) => LanguageSelectionDialog(
    onLanguageChanged: (languageCode) {
      // Handle language change
      setState(() {
        _selectedLanguage = languageCode;
      });
    },
  ),
);
```

### 3. Getting Current Language

```dart
String currentLanguage = LanguageService.getCurrentLanguage();
String displayName = LanguageService.getLanguageDisplayName(currentLanguage);
```

## Translation Key Structure

The translation keys are organized hierarchically:

```
app.*                 - App-level strings
auth.*               - Authentication related
navigation.*         - Navigation items
profile.*            - Profile page strings
language.*           - Language selection dialog
statistics.*         - Statistics page strings
payment.*            - Payment related strings
places.*             - Place/location related
common.*             - Common UI elements
errors.*             - Error messages (COMPREHENSIVE)
  ├── network_error, server_error, unknown_error
  ├── connection_timeout, send_timeout, receive_timeout
  ├── bad_request_detailed, unauthorized_detailed
  ├── forbidden_detailed, not_found_detailed
  ├── validation_error, too_many_requests
  ├── internal_server_detailed, bad_gateway
  ├── service_unavailable, generic_error
  └── network_check_failed, demo_login_error
image_errors.*        - Image loading errors (NEW)
  ├── unsupported_format, network_error
  ├── corrupted_image, image_not_found
  └── loading_error
dialogs.*            - Dialog-specific strings (NEW)
  ├── empty_square.* - Empty square dialog strings
  └── contact.* - Contact dialog strings
```

## Files Modified

### Core Files (Updated for Complete Localization)
- `lib/main.dart` - Added EasyLocalization configuration
- `lib/core/services/language_service.dart` - New language management service
- `lib/core/widgets/language_selection_dialog.dart` - New universal dialog

### Error Handling Services (Fully Localized)
- `lib/core/services/dio_error_handler.dart` - ✅ All error messages localized
- `lib/core/services/image_error_handler.dart` - ✅ All image error messages localized
- `lib/core/services/error_handler_service.dart` - ✅ Generic error handling localized
- `lib/core/mixins/error_handler_mixin.dart` - ✅ BLoC error handling localized

### Dialog Components (Fully Localized)
- `lib/features/nazoratchi/naz_tuzilma/dialogs/rastalar_empty_square_dialog.dart` - ✅ Localized
- `lib/features/nazoratchi/naz_tuzilma/dialogs/rastalar_square_dialog.dart` - ✅ Localized

### Authentication (Updated)
- `lib/features/auth/presentation/pages/login_page.dart` - ✅ Demo login errors localized

### Navigation
- `lib/main_naz_navigation_page.dart` - Updated navigation labels
- `lib/main_sot_navigation_page.dart` - Updated navigation labels

### Authentication
- `lib/features/auth/presentation/pages/login_page.dart` - Updated login strings

### Profile Pages
- `lib/features/nazoratchi/naz_profile/presentation/pages/naz_profile_page.dart`
- `lib/features/sotuvchi/sot_profile/presentation/pages/sot_profile_page.dart`
- `lib/features/sotuvchi/sot_profile/presentation/pages/sot_personal_info_page.dart`
- `lib/features/sotuvchi/sot_profile/presentation/pages/sot_settings_page.dart`

### Statistics
- `lib/features/nazoratchi/naz_statistics/presentation/pages/naz_statistics_page.dart`

### Payment Dialogs
- `lib/features/nazoratchi/naz_tuzilma/dialogs/cash_payment_dialog.dart`
- `lib/features/nazoratchi/naz_tuzilma/dialogs/payment_result_dialog.dart`

### Other Components
- `lib/core/widgets/custom_toast.dart`
- `lib/features/sotuvchi/sot_home/presentation/page/sot_home_page.dart`

## Comprehensive Testing

Enhanced test files have been created to verify complete localization:

### `test/localization_test.dart` - Comprehensive Tests
- ✅ Uzbek translations work correctly
- ✅ Russian translations work correctly
- ✅ Language switching functionality
- ✅ **DioErrorHandler localization tests** (NEW)
- ✅ **ImageErrorHandler localization tests** (NEW)
- ✅ **Dialog component localization tests** (NEW)
- ✅ **Error message parameter substitution tests** (NEW)

### `test/sotuvchi_localization_test.dart` - Seller-specific Tests
- ✅ Seller profile page translations
- ✅ Seller-specific UI components

## New Error Handling Features

### 1. Comprehensive Error Message Localization
All error handling components now use localized strings:

```dart
// Before (hardcoded)
return 'Ulanish vaqti tugadi. Iltimos, qayta urinib ko\'ring.';

// After (localized)
return LocaleKeys.errors_connection_timeout.tr();
```

### 2. Parameter Substitution Support
Error messages support dynamic parameter substitution:

```dart
// Usage with parameters
LocaleKeys.errors_generic_error.tr(namedArgs: {'error': error.toString()});
LocaleKeys.dialogs_empty_square_place_number.tr(namedArgs: {'number': '123'});
```

### 3. Image Error Handling
Comprehensive image loading error messages:

```dart
// Usage in image widgets
Text(ImageErrorHandler.getUserFriendlyMessage(error))
```

### 4. Dialog Localization
All dialog components use localized strings:

```dart
// Empty square dialog
Text(LocaleKeys.dialogs_empty_square_status_empty.tr())
Text(LocaleKeys.dialogs_empty_square_description.tr())
```

## Best Practices (Updated)

1. **Always use LocaleKeys**: Never use hardcoded strings in UI components
2. **Consistent naming**: Follow the established key naming convention
3. **Context usage**: Use `context.tr()` when context is available
4. **Parameter substitution**: Use `namedArgs` for dynamic content
5. **Error handling**: Use localized error messages in all error handlers
6. **Fallback handling**: Always provide fallback values for missing translations
7. **Testing**: Test all language variants and error scenarios before deployment

## Adding New Translations

1. Add the new key-value pair to both `uz.json` and `ru.json`
2. Regenerate the locale keys (if using code generation)
3. Use the new key in your UI components
4. Test both language variants

## Language Persistence

The selected language is automatically persisted using GetStorage with the key `'selected_language'`. The app will remember the user's language choice across app restarts.

## Future Enhancements

- Add English language support
- Implement RTL language support if needed
- Add pluralization support for complex translations
- Consider adding region-specific variants (e.g., uz_UZ, ru_RU)
