<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/facesdk.h</key>
		<data>
		iXedJdnTNjJdkav0lex0htXPBa8=
		</data>
		<key>Headers/facesdk_api.h</key>
		<data>
		nvq2BAwf5zwJ4Ztl7CQydAFcrZo=
		</data>
		<key>Info.plist</key>
		<data>
		fDY3c5UxWB5qjD9Cxy526l/nKwg=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		X8zglrv2quSgEMFeVf0TybJa6gA=
		</data>
		<key>detection.bin</key>
		<data>
		G52JYi47ACwak8HkEcpkm9zo1as=
		</data>
		<key>detection.param</key>
		<data>
		tHFm7ohczZG7KEtACVgRSjX3XIA=
		</data>
		<key>landmark.bin</key>
		<data>
		YwuxUSePlqnFEDLD95H35rgexRI=
		</data>
		<key>landmark.param</key>
		<data>
		HHZGt8b9GPLjgGplhK/7hcOW0Mo=
		</data>
		<key>liveness.bin</key>
		<data>
		qh1obYoGNe0LoyD5JBmUkdeYrEY=
		</data>
		<key>recognize.bin</key>
		<data>
		SViF2bgK4XlpoZCVL4WMJbPq6q8=
		</data>
		<key>recognize.param</key>
		<data>
		G2oZ25SbKg4KWk0aPL047vM/Rv0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/facesdk.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CEHWZwXGt6HTp0cWeGAWrhBufzotOw42QLbWl2FjbpE=
			</data>
		</dict>
		<key>Headers/facesdk_api.h</key>
		<dict>
			<key>hash2</key>
			<data>
			AzDRsIwSMWBPsvoOmXuBP3EefyILyncJlo/a6keydI0=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			MPLn3hXOxK25/iaEZAyvAIf2qaUvsHUOWkHsaZrg7LY=
			</data>
		</dict>
		<key>detection.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			aSBPVzW2w5vueXMHRoelxdPFfzE45CIHzJJfvcmNVVc=
			</data>
		</dict>
		<key>detection.param</key>
		<dict>
			<key>hash2</key>
			<data>
			yb/QW3dSgGun1LdCqss+oipjB11F0sE0NtewKSJibFo=
			</data>
		</dict>
		<key>landmark.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			q59zkpnecoyeYz1houDmoVK0+ROe2x8l/0dtpqDC9Ic=
			</data>
		</dict>
		<key>landmark.param</key>
		<dict>
			<key>hash2</key>
			<data>
			nkGOHyOPviHWfG8E67n+geJfRcw+yc7qGU4famEpZNc=
			</data>
		</dict>
		<key>liveness.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			3XjsZ9HffijptH6kD8YcgXR9DwsM2tDsYTNDvdErRkE=
			</data>
		</dict>
		<key>recognize.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			ljjc/uY3hbh6WrCw7Z+d/+k+SzcY5eobU33VJDpmG4c=
			</data>
		</dict>
		<key>recognize.param</key>
		<dict>
			<key>hash2</key>
			<data>
			kDIa3Db/MNtnTOFGFGQ1ZvrQfA8iAnM1hx/ZDYyci4A=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
