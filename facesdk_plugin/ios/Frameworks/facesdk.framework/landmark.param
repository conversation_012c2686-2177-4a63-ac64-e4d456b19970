7767517
15 15
Input            input            0 1 data 0=60 1=60 2=1
Convolution      Conv1            1 1 data Conv1 0=20 1=5 2=1 3=1 4=2 5=1 6=500
ReLU             ActivationReLU1  1 1 Conv1 ActivationReLU1
Pooling          Pool1            1 1 ActivationReLU1 Pool1 0=0 1=2 2=2 3=0 4=0
Convolution      Conv2            1 1 Pool1 Conv2 0=48 1=5 2=1 3=1 4=2 5=1 6=24000
ReLU             ActivationReLU2  1 1 Conv2 ActivationReLU2
Pooling          Pool2            1 1 ActivationReLU2 Pool2 0=0 1=2 2=2 3=0 4=0
Convolution      Conv3            1 1 Pool2 Conv3 0=64 1=3 2=1 3=1 4=0 5=1 6=27648
ReLU             ActivationReLU3  1 1 Conv3 ActivationReLU3
Pooling          Pool3            1 1 ActivationReLU3 Pool3 0=0 1=3 2=2 3=0 4=0
Convolution      Conv4            1 1 Pool3 Conv4 0=80 1=3 2=1 3=1 4=0 5=1 6=46080
ReLU             ActivationReLU4  1 1 Conv4 ActivationReLU4
InnerProduct     Dense1           1 1 ActivationReLU4 Dense1 0=512 1=1 2=655360
ReLU             ActivationReLU5  1 1 Dense1 ActivationReLU5
InnerProduct     Dense3           1 1 ActivationReLU5 Dense3 0=136 1=1 2=69632
